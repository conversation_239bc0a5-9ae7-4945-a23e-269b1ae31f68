<?php
// اختبار الاتصال بقاعدة البيانات
require_once "config/database.php";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS
    );
    
    echo "✓ الاتصال بقاعدة البيانات نجح\n";
    
    // اختبار جدول المستخدمين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "✓ عدد المستخدمين: " . $result["count"] . "\n";
    
} catch (Exception $e) {
    echo "✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
}
?>