<?php
/**
 * لوحة التحكم الرئيسية
 * Main Dashboard
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('dashboard.view');

// الحصول على الإحصائيات
$today = date('Y-m-d');
$this_month = date('Y-m');

// إحصائيات اليوم
$today_stats = array();
$today_stats['orders'] = DB::selectOne("SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = ?", array($today))['count'];
$today_stats['revenue'] = DB::selectOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE DATE(created_at) = ? AND status = 'completed'", array($today))['total'];
$today_stats['customers'] = DB::selectOne("SELECT COUNT(DISTINCT customer_id) as count FROM orders WHERE DATE(created_at) = ? AND customer_id IS NOT NULL", array($today))['count'];

// إحصائيات الشهر
$month_stats = array();
$month_stats['orders'] = DB::selectOne("SELECT COUNT(*) as count FROM orders WHERE DATE_FORMAT(created_at, '%Y-%m') = ?", array($this_month))['count'];
$month_stats['revenue'] = DB::selectOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE DATE_FORMAT(created_at, '%Y-%m') = ? AND status = 'completed'", array($this_month))['total'];

// إحصائيات عامة
$general_stats = array();
$general_stats['total_customers'] = DB::selectOne("SELECT COUNT(*) as count FROM customers")['count'];
$general_stats['total_products'] = DB::selectOne("SELECT COUNT(*) as count FROM products WHERE is_active = 1")['count'];
$general_stats['total_tables'] = DB::selectOne("SELECT COUNT(*) as count FROM tables WHERE is_active = 1")['count'];
$general_stats['active_users'] = DB::selectOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count'];

// حالة الطاولات
$tables_status = DB::select("SELECT status, COUNT(*) as count FROM tables WHERE is_active = 1 GROUP BY status");
$tables_by_status = array();
foreach ($tables_status as $status) {
    $tables_by_status[$status['status']] = $status['count'];
}

// الطلبات الحديثة
$recent_orders = DB::select("
    SELECT o.*, c.name as customer_name, t.table_number 
    FROM orders o 
    LEFT JOIN customers c ON o.customer_id = c.id 
    LEFT JOIN table_sessions ts ON o.table_session_id = ts.id
    LEFT JOIN tables t ON ts.table_id = t.id
    ORDER BY o.created_at DESC 
    LIMIT 10
");

// المنتجات الأكثر مبيعاً اليوم
$top_products = DB::select("
    SELECT p.name_ar, p.name, SUM(oi.quantity) as total_sold, SUM(oi.total_price) as total_revenue
    FROM order_items oi
    JOIN products p ON oi.product_id = p.id
    JOIN orders o ON oi.order_id = o.id
    WHERE DATE(o.created_at) = ? AND o.status = 'completed'
    GROUP BY p.id
    ORDER BY total_sold DESC
    LIMIT 5
", array($today));

// بيانات المبيعات لآخر 7 أيام
$sales_chart_data = array();
for ($i = 6; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $revenue = DB::selectOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE DATE(created_at) = ? AND status = 'completed'", array($date))['total'];
    $sales_chart_data[] = array(
        'date' => $date,
        'revenue' => floatval($revenue)
    );
}

$page_title = 'لوحة التحكم';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">لوحة التحكم</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>

            <!-- إحصائيات اليوم -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $today_stats['orders']; ?></h4>
                                    <p class="mb-0">طلبات اليوم</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-shopping-cart fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo formatCurrency($today_stats['revenue']); ?></h4>
                                    <p class="mb-0">مبيعات اليوم</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $today_stats['customers']; ?></h4>
                                    <p class="mb-0">عملاء اليوم</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo isset($tables_by_status['occupied']) ? $tables_by_status['occupied'] : 0; ?></h4>
                                    <p class="mb-0">طاولات مشغولة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chair fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- الرسم البياني للمبيعات -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">مبيعات آخر 7 أيام</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="salesChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- حالة الطاولات -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">حالة الطاولات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="tablesChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <!-- الطلبات الحديثة -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">الطلبات الحديثة</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_orders)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد طلبات</h5>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>رقم الطلب</th>
                                                <th>العميل</th>
                                                <th>الطاولة</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                                <th>الوقت</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td><strong>#<?php echo $order['order_number']; ?></strong></td>
                                                <td>
                                                    <?php if ($order['customer_name']): ?>
                                                        <?php echo htmlspecialchars($order['customer_name']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">زائر</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($order['table_number']): ?>
                                                        طاولة <?php echo $order['table_number']; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo formatCurrency($order['total_amount']); ?></td>
                                                <td>
                                                    <?php
                                                    $status_classes = array(
                                                        'pending' => 'bg-warning',
                                                        'confirmed' => 'bg-info',
                                                        'preparing' => 'bg-primary',
                                                        'ready' => 'bg-success',
                                                        'completed' => 'bg-success',
                                                        'cancelled' => 'bg-danger'
                                                    );
                                                    $status_names = array(
                                                        'pending' => 'في الانتظار',
                                                        'confirmed' => 'مؤكد',
                                                        'preparing' => 'قيد التحضير',
                                                        'ready' => 'جاهز',
                                                        'completed' => 'مكتمل',
                                                        'cancelled' => 'ملغي'
                                                    );
                                                    ?>
                                                    <span class="badge <?php echo isset($status_classes[$order['status']]) ? $status_classes[$order['status']] : 'bg-secondary'; ?>">
                                                        <?php echo isset($status_names[$order['status']]) ? $status_names[$order['status']] : $order['status']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo formatDateTime($order['created_at']); ?>
                                                    </small>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- المنتجات الأكثر مبيعاً -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">الأكثر مبيعاً اليوم</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($top_products)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد مبيعات اليوم</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($top_products as $index => $product): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($product['name_ar']); ?></h6>
                                        <small class="text-muted"><?php echo $product['total_sold']; ?> قطعة</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-primary">#<?php echo $index + 1; ?></span>
                                        <div><small><?php echo formatCurrency($product['total_revenue']); ?></small></div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات إضافية -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h4><?php echo $general_stats['total_customers']; ?></h4>
                            <p class="text-muted">إجمالي العملاء</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-utensils fa-2x text-success mb-2"></i>
                            <h4><?php echo $general_stats['total_products']; ?></h4>
                            <p class="text-muted">المنتجات النشطة</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-chair fa-2x text-info mb-2"></i>
                            <h4><?php echo $general_stats['total_tables']; ?></h4>
                            <p class="text-muted">إجمالي الطاولات</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-user-shield fa-2x text-warning mb-2"></i>
                            <h4><?php echo $general_stats['active_users']; ?></h4>
                            <p class="text-muted">المستخدمون النشطون</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
$additional_js = '
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// بيانات المبيعات
const salesData = ' . json_encode($sales_chart_data) . ';
const salesLabels = salesData.map(item => {
    const date = new Date(item.date);
    return date.toLocaleDateString("ar-SA", { month: "short", day: "numeric" });
});
const salesValues = salesData.map(item => item.revenue);

// رسم بياني للمبيعات
const salesCtx = document.getElementById("salesChart").getContext("2d");
new Chart(salesCtx, {
    type: "line",
    data: {
        labels: salesLabels,
        datasets: [{
            label: "المبيعات (ر.س)",
            data: salesValues,
            borderColor: "#007bff",
            backgroundColor: "rgba(0, 123, 255, 0.1)",
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + " ر.س";
                    }
                }
            }
        }
    }
});

// بيانات الطاولات
const tablesData = ' . json_encode($tables_by_status) . ';
const tablesLabels = [];
const tablesValues = [];
const tablesColors = [];

const statusNames = {
    "available": "متاحة",
    "occupied": "مشغولة", 
    "reserved": "محجوزة",
    "maintenance": "صيانة"
};

const statusColors = {
    "available": "#28a745",
    "occupied": "#dc3545",
    "reserved": "#ffc107", 
    "maintenance": "#6c757d"
};

for (const [status, count] of Object.entries(tablesData)) {
    tablesLabels.push(statusNames[status] || status);
    tablesValues.push(count);
    tablesColors.push(statusColors[status] || "#6c757d");
}

// رسم بياني دائري للطاولات
const tablesCtx = document.getElementById("tablesChart").getContext("2d");
new Chart(tablesCtx, {
    type: "doughnut",
    data: {
        labels: tablesLabels,
        datasets: [{
            data: tablesValues,
            backgroundColor: tablesColors,
            borderWidth: 2,
            borderColor: "#fff"
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: "bottom"
            }
        }
    }
});

// تحديث لوحة التحكم
function refreshDashboard() {
    location.reload();
}

// تحديث تلقائي كل 5 دقائق
setInterval(function() {
    if (!document.hidden) {
        refreshDashboard();
    }
}, 300000);
</script>
';

include '../layouts/footer.php';
?>
