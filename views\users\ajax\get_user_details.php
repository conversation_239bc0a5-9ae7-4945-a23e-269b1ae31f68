<?php
/**
 * تفاصيل المستخدم - AJAX
 * User Details - AJAX
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('users.view');

$user_id = isset($_POST['user_id']) ? $_POST['user_id'] : null;

if (!$user_id) {
    echo '<div class="alert alert-danger">معرف المستخدم مطلوب</div>';
    exit;
}

// الحصول على بيانات المستخدم
$sql = "SELECT u.*, r.name_ar as role_name, r.description as role_description
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        WHERE u.id = ?";
$user = DB::selectOne($sql, array($user_id));

if (!$user) {
    echo '<div class="alert alert-danger">المستخدم غير موجود</div>';
    exit;
}

// الحصول على إحصائيات المستخدم
$stats = array();

// عدد الطلبات التي أنشأها
$stats['orders_created'] = DB::selectOne("SELECT COUNT(*) as count FROM orders WHERE created_by = ?", array($user_id))['count'];

// عدد الأنشطة
$stats['activities'] = DB::selectOne("SELECT COUNT(*) as count FROM activity_log WHERE user_id = ?", array($user_id))['count'];

// آخر نشاط
$last_activity = DB::selectOne("SELECT * FROM activity_log WHERE user_id = ? ORDER BY created_at DESC LIMIT 1", array($user_id));

// عدد مرات تسجيل الدخول (تقريبي)
$login_count = DB::selectOne("SELECT COUNT(*) as count FROM activity_log WHERE user_id = ? AND action = 'login'", array($user_id))['count'];
?>

<div class="row">
    <div class="col-md-4">
        <div class="text-center mb-4">
            <?php if ($user['avatar']): ?>
                <img src="../../../uploads/avatars/<?php echo $user['avatar']; ?>" 
                     alt="صورة المستخدم" class="rounded-circle mb-3" width="120" height="120">
            <?php else: ?>
                <div class="avatar-placeholder-large mb-3">
                    <i class="fas fa-user fa-3x"></i>
                </div>
            <?php endif; ?>
            
            <h5><?php echo htmlspecialchars($user['full_name']); ?></h5>
            <p class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></p>
            
            <?php if ($user['is_active']): ?>
                <span class="badge bg-success">نشط</span>
            <?php else: ?>
                <span class="badge bg-danger">غير نشط</span>
            <?php endif; ?>
            
            <?php if ($user['id'] == $_SESSION['user_id']): ?>
                <span class="badge bg-primary">أنت</span>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="row">
            <div class="col-md-6">
                <h6>المعلومات الأساسية</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>الاسم الكامل:</strong></td>
                        <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>اسم المستخدم:</strong></td>
                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>البريد الإلكتروني:</strong></td>
                        <td>
                            <?php if ($user['email']): ?>
                                <a href="mailto:<?php echo $user['email']; ?>">
                                    <?php echo htmlspecialchars($user['email']); ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">غير محدد</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>رقم الهاتف:</strong></td>
                        <td>
                            <?php if ($user['phone']): ?>
                                <a href="tel:<?php echo $user['phone']; ?>">
                                    <?php echo htmlspecialchars($user['phone']); ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">غير محدد</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الدور:</strong></td>
                        <td>
                            <span class="badge bg-info">
                                <?php echo $user['role_name'] ? $user['role_name'] : 'غير محدد'; ?>
                            </span>
                            <?php if ($user['role_description']): ?>
                                <br><small class="text-muted"><?php echo $user['role_description']; ?></small>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="col-md-6">
                <h6>معلومات النشاط</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>تاريخ الإنشاء:</strong></td>
                        <td><?php echo formatDateTime($user['created_at']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>آخر تحديث:</strong></td>
                        <td><?php echo formatDateTime($user['updated_at']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>آخر نشاط:</strong></td>
                        <td>
                            <?php if ($user['last_activity']): ?>
                                <?php echo formatDateTime($user['last_activity']); ?>
                            <?php else: ?>
                                <span class="text-muted">لم يسجل دخول</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>محاولات الدخول الفاشلة:</strong></td>
                        <td>
                            <?php if ($user['failed_login_attempts'] > 0): ?>
                                <span class="badge bg-warning"><?php echo $user['failed_login_attempts']; ?></span>
                            <?php else: ?>
                                <span class="text-success">0</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php if ($user['locked_until']): ?>
                    <tr>
                        <td><strong>مقفل حتى:</strong></td>
                        <td>
                            <span class="badge bg-danger">
                                <?php echo formatDateTime($user['locked_until']); ?>
                            </span>
                        </td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <h6>الإحصائيات</h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $stats['orders_created']; ?></h4>
                                <small>الطلبات المُنشأة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $stats['activities']; ?></h4>
                                <small>إجمالي الأنشطة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $login_count; ?></h4>
                                <small>مرات تسجيل الدخول</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $user['failed_login_attempts']; ?></h4>
                                <small>محاولات فاشلة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if ($last_activity): ?>
        <div class="row mt-4">
            <div class="col-md-12">
                <h6>آخر نشاط</h6>
                <div class="alert alert-info">
                    <strong><?php echo $last_activity['action']; ?></strong>
                    <?php if ($last_activity['table_name']): ?>
                        في جدول <?php echo $last_activity['table_name']; ?>
                    <?php endif; ?>
                    <br>
                    <small class="text-muted">
                        <?php echo formatDateTime($last_activity['created_at']); ?>
                        <?php if ($last_activity['ip_address']): ?>
                            من IP: <?php echo $last_activity['ip_address']; ?>
                        <?php endif; ?>
                    </small>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<div class="modal-footer">
    <?php if (hasPermission('users.edit')): ?>
    <a href="../edit.php?id=<?php echo $user['id']; ?>" class="btn btn-primary">
        <i class="fas fa-edit"></i> تعديل المستخدم
    </a>
    <?php endif; ?>
    
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
</div>

<style>
.avatar-placeholder-large {
    width: 120px;
    height: 120px;
    background: #f8f9fa;
    border: 3px solid #dee2e6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    margin: 0 auto;
}

.table-sm td {
    padding: 0.25rem 0.5rem;
    border-top: 1px solid #dee2e6;
}

.card {
    margin-bottom: 1rem;
}

.card-body {
    padding: 1rem;
}
</style>
