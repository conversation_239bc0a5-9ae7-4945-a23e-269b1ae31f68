<?php
// تحديد الصفحة الحالية لتمييز الرابط النشط
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

function isActive($page, $dir = '') {
    global $current_page, $current_dir;
    if ($dir && $current_dir == $dir) {
        return 'active';
    }
    if ($current_page == $page) {
        return 'active';
    }
    return '';
}
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <!-- الصفحة الرئيسية -->
            <li class="nav-item">
                <a class="nav-link <?php echo isActive('index.php'); ?>" href="<?php echo SITE_URL; ?>/index.php">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
            </li>
            
            <!-- نقاط البيع -->
            <?php if (hasPermission('orders.create')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActive('index.php', 'pos'); ?>" href="<?php echo SITE_URL; ?>/views/pos/index.php">
                    <i class="fas fa-cash-register"></i>
                    نقاط البيع
                </a>
            </li>
            <?php endif; ?>
            
            <!-- إدارة الطلبات -->
            <?php if (hasPermission('orders.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActive('list.php', 'orders'); ?>" href="<?php echo SITE_URL; ?>/views/orders/list.php">
                    <i class="fas fa-shopping-cart"></i>
                    إدارة الطلبات
                </a>
            </li>
            <?php endif; ?>
            
            <!-- شاشة المطبخ -->
            <?php if (hasPermission('kitchen.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActive('display.php', 'kitchen'); ?>" href="<?php echo SITE_URL; ?>/views/kitchen/display.php">
                    <i class="fas fa-utensils"></i>
                    شاشة المطبخ
                </a>
            </li>
            <?php endif; ?>
            
            <!-- إدارة الطاولات -->
            <li class="nav-item">
                <a class="nav-link <?php echo isActive('list.php', 'tables'); ?>" href="<?php echo SITE_URL; ?>/views/tables/list.php">
                    <i class="fas fa-chair"></i>
                    إدارة الطاولات
                </a>
            </li>
            
            <!-- الحجوزات -->
            <li class="nav-item">
                <a class="nav-link <?php echo isActive('list.php', 'reservations'); ?>" href="<?php echo SITE_URL; ?>/views/reservations/list.php">
                    <i class="fas fa-calendar-check"></i>
                    الحجوزات
                </a>
            </li>
            
            <hr class="my-3">
            
            <!-- إدارة المنتجات -->
            <?php if (hasPermission('products.view')): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#productsMenu" role="button" 
                   aria-expanded="false" aria-controls="productsMenu">
                    <i class="fas fa-box"></i>
                    إدارة المنتجات
                    <i class="fas fa-chevron-down float-start mt-1"></i>
                </a>
                <div class="collapse" id="productsMenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('list.php', 'products'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/products/list.php">
                                <i class="fas fa-list"></i>
                                قائمة المنتجات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('list.php', 'categories'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/categories/list.php">
                                <i class="fas fa-tags"></i>
                                الفئات
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
            
            <!-- إدارة المخزون -->
            <?php if (hasPermission('inventory.view')): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#inventoryMenu" role="button" 
                   aria-expanded="false" aria-controls="inventoryMenu">
                    <i class="fas fa-warehouse"></i>
                    إدارة المخزون
                    <i class="fas fa-chevron-down float-start mt-1"></i>
                </a>
                <div class="collapse" id="inventoryMenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('list.php', 'ingredients'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/ingredients/list.php">
                                <i class="fas fa-list"></i>
                                المكونات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('list.php', 'suppliers'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/suppliers/list.php">
                                <i class="fas fa-truck"></i>
                                الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('list.php', 'purchases'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/purchases/list.php">
                                <i class="fas fa-shopping-bag"></i>
                                المشتريات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('stock.php', 'inventory'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/inventory/stock.php">
                                <i class="fas fa-boxes"></i>
                                حالة المخزون
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
            
            <!-- إدارة العملاء -->
            <?php if (hasPermission('customers.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActive('list.php', 'customers'); ?>" href="<?php echo SITE_URL; ?>/views/customers/list.php">
                    <i class="fas fa-users"></i>
                    إدارة العملاء
                </a>
            </li>
            <?php endif; ?>
            
            <hr class="my-3">
            
            <!-- التقارير -->
            <?php if (hasPermission('reports.sales') || hasPermission('reports.inventory') || hasPermission('reports.financial')): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#reportsMenu" role="button" 
                   aria-expanded="false" aria-controls="reportsMenu">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                    <i class="fas fa-chevron-down float-start mt-1"></i>
                </a>
                <div class="collapse" id="reportsMenu">
                    <ul class="nav flex-column ms-3">
                        <?php if (hasPermission('reports.sales')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('sales.php', 'reports'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/reports/sales.php">
                                <i class="fas fa-chart-line"></i>
                                تقارير المبيعات
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if (hasPermission('reports.inventory')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('inventory.php', 'reports'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/reports/inventory.php">
                                <i class="fas fa-boxes"></i>
                                تقارير المخزون
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if (hasPermission('reports.financial')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('financial.php', 'reports'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/reports/financial.php">
                                <i class="fas fa-money-bill-wave"></i>
                                التقارير المالية
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('customers.php', 'reports'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/reports/customers.php">
                                <i class="fas fa-users"></i>
                                تقارير العملاء
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
            
            <!-- الورديات -->
            <?php if (hasPermission('shifts.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActive('list.php', 'shifts'); ?>" href="<?php echo SITE_URL; ?>/views/shifts/list.php">
                    <i class="fas fa-clock"></i>
                    الورديات
                </a>
            </li>
            <?php endif; ?>
            
            <hr class="my-3">
            
            <!-- الإدارة -->
            <?php if (hasPermission('users.view') || hasPermission('settings.view')): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#adminMenu" role="button" 
                   aria-expanded="false" aria-controls="adminMenu">
                    <i class="fas fa-cogs"></i>
                    الإدارة
                    <i class="fas fa-chevron-down float-start mt-1"></i>
                </a>
                <div class="collapse" id="adminMenu">
                    <ul class="nav flex-column ms-3">
                        <?php if (hasPermission('users.view')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('list.php', 'users'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/users/list.php">
                                <i class="fas fa-user-cog"></i>
                                المستخدمين
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('list.php', 'chefs'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/chefs/list.php">
                                <i class="fas fa-chef-hat"></i>
                                الطهاة
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('list.php', 'promotions'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/promotions/list.php">
                                <i class="fas fa-percentage"></i>
                                العروض والخصومات
                            </a>
                        </li>
                        
                        <?php if (hasPermission('settings.view')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActive('index.php', 'settings'); ?>" 
                               href="<?php echo SITE_URL; ?>/views/settings/index.php">
                                <i class="fas fa-cog"></i>
                                إعدادات النظام
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
        </ul>
        
        <!-- معلومات سريعة -->
        <div class="mt-4 px-3">
            <div class="card bg-light">
                <div class="card-body p-3">
                    <h6 class="card-title mb-2">
                        <i class="fas fa-info-circle text-primary"></i>
                        معلومات سريعة
                    </h6>
                    <small class="text-muted">
                        <div class="mb-1">
                            <i class="fas fa-calendar"></i>
                            <?php echo formatDate(date('Y-m-d')); ?>
                        </div>
                        <div class="mb-1">
                            <i class="fas fa-clock"></i>
                            <span id="current-time"><?php echo date('H:i:s'); ?></span>
                        </div>
                        <?php if (isLoggedIn()): ?>
                        <div>
                            <i class="fas fa-user"></i>
                            <?php echo $_SESSION['full_name']; ?>
                        </div>
                        <?php endif; ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
// تحديث الوقت كل ثانية
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('current-time').textContent = timeString;
}

// تحديث الوقت كل ثانية
setInterval(updateTime, 1000);

// إبقاء القوائم المنسدلة مفتوحة حسب الصفحة الحالية
document.addEventListener('DOMContentLoaded', function() {
    const activeLink = document.querySelector('.nav-link.active');
    if (activeLink) {
        const parentCollapse = activeLink.closest('.collapse');
        if (parentCollapse) {
            parentCollapse.classList.add('show');
        }
    }
});
</script>
