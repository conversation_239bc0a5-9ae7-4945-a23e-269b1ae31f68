<?php
/**
 * تحديث حالة طلب المطبخ
 * Update Kitchen Order Status
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('kitchen.update');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

$order_id = $_POST['order_id'] ?? null;
$status = $_POST['status'] ?? '';

if (!$order_id || !$status) {
    echo json_encode(['success' => false, 'message' => 'معرف الطلب والحالة مطلوبان']);
    exit;
}

if (!in_array($status, ['pending', 'preparing', 'ready', 'served', 'cancelled'])) {
    echo json_encode(['success' => false, 'message' => 'حالة غير صحيحة']);
    exit;
}

try {
    DB::beginTransaction();
    
    // الحصول على معلومات طلب المطبخ
    $kitchen_order = DB::selectOne("SELECT * FROM kitchen_orders WHERE id = ?", [$order_id]);
    
    if (!$kitchen_order) {
        throw new Exception('طلب المطبخ غير موجود');
    }
    
    $old_status = $kitchen_order['status'];
    
    // تحديث حالة طلب المطبخ
    $update_fields = ['status = ?'];
    $update_params = [$status];
    
    // إضافة الطوابع الزمنية حسب الحالة
    switch ($status) {
        case 'preparing':
            $update_fields[] = 'preparation_started_at = NOW()';
            $update_fields[] = 'assigned_chef_id = ?';
            $update_params[] = $_SESSION['user_id']; // افتراض أن المستخدم الحالي هو الطباخ
            break;
            
        case 'ready':
            $update_fields[] = 'preparation_completed_at = NOW()';
            break;
    }
    
    $sql = "UPDATE kitchen_orders SET " . implode(', ', $update_fields) . " WHERE id = ?";
    $update_params[] = $order_id;
    
    if (!DB::execute($sql, $update_params)) {
        throw new Exception('فشل في تحديث طلب المطبخ');
    }
    
    // تحديث حالة عنصر الطلب الأصلي
    $sql = "UPDATE order_items SET status = ? WHERE id = ?";
    DB::execute($sql, [$status, $kitchen_order['order_item_id']]);
    
    // إذا كان الطلب جاهزاً أو ملغياً، تحديث حالة الطلب الرئيسي إذا لزم الأمر
    if ($status === 'ready' || $status === 'cancelled') {
        // التحقق من حالة جميع عناصر الطلب
        $order_item = DB::selectOne("SELECT order_id FROM order_items WHERE id = ?", 
                                   [$kitchen_order['order_item_id']]);
        
        if ($order_item) {
            $order_id_main = $order_item['order_id'];
            
            // الحصول على حالة جميع العناصر
            $items_status = DB::select("SELECT status FROM order_items WHERE order_id = ?", 
                                      [$order_id_main]);
            
            $all_ready = true;
            $all_cancelled = true;
            
            foreach ($items_status as $item) {
                if ($item['status'] !== 'ready' && $item['status'] !== 'served') {
                    $all_ready = false;
                }
                if ($item['status'] !== 'cancelled') {
                    $all_cancelled = false;
                }
            }
            
            // تحديث حالة الطلب الرئيسي
            if ($all_cancelled) {
                DB::execute("UPDATE orders SET status = 'cancelled' WHERE id = ?", [$order_id_main]);
            } elseif ($all_ready) {
                DB::execute("UPDATE orders SET status = 'ready' WHERE id = ?", [$order_id_main]);
            } else {
                DB::execute("UPDATE orders SET status = 'preparing' WHERE id = ?", [$order_id_main]);
            }
        }
    }
    
    DB::commit();
    
    // تسجيل النشاط
    logActivity('update_kitchen_order_status', 'kitchen_orders', $order_id, 
               ['status' => $old_status], ['status' => $status]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث حالة الطلب بنجاح'
    ]);
    
} catch (Exception $e) {
    DB::rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
