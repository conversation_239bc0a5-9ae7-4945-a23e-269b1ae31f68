<?php
/**
 * تقارير المبيعات
 * Sales Reports
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports.sales');

// معالجة الفلاتر
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-d');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'daily';

// تقرير المبيعات العام
$sales_sql = "SELECT 
                COUNT(*) as total_orders,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
                SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_revenue,
                SUM(CASE WHEN status = 'completed' THEN subtotal ELSE 0 END) as total_subtotal,
                SUM(CASE WHEN status = 'completed' THEN tax_amount ELSE 0 END) as total_tax,
                SUM(CASE WHEN status = 'completed' THEN service_charge ELSE 0 END) as total_service,
                AVG(CASE WHEN status = 'completed' THEN total_amount ELSE NULL END) as avg_order_value
              FROM orders 
              WHERE DATE(created_at) BETWEEN ? AND ?";

$sales_data = DB::selectOne($sales_sql, [$date_from, $date_to]);

// تقرير المبيعات حسب نوع الطلب
$order_types_sql = "SELECT 
                      order_type,
                      COUNT(*) as orders_count,
                      SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as revenue
                    FROM orders 
                    WHERE DATE(created_at) BETWEEN ? AND ? 
                    GROUP BY order_type";

$order_types_data = DB::select($order_types_sql, [$date_from, $date_to]);

// تقرير المبيعات حسب الفترة الزمنية
if ($report_type == 'hourly') {
    $period_sql = "SELECT 
                     HOUR(created_at) as period,
                     COUNT(*) as orders_count,
                     SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as revenue
                   FROM orders 
                   WHERE DATE(created_at) BETWEEN ? AND ?
                   GROUP BY HOUR(created_at)
                   ORDER BY period";
} elseif ($report_type == 'weekly') {
    $period_sql = "SELECT 
                     WEEK(created_at) as period,
                     COUNT(*) as orders_count,
                     SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as revenue
                   FROM orders 
                   WHERE DATE(created_at) BETWEEN ? AND ?
                   GROUP BY WEEK(created_at)
                   ORDER BY period";
} elseif ($report_type == 'monthly') {
    $period_sql = "SELECT 
                     MONTH(created_at) as period,
                     COUNT(*) as orders_count,
                     SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as revenue
                   FROM orders 
                   WHERE DATE(created_at) BETWEEN ? AND ?
                   GROUP BY MONTH(created_at)
                   ORDER BY period";
} else {
    $period_sql = "SELECT 
                     DATE(created_at) as period,
                     COUNT(*) as orders_count,
                     SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as revenue
                   FROM orders 
                   WHERE DATE(created_at) BETWEEN ? AND ?
                   GROUP BY DATE(created_at)
                   ORDER BY period";
}

$period_data = DB::select($period_sql, [$date_from, $date_to]);

// أفضل المنتجات مبيعاً
$top_products_sql = "SELECT 
                       p.name_ar,
                       SUM(oi.quantity) as total_quantity,
                       SUM(oi.total_price) as total_revenue
                     FROM order_items oi
                     JOIN products p ON oi.product_id = p.id
                     JOIN orders o ON oi.order_id = o.id
                     WHERE o.status = 'completed' 
                     AND DATE(o.created_at) BETWEEN ? AND ?
                     GROUP BY p.id
                     ORDER BY total_quantity DESC
                     LIMIT 10";

$top_products = DB::select($top_products_sql, [$date_from, $date_to]);

// أفضل العملاء
$top_customers_sql = "SELECT 
                        c.name,
                        c.phone,
                        COUNT(o.id) as orders_count,
                        SUM(o.total_amount) as total_spent
                      FROM customers c
                      JOIN orders o ON c.id = o.customer_id
                      WHERE o.status = 'completed' 
                      AND DATE(o.created_at) BETWEEN ? AND ?
                      GROUP BY c.id
                      ORDER BY total_spent DESC
                      LIMIT 10";

$top_customers = DB::select($top_customers_sql, [$date_from, $date_to]);

$page_title = 'تقارير المبيعات';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تقارير المبيعات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="printReport()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="exportReport()">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                    </div>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع التقرير</label>
                            <select class="form-select" name="report_type">
                                <option value="daily" <?php echo $report_type == 'daily' ? 'selected' : ''; ?>>يومي</option>
                                <option value="hourly" <?php echo $report_type == 'hourly' ? 'selected' : ''; ?>>ساعي</option>
                                <option value="weekly" <?php echo $report_type == 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                                <option value="monthly" <?php echo $report_type == 'monthly' ? 'selected' : ''; ?>>شهري</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-chart-bar"></i> إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- ملخص المبيعات -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي المبيعات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo formatCurrency($sales_data['total_revenue']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        عدد الطلبات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($sales_data['completed_orders']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        متوسط قيمة الطلب
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo formatCurrency($sales_data['avg_order_value']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        الطلبات الملغية
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($sales_data['cancelled_orders']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- رسم بياني للمبيعات -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">اتجاه المبيعات</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="salesChart" width="100%" height="40"></canvas>
                        </div>
                    </div>
                </div>

                <!-- المبيعات حسب نوع الطلب -->
                <div class="col-lg-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">المبيعات حسب نوع الطلب</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="orderTypesChart" width="100%" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- أفضل المنتجات -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">أفضل المنتجات مبيعاً</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>المبيعات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($top_products as $product): ?>
                                        <tr>
                                            <td><?php echo $product['name_ar']; ?></td>
                                            <td><span class="badge bg-info"><?php echo $product['total_quantity']; ?></span></td>
                                            <td><strong><?php echo formatCurrency($product['total_revenue']); ?></strong></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أفضل العملاء -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">أفضل العملاء</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>العميل</th>
                                            <th>الطلبات</th>
                                            <th>الإنفاق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($top_customers as $customer): ?>
                                        <tr>
                                            <td>
                                                <div><?php echo $customer['name']; ?></div>
                                                <small class="text-muted"><?php echo $customer['phone']; ?></small>
                                            </td>
                                            <td><span class="badge bg-primary"><?php echo $customer['orders_count']; ?></span></td>
                                            <td><strong><?php echo formatCurrency($customer['total_spent']); ?></strong></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل إضافية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">تفاصيل المبيعات</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="border rounded p-3 text-center">
                                <h5 class="text-primary"><?php echo formatCurrency($sales_data['total_subtotal']); ?></h5>
                                <small class="text-muted">المجموع الفرعي</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 text-center">
                                <h5 class="text-success"><?php echo formatCurrency($sales_data['total_tax']); ?></h5>
                                <small class="text-muted">الضرائب</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 text-center">
                                <h5 class="text-info"><?php echo formatCurrency($sales_data['total_service']); ?></h5>
                                <small class="text-muted">رسوم الخدمة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
$additional_js = '
<script>
// بيانات الرسوم البيانية
const periodData = ' . json_encode($period_data) . ';
const orderTypesData = ' . json_encode($order_types_data) . ';

// رسم بياني للمبيعات
const salesCtx = document.getElementById("salesChart").getContext("2d");
const salesChart = new Chart(salesCtx, {
    type: "line",
    data: {
        labels: periodData.map(item => item.period),
        datasets: [{
            label: "المبيعات",
            data: periodData.map(item => item.revenue),
            borderColor: "#4e73df",
            backgroundColor: "rgba(78, 115, 223, 0.1)",
            borderWidth: 2,
            fill: true
        }, {
            label: "عدد الطلبات",
            data: periodData.map(item => item.orders_count),
            borderColor: "#1cc88a",
            backgroundColor: "rgba(28, 200, 138, 0.1)",
            borderWidth: 2,
            fill: true,
            yAxisID: "y1"
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                position: "left"
            },
            y1: {
                type: "linear",
                display: true,
                position: "right",
                beginAtZero: true,
                grid: {
                    drawOnChartArea: false
                }
            }
        }
    }
});

// رسم بياني دائري لأنواع الطلبات
const orderTypesCtx = document.getElementById("orderTypesChart").getContext("2d");
const orderTypesChart = new Chart(orderTypesCtx, {
    type: "doughnut",
    data: {
        labels: orderTypesData.map(item => {
            const types = {"dine_in": "طاولة", "takeaway": "سفري", "delivery": "توصيل"};
            return types[item.order_type] || item.order_type;
        }),
        datasets: [{
            data: orderTypesData.map(item => item.revenue),
            backgroundColor: ["#4e73df", "#1cc88a", "#36b9cc"],
            hoverBackgroundColor: ["#2e59d9", "#17a673", "#2c9faf"],
            hoverBorderColor: "rgba(234, 236, 244, 1)"
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: "bottom"
            }
        }
    }
});

function printReport() {
    window.print();
}

function exportReport() {
    const params = new URLSearchParams(window.location.search);
    window.open("export.php?type=sales&" + params.toString(), "_blank");
}
</script>
';

include '../layouts/footer.php';
?>
