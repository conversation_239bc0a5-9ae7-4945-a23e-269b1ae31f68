-- تحديثات قاعدة البيانات
-- Database Updates
-- Restaurant Management System

-- إضافة فهارس لتحسين الأداء
-- Adding indexes for better performance

-- فهارس جدول الطلبات
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_customer ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_table_session ON orders(table_session_id);
CREATE INDEX IF NOT EXISTS idx_orders_type ON orders(order_type);

-- فهارس جدول عناصر الطلبات
CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product ON order_items(product_id);

-- فهارس جدول العملاء
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_active ON customers(is_active);

-- فهارس جدول المنتجات
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_products_sort ON products(sort_order);

-- فهارس جدول الطاولات
CREATE INDEX IF NOT EXISTS idx_tables_status ON tables(status);
CREATE INDEX IF NOT EXISTS idx_tables_number ON tables(table_number);

-- فهارس جدول جلسات الطاولات
CREATE INDEX IF NOT EXISTS idx_table_sessions_table ON table_sessions(table_id);
CREATE INDEX IF NOT EXISTS idx_table_sessions_status ON table_sessions(status);
CREATE INDEX IF NOT EXISTS idx_table_sessions_date ON table_sessions(start_time);

-- فهارس جدول المدفوعات
CREATE INDEX IF NOT EXISTS idx_payments_order ON payments(order_id);
CREATE INDEX IF NOT EXISTS idx_payments_method ON payments(payment_method);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(created_at);

-- فهارس جدول المستخدمين
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role_id);

-- فهارس جدول سجل الأنشطة
CREATE INDEX IF NOT EXISTS idx_activity_log_user ON activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_log_action ON activity_log(action);
CREATE INDEX IF NOT EXISTS idx_activity_log_table ON activity_log(table_name);
CREATE INDEX IF NOT EXISTS idx_activity_log_date ON activity_log(created_at);

-- فهارس جدول المكونات
CREATE INDEX IF NOT EXISTS idx_ingredients_active ON ingredients(is_active);
CREATE INDEX IF NOT EXISTS idx_ingredients_stock ON ingredients(current_stock);

-- إضافة قيود خارجية إضافية
-- Adding additional foreign key constraints

-- قيود جدول عناصر الطلبات
ALTER TABLE order_items 
ADD CONSTRAINT fk_order_items_order 
FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE;

ALTER TABLE order_items 
ADD CONSTRAINT fk_order_items_product 
FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT;

-- قيود جدول الطلبات
ALTER TABLE orders 
ADD CONSTRAINT fk_orders_customer 
FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL;

ALTER TABLE orders 
ADD CONSTRAINT fk_orders_table_session 
FOREIGN KEY (table_session_id) REFERENCES table_sessions(id) ON DELETE SET NULL;

-- قيود جدول جلسات الطاولات
ALTER TABLE table_sessions 
ADD CONSTRAINT fk_table_sessions_table 
FOREIGN KEY (table_id) REFERENCES tables(id) ON DELETE CASCADE;

ALTER TABLE table_sessions 
ADD CONSTRAINT fk_table_sessions_customer 
FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL;

-- قيود جدول المدفوعات
ALTER TABLE payments 
ADD CONSTRAINT fk_payments_order 
FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE;

-- قيود جدول المنتجات
ALTER TABLE products 
ADD CONSTRAINT fk_products_category 
FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT;

-- إضافة أعمدة جديدة إذا لم تكن موجودة
-- Adding new columns if they don't exist

-- إضافة عمود آخر نشاط للمستخدمين
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP NULL DEFAULT NULL;

-- إضافة عمود عدد محاولات تسجيل الدخول الفاشلة
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS failed_login_attempts INT DEFAULT 0;

-- إضافة عمود وقت قفل الحساب
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP NULL DEFAULT NULL;

-- إضافة عمود رمز إعادة تعيين كلمة المرور
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255) NULL DEFAULT NULL;

-- إضافة عمود انتهاء صلاحية رمز إعادة التعيين
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMP NULL DEFAULT NULL;

-- إضافة عمود الملاحظات للطلبات
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS notes TEXT NULL DEFAULT NULL;

-- إضافة عمود وقت التسليم المتوقع
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS estimated_delivery_time TIMESTAMP NULL DEFAULT NULL;

-- إضافة عمود تقييم الطلب
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS rating TINYINT NULL DEFAULT NULL;

-- إضافة عمود تعليق العميل
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS customer_feedback TEXT NULL DEFAULT NULL;

-- إضافة عمود الخصم للعملاء
ALTER TABLE customers 
ADD COLUMN IF NOT EXISTS discount_percentage DECIMAL(5,2) DEFAULT 0.00;

-- إضافة عمود تاريخ انتهاء نقاط الولاء
ALTER TABLE customers 
ADD COLUMN IF NOT EXISTS loyalty_points_expiry DATE NULL DEFAULT NULL;

-- إضافة عمود الحد الأدنى للمخزون
ALTER TABLE ingredients 
ADD COLUMN IF NOT EXISTS minimum_stock DECIMAL(10,3) DEFAULT 0.000;

-- إضافة عمود الحد الأقصى للمخزون
ALTER TABLE ingredients 
ADD COLUMN IF NOT EXISTS maximum_stock DECIMAL(10,3) DEFAULT 0.000;

-- إضافة عمود تاريخ انتهاء الصلاحية
ALTER TABLE ingredients 
ADD COLUMN IF NOT EXISTS expiry_date DATE NULL DEFAULT NULL;

-- إضافة عمود المورد
ALTER TABLE ingredients 
ADD COLUMN IF NOT EXISTS supplier_id INT NULL DEFAULT NULL;

-- إضافة جدول الموردين إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    email VARCHAR(255) NULL,
    address TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إضافة جدول المشتريات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS purchases (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supplier_id INT NULL,
    purchase_number VARCHAR(50) UNIQUE NOT NULL,
    purchase_date DATE NOT NULL,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('pending', 'received', 'cancelled') DEFAULT 'pending',
    notes TEXT NULL,
    created_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- إضافة جدول عناصر المشتريات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS purchase_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchase_id INT NOT NULL,
    ingredient_id INT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE,
    FOREIGN KEY (ingredient_id) REFERENCES ingredients(id) ON DELETE RESTRICT
);

-- إضافة جدول طابعات المطبخ إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS kitchen_printers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name VARCHAR(255) NULL,
    ip_address VARCHAR(15) NULL,
    port INT DEFAULT 9100,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إضافة جدول الورديات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS shifts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NULL,
    opening_cash DECIMAL(10,2) DEFAULT 0.00,
    closing_cash DECIMAL(10,2) NULL,
    total_sales DECIMAL(10,2) DEFAULT 0.00,
    total_orders INT DEFAULT 0,
    notes TEXT NULL,
    status ENUM('active', 'closed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
);

-- تحديث البيانات الافتراضية
-- Update default data

-- إضافة إعدادات افتراضية جديدة
INSERT IGNORE INTO settings (setting_key, setting_value) VALUES
('system_version', '1.0.0'),
('last_backup', NULL),
('auto_backup_enabled', '0'),
('backup_retention_days', '30'),
('low_stock_alert', '1'),
('expiry_alert_enabled', '1'),
('expiry_alert_days', '7'),
('kitchen_display_refresh', '30'),
('pos_auto_print', '1'),
('receipt_logo', ''),
('receipt_footer_text', 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى'),
('loyalty_welcome_points', '100'),
('loyalty_birthday_points', '50'),
('max_discount_percentage', '50'),
('min_order_amount', '0'),
('delivery_charge', '0'),
('free_delivery_threshold', '100');

-- تحديث كلمات المرور لاستخدام التشفير الجديد
UPDATE users SET password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' 
WHERE username = 'admin' AND password = 'admin123';

-- إضافة مستخدم تجريبي للمطبخ
INSERT IGNORE INTO users (username, password, full_name, email, role_id, is_active) VALUES
('kitchen', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مستخدم المطبخ', '<EMAIL>', 4, 1);

-- إضافة مستخدم تجريبي للكاشير
INSERT IGNORE INTO users (username, password, full_name, email, role_id, is_active) VALUES
('cashier', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مستخدم الكاشير', '<EMAIL>', 3, 1);

-- تحديث الصلاحيات
UPDATE roles SET permissions = JSON_ARRAY(
    'dashboard.view', 'orders.view', 'orders.create', 'orders.edit', 
    'customers.view', 'customers.create', 'customers.edit',
    'products.view', 'tables.view', 'reports.sales'
) WHERE name = 'cashier';

UPDATE roles SET permissions = JSON_ARRAY(
    'kitchen.view', 'orders.view', 'orders.edit'
) WHERE name = 'kitchen';

-- تنظيف البيانات القديمة
-- Clean up old data

-- حذف الطلبات الملغية القديمة (أكثر من 6 أشهر)
DELETE FROM orders 
WHERE status = 'cancelled' 
AND created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);

-- حذف سجل الأنشطة القديم (أكثر من سنة)
DELETE FROM activity_log 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- تحديث إحصائيات الجداول
ANALYZE TABLE orders, order_items, customers, products, tables;

-- إنشاء views مفيدة
-- Create useful views

-- عرض إحصائيات المبيعات اليومية
CREATE OR REPLACE VIEW daily_sales AS
SELECT 
    DATE(created_at) as sale_date,
    COUNT(*) as total_orders,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as average_order_value
FROM orders 
WHERE status = 'completed'
GROUP BY DATE(created_at)
ORDER BY sale_date DESC;

-- عرض المنتجات الأكثر مبيعاً
CREATE OR REPLACE VIEW top_selling_products AS
SELECT 
    p.id,
    p.name_ar,
    p.name,
    SUM(oi.quantity) as total_sold,
    SUM(oi.total_price) as total_revenue
FROM products p
JOIN order_items oi ON p.id = oi.product_id
JOIN orders o ON oi.order_id = o.id
WHERE o.status = 'completed'
GROUP BY p.id
ORDER BY total_sold DESC;

-- عرض العملاء الأكثر إنفاقاً
CREATE OR REPLACE VIEW top_customers AS
SELECT 
    c.id,
    c.name,
    c.phone,
    COUNT(o.id) as total_orders,
    SUM(o.total_amount) as total_spent,
    AVG(o.total_amount) as average_order_value
FROM customers c
JOIN orders o ON c.id = o.customer_id
WHERE o.status = 'completed'
GROUP BY c.id
ORDER BY total_spent DESC;

-- إنشاء stored procedures مفيدة
-- Create useful stored procedures

DELIMITER //

-- إجراء لحساب إحصائيات العميل
CREATE OR REPLACE PROCEDURE UpdateCustomerStats(IN customer_id INT)
BEGIN
    UPDATE customers c
    SET 
        total_visits = (
            SELECT COUNT(*) 
            FROM orders o 
            WHERE o.customer_id = c.id AND o.status = 'completed'
        ),
        total_spent = (
            SELECT COALESCE(SUM(total_amount), 0) 
            FROM orders o 
            WHERE o.customer_id = c.id AND o.status = 'completed'
        ),
        average_order_value = (
            SELECT COALESCE(AVG(total_amount), 0) 
            FROM orders o 
            WHERE o.customer_id = c.id AND o.status = 'completed'
        ),
        last_visit = (
            SELECT MAX(created_at) 
            FROM orders o 
            WHERE o.customer_id = c.id AND o.status = 'completed'
        )
    WHERE c.id = customer_id;
END //

DELIMITER ;

-- تحديث رقم الإصدار
UPDATE settings SET setting_value = '1.0.0' WHERE setting_key = 'system_version';

-- إضافة تعليق على اكتمال التحديث
INSERT INTO activity_log (user_id, action, table_name, record_id, new_data, created_at) 
VALUES (1, 'system_update', 'system', NULL, '{"version": "1.0.0", "update_date": "' + NOW() + '"}', NOW());
