# Restaurant Management System - Apache Configuration
# نظام إدارة المطعم - إعدادات Apache المحدثة

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

<Files "*.backup">
    Order allow,deny
    Deny from all
</Files>

# منع الوصول لمجلدات النظام
<IfModule mod_rewrite.c>
    RewriteRule ^config/ - [F,L]
    RewriteRule ^includes/ - [F,L]
    RewriteRule ^models/ - [F,L]
    RewriteRule ^logs/ - [F,L]
    RewriteRule ^temp/ - [F,L]
    RewriteRule ^backups/ - [F,L]
</IfModule>

# منع الوصول للملفات الخاصة
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع عرض محتويات المجلدات
Options -Indexes

# تحسين الأمان
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options DENY
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # تفعيل حماية XSS
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
    
    # تحسين التخزين المؤقت للملفات الثابتة
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000"
        Header set Expires "Thu, 31 Dec 2025 23:59:59 GMT"
    </FilesMatch>
    
    # منع التخزين المؤقت للملفات الديناميكية
    <FilesMatch "\.(php|html)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# تحسين انتهاء صلاحية الملفات
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/eot "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# منع الهجمات الشائعة
<IfModule mod_rewrite.c>
    # منع محاولات SQL Injection
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # منع محاولات الوصول للملفات الحساسة
    RewriteCond %{REQUEST_URI} (wp-config\.php|configuration\.php|config\.php) [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # منع User Agents المشبوهة
    RewriteCond %{HTTP_USER_AGENT} (libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%22|%27|%28|%3C|%3E|%00).*(libwww-perl|wget|python|nikto|curl|scan|java|winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# تحديد حجم الملفات المرفوعة
<IfModule mod_php5.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
</IfModule>

<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
</IfModule>

# منع الوصول لملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|orig|original|tmp|temp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع الوصول المباشر لملفات PHP في مجلدات معينة
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_URI} ^/(uploads|temp|logs)/.*\.php$ [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# صفحات الخطأ المخصصة
ErrorDocument 403 /error.php?code=403
ErrorDocument 404 /error.php?code=404
ErrorDocument 500 /error.php?code=500

# تحسين الأمان للجلسات
<IfModule mod_php5.c>
    php_flag session.cookie_httponly On
    php_flag session.cookie_secure Off
    php_value session.cookie_lifetime 0
</IfModule>

# منع الوصول لملفات Git
<IfModule mod_rewrite.c>
    RewriteRule ^\.git - [F,L]
</IfModule>
