# Restaurant Management System - Apache Configuration
# نظام إدارة المطعم - إعدادات Apache

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "config/database.php">
    Order Allow,<PERSON>y
    Deny from all
</Files>

<Files "includes/functions.php">
    Order Allow,<PERSON>y
    Deny from all
</Files>

<FilesMatch "\.(sql|log|txt)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول للمجلدات الحساسة
RedirectMatch 403 ^/logs/.*$
RedirectMatch 403 ^/config/.*$
RedirectMatch 403 ^/includes/.*$

# حماية من الهجمات الشائعة
<IfModule mod_rewrite.c>
    # منع SQL Injection
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER) [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # منع محاولات الوصول للملفات المخفية
    RewriteCond %{REQUEST_URI} (^|/)\.
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    # منع clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # تفعيل XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
    
    # Content Security Policy
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self';"
</IfModule>

# منع عرض محتويات المجلدات
Options -Indexes

# تحديد صفحات الخطأ المخصصة
ErrorDocument 403 /error/403.php
ErrorDocument 404 /error/404.php
ErrorDocument 500 /error/500.php

# إعدادات PHP
<IfModule mod_php5.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
    php_value session.gc_maxlifetime 3600
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_flag session.use_strict_mode On
</IfModule>

# إعدادات للـ PHP 7+
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
    php_value session.gc_maxlifetime 3600
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_flag session.use_strict_mode On
</IfModule>

# إعادة توجيه HTTPS (اختياري - قم بإلغاء التعليق إذا كان لديك SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه www (اختياري)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ http://%1/$1 [R=301,L]

# منع الوصول المباشر للملفات الحساسة
<FilesMatch "^(composer\.(json|lock)|package\.(json|lock)|\.env|\.git.*|\.htaccess|\.htpasswd)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# تحسين الأداء - إزالة ETags
FileETag None

# ضبط المنطقة الزمنية (اختياري)
# SetEnv TZ Asia/Riyadh
