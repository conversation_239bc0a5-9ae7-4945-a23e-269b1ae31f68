-- نظام إدارة المطعم - قاعدة البيانات
-- Restaurant Management System Database
-- PHP 5.6.26 & MySQL Compatible

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `restaurant_system` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `restaurant_system`;

-- =============================================
-- جدول الأدوار والصلاحيات
-- =============================================

-- جدول الأدوار
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `name_ar` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول الصلاحيات
CREATE TABLE `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_ar` varchar(150) NOT NULL,
  `module` varchar(50) NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول ربط الأدوار بالصلاحيات
CREATE TABLE `role_permissions` (
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول المستخدمين
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100),
  `phone` varchar(20),
  `role_id` int(11) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- =============================================
-- جداول الفئات والمنتجات
-- =============================================

-- جدول الفئات
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_ar` varchar(150) NOT NULL,
  `description` text,
  `image` varchar(255),
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول طابعات المطبخ
CREATE TABLE `kitchen_printers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_ar` varchar(150) NOT NULL,
  `ip_address` varchar(45),
  `port` int(11) DEFAULT 9100,
  `printer_type` enum('thermal','laser','display') NOT NULL DEFAULT 'thermal',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول المنتجات
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_ar` varchar(150) NOT NULL,
  `description` text,
  `image` varchar(255),
  `category_id` int(11) NOT NULL,
  `kitchen_printer_id` int(11),
  `base_price` decimal(10,2) NOT NULL,
  `cost_price` decimal(10,2) DEFAULT 0.00,
  `is_recipe_based` tinyint(1) NOT NULL DEFAULT 0,
  `is_stock_item` tinyint(1) NOT NULL DEFAULT 0,
  `preparation_time` int(11) DEFAULT 0,
  `calories` int(11) DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `kitchen_printer_id` (`kitchen_printer_id`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`),
  CONSTRAINT `products_ibfk_2` FOREIGN KEY (`kitchen_printer_id`) REFERENCES `kitchen_printers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول أحجام المنتجات
CREATE TABLE `product_sizes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `name_ar` varchar(100) NOT NULL,
  `price_modifier` decimal(10,2) NOT NULL DEFAULT 0.00,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `product_sizes_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول إضافات المنتجات
CREATE TABLE `product_addons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `name_ar` varchar(150) NOT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_required` tinyint(1) NOT NULL DEFAULT 0,
  `max_quantity` int(11) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `product_addons_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- =============================================
-- جداول المخزون والمكونات
-- =============================================

-- جدول المكونات
CREATE TABLE `ingredients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_ar` varchar(150) NOT NULL,
  `unit` varchar(20) NOT NULL,
  `unit_ar` varchar(50) NOT NULL,
  `minimum_stock` decimal(10,3) DEFAULT 0.000,
  `current_stock` decimal(10,3) DEFAULT 0.000,
  `average_cost` decimal(10,2) DEFAULT 0.00,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول وصفات المنتجات
CREATE TABLE `product_recipes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `ingredient_id` int(11) NOT NULL,
  `quantity` decimal(10,3) NOT NULL,
  `cost_per_unit` decimal(10,2) DEFAULT 0.00,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `ingredient_id` (`ingredient_id`),
  CONSTRAINT `product_recipes_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_recipes_ibfk_2` FOREIGN KEY (`ingredient_id`) REFERENCES `ingredients` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول الموردين
CREATE TABLE `suppliers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `contact_person` varchar(100),
  `phone` varchar(20),
  `email` varchar(100),
  `address` text,
  `tax_number` varchar(50),
  `payment_terms` int(11) DEFAULT 30,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول فواتير الشراء
CREATE TABLE `purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supplier_id` int(11) NOT NULL,
  `invoice_number` varchar(50),
  `purchase_date` date NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `net_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_status` enum('pending','partial','paid') NOT NULL DEFAULT 'pending',
  `notes` text,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `supplier_id` (`supplier_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `purchases_ibfk_1` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`),
  CONSTRAINT `purchases_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول عناصر فواتير الشراء
CREATE TABLE `purchase_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_id` int(11) NOT NULL,
  `ingredient_id` int(11) NOT NULL,
  `quantity` decimal(10,3) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `expiry_date` date,
  `batch_number` varchar(50),
  PRIMARY KEY (`id`),
  KEY `purchase_id` (`purchase_id`),
  KEY `ingredient_id` (`ingredient_id`),
  CONSTRAINT `purchase_items_ibfk_1` FOREIGN KEY (`purchase_id`) REFERENCES `purchases` (`id`) ON DELETE CASCADE,
  CONSTRAINT `purchase_items_ibfk_2` FOREIGN KEY (`ingredient_id`) REFERENCES `ingredients` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول دفعات المخزون (FEFO)
CREATE TABLE `stock_batches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ingredient_id` int(11) NOT NULL,
  `purchase_item_id` int(11) NOT NULL,
  `quantity_received` decimal(10,3) NOT NULL,
  `quantity_remaining` decimal(10,3) NOT NULL,
  `unit_cost` decimal(10,2) NOT NULL,
  `expiry_date` date,
  `batch_number` varchar(50),
  `received_date` date NOT NULL,
  `is_expired` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `ingredient_id` (`ingredient_id`),
  KEY `purchase_item_id` (`purchase_item_id`),
  KEY `expiry_date` (`expiry_date`),
  CONSTRAINT `stock_batches_ibfk_1` FOREIGN KEY (`ingredient_id`) REFERENCES `ingredients` (`id`),
  CONSTRAINT `stock_batches_ibfk_2` FOREIGN KEY (`purchase_item_id`) REFERENCES `purchase_items` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- =============================================
-- جداول العملاء ونظام الولاء
-- =============================================

-- جدول العملاء
CREATE TABLE `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100),
  `address` text,
  `date_of_birth` date,
  `loyalty_points` int(11) NOT NULL DEFAULT 0,
  `total_spent` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_visits` int(11) NOT NULL DEFAULT 0,
  `last_visit` timestamp NULL DEFAULT NULL,
  `is_vip` tinyint(1) NOT NULL DEFAULT 0,
  `notes` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `phone` (`phone`),
  KEY `loyalty_points` (`loyalty_points`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول معاملات العملاء (ديون ومدفوعات)
CREATE TABLE `customer_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `transaction_type` enum('debit','credit') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` varchar(255) NOT NULL,
  `reference_type` enum('order','payment','adjustment') NOT NULL,
  `reference_id` int(11),
  `balance_after` decimal(10,2) NOT NULL DEFAULT 0.00,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `created_by` (`created_by`),
  KEY `reference_type_id` (`reference_type`,`reference_id`),
  CONSTRAINT `customer_transactions_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  CONSTRAINT `customer_transactions_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول نقاط الولاء
CREATE TABLE `loyalty_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `transaction_type` enum('earned','redeemed','expired','adjusted') NOT NULL,
  `points` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `reference_type` enum('order','redemption','expiry','adjustment') NOT NULL,
  `reference_id` int(11),
  `balance_after` int(11) NOT NULL DEFAULT 0,
  `expiry_date` date,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `created_by` (`created_by`),
  KEY `expiry_date` (`expiry_date`),
  CONSTRAINT `loyalty_points_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  CONSTRAINT `loyalty_points_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- =============================================
-- جداول الطاولات والجلسات
-- =============================================

-- جدول الطاولات
CREATE TABLE `tables` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `table_number` varchar(20) NOT NULL,
  `capacity` int(11) NOT NULL DEFAULT 4,
  `location` varchar(100),
  `status` enum('available','occupied','reserved','maintenance') NOT NULL DEFAULT 'available',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `table_number` (`table_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول جلسات الطاولات
CREATE TABLE `table_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `table_id` int(11) NOT NULL,
  `session_code` varchar(20) NOT NULL,
  `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_time` timestamp NULL DEFAULT NULL,
  `total_customers` int(11) NOT NULL DEFAULT 1,
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  `notes` text,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_code` (`session_code`),
  KEY `table_id` (`table_id`),
  KEY `created_by` (`created_by`),
  KEY `status` (`status`),
  CONSTRAINT `table_sessions_ibfk_1` FOREIGN KEY (`table_id`) REFERENCES `tables` (`id`),
  CONSTRAINT `table_sessions_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول عملاء الجلسة
CREATE TABLE `table_session_customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `customer_id` int(11),
  `customer_name` varchar(100),
  `seat_number` int(11),
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `left_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `table_session_customers_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `table_sessions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `table_session_customers_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول الحجوزات
CREATE TABLE `reservations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11),
  `customer_name` varchar(100) NOT NULL,
  `customer_phone` varchar(20) NOT NULL,
  `table_id` int(11),
  `reservation_date` date NOT NULL,
  `reservation_time` time NOT NULL,
  `party_size` int(11) NOT NULL,
  `duration_minutes` int(11) DEFAULT 120,
  `status` enum('confirmed','arrived','completed','cancelled','no_show') NOT NULL DEFAULT 'confirmed',
  `special_requests` text,
  `notes` text,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `table_id` (`table_id`),
  KEY `created_by` (`created_by`),
  KEY `reservation_datetime` (`reservation_date`,`reservation_time`),
  CONSTRAINT `reservations_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `reservations_ibfk_2` FOREIGN KEY (`table_id`) REFERENCES `tables` (`id`) ON DELETE SET NULL,
  CONSTRAINT `reservations_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- =============================================
-- جداول الطلبات والفواتير
-- =============================================

-- جدول الطلبات
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(20) NOT NULL,
  `order_type` enum('dine_in','takeaway','delivery') NOT NULL,
  `table_session_id` int(11),
  `customer_id` int(11),
  `customer_name` varchar(100),
  `customer_phone` varchar(20),
  `delivery_address` text,
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `service_charge` decimal(10,2) NOT NULL DEFAULT 0.00,
  `delivery_charge` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','confirmed','preparing','ready','completed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_status` enum('pending','partial','paid','refunded') NOT NULL DEFAULT 'pending',
  `special_instructions` text,
  `estimated_time` int(11) DEFAULT 0,
  `actual_time` int(11) DEFAULT 0,
  `loyalty_points_earned` int(11) DEFAULT 0,
  `loyalty_points_used` int(11) DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `table_session_id` (`table_session_id`),
  KEY `customer_id` (`customer_id`),
  KEY `created_by` (`created_by`),
  KEY `status` (`status`),
  KEY `order_type` (`order_type`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`table_session_id`) REFERENCES `table_sessions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول عناصر الطلبات
CREATE TABLE `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_size_id` int(11),
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `special_instructions` text,
  `status` enum('pending','preparing','ready','served','cancelled') NOT NULL DEFAULT 'pending',
  `kitchen_printer_id` int(11),
  `sent_to_kitchen_at` timestamp NULL DEFAULT NULL,
  `prepared_at` timestamp NULL DEFAULT NULL,
  `served_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  KEY `product_size_id` (`product_size_id`),
  KEY `kitchen_printer_id` (`kitchen_printer_id`),
  KEY `status` (`status`),
  CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `order_items_ibfk_3` FOREIGN KEY (`product_size_id`) REFERENCES `product_sizes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `order_items_ibfk_4` FOREIGN KEY (`kitchen_printer_id`) REFERENCES `kitchen_printers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول إضافات عناصر الطلبات
CREATE TABLE `order_item_addons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_item_id` int(11) NOT NULL,
  `product_addon_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_item_id` (`order_item_id`),
  KEY `product_addon_id` (`product_addon_id`),
  CONSTRAINT `order_item_addons_ibfk_1` FOREIGN KEY (`order_item_id`) REFERENCES `order_items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `order_item_addons_ibfk_2` FOREIGN KEY (`product_addon_id`) REFERENCES `product_addons` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول الفواتير
CREATE TABLE `bills` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bill_number` varchar(20) NOT NULL,
  `order_id` int(11),
  `table_session_id` int(11),
  `customer_id` int(11),
  `bill_type` enum('full','split_equal','split_items','split_percentage') NOT NULL DEFAULT 'full',
  `split_count` int(11) DEFAULT 1,
  `split_percentage` decimal(5,2) DEFAULT 100.00,
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `service_charge` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_status` enum('pending','partial','paid','refunded') NOT NULL DEFAULT 'pending',
  `printed_at` timestamp NULL DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `bill_number` (`bill_number`),
  KEY `order_id` (`order_id`),
  KEY `table_session_id` (`table_session_id`),
  KEY `customer_id` (`customer_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `bills_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE SET NULL,
  CONSTRAINT `bills_ibfk_2` FOREIGN KEY (`table_session_id`) REFERENCES `table_sessions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `bills_ibfk_3` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `bills_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول عناصر الفواتير
CREATE TABLE `bill_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bill_id` int(11) NOT NULL,
  `order_item_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bill_id` (`bill_id`),
  KEY `order_item_id` (`order_item_id`),
  CONSTRAINT `bill_items_ibfk_1` FOREIGN KEY (`bill_id`) REFERENCES `bills` (`id`) ON DELETE CASCADE,
  CONSTRAINT `bill_items_ibfk_2` FOREIGN KEY (`order_item_id`) REFERENCES `order_items` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول المدفوعات
CREATE TABLE `payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bill_id` int(11) NOT NULL,
  `payment_method` enum('cash','card','mobile','loyalty_points','credit') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `reference_number` varchar(100),
  `notes` text,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `bill_id` (`bill_id`),
  KEY `created_by` (`created_by`),
  KEY `payment_method` (`payment_method`),
  CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`bill_id`) REFERENCES `bills` (`id`) ON DELETE CASCADE,
  CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- =============================================
-- جداول الطهاة والورديات
-- =============================================

-- جدول الطهاة
CREATE TABLE `chefs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `chef_code` varchar(20) NOT NULL,
  `payment_type` enum('percentage','daily_rate') NOT NULL,
  `percentage_rate` decimal(5,2) DEFAULT 0.00,
  `daily_rate` decimal(10,2) DEFAULT 0.00,
  `kitchen_printer_id` int(11),
  `specialties` text,
  `hire_date` date NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `chef_code` (`chef_code`),
  KEY `user_id` (`user_id`),
  KEY `kitchen_printer_id` (`kitchen_printer_id`),
  CONSTRAINT `chefs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `chefs_ibfk_2` FOREIGN KEY (`kitchen_printer_id`) REFERENCES `kitchen_printers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول مدفوعات الطهاة
CREATE TABLE `chef_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chef_id` int(11) NOT NULL,
  `payment_period_start` date NOT NULL,
  `payment_period_end` date NOT NULL,
  `total_sales` decimal(10,2) DEFAULT 0.00,
  `days_worked` int(11) DEFAULT 0,
  `calculated_amount` decimal(10,2) NOT NULL,
  `bonus_amount` decimal(10,2) DEFAULT 0.00,
  `deduction_amount` decimal(10,2) DEFAULT 0.00,
  `final_amount` decimal(10,2) NOT NULL,
  `payment_date` date,
  `payment_method` enum('cash','bank_transfer','check') DEFAULT 'cash',
  `notes` text,
  `status` enum('calculated','paid','cancelled') NOT NULL DEFAULT 'calculated',
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `chef_id` (`chef_id`),
  KEY `created_by` (`created_by`),
  KEY `payment_period` (`payment_period_start`,`payment_period_end`),
  CONSTRAINT `chef_payments_ibfk_1` FOREIGN KEY (`chef_id`) REFERENCES `chefs` (`id`),
  CONSTRAINT `chef_payments_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول الورديات
CREATE TABLE `shifts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `shift_date` date NOT NULL,
  `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_time` timestamp NULL DEFAULT NULL,
  `opening_cash` decimal(10,2) NOT NULL DEFAULT 0.00,
  `closing_cash` decimal(10,2) DEFAULT 0.00,
  `expected_cash` decimal(10,2) DEFAULT 0.00,
  `cash_difference` decimal(10,2) DEFAULT 0.00,
  `total_sales` decimal(10,2) DEFAULT 0.00,
  `total_orders` int(11) DEFAULT 0,
  `notes` text,
  `status` enum('active','closed','cancelled') NOT NULL DEFAULT 'active',
  `closed_by` int(11),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `closed_by` (`closed_by`),
  KEY `shift_date` (`shift_date`),
  KEY `status` (`status`),
  CONSTRAINT `shifts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `shifts_ibfk_2` FOREIGN KEY (`closed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول سجل الورديات
CREATE TABLE `shift_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shift_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `description` text,
  `amount` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `shift_id` (`shift_id`),
  CONSTRAINT `shift_logs_ibfk_1` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- =============================================
-- جداول العروض والخصومات
-- =============================================

-- جدول العروض
CREATE TABLE `promotions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_ar` varchar(150) NOT NULL,
  `description` text,
  `promotion_type` enum('percentage','fixed_amount','buy_x_get_y','happy_hour','customer_specific') NOT NULL,
  `discount_value` decimal(10,2) NOT NULL DEFAULT 0.00,
  `minimum_amount` decimal(10,2) DEFAULT 0.00,
  `maximum_discount` decimal(10,2) DEFAULT 0.00,
  `buy_quantity` int(11) DEFAULT 0,
  `get_quantity` int(11) DEFAULT 0,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `start_time` time,
  `end_time` time,
  `applicable_days` varchar(20) DEFAULT '1,2,3,4,5,6,7',
  `usage_limit` int(11) DEFAULT 0,
  `usage_count` int(11) DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `promotion_type` (`promotion_type`),
  KEY `date_range` (`start_date`,`end_date`),
  CONSTRAINT `promotions_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول قواعد العروض
CREATE TABLE `promotion_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `promotion_id` int(11) NOT NULL,
  `rule_type` enum('product','category','customer','order_type','minimum_amount') NOT NULL,
  `rule_value` varchar(100) NOT NULL,
  `operator` enum('equals','in','greater_than','less_than','between') NOT NULL DEFAULT 'equals',
  PRIMARY KEY (`id`),
  KEY `promotion_id` (`promotion_id`),
  CONSTRAINT `promotion_rules_ibfk_1` FOREIGN KEY (`promotion_id`) REFERENCES `promotions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول استخدام العروض
CREATE TABLE `promotion_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `promotion_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `customer_id` int(11),
  `discount_amount` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `promotion_id` (`promotion_id`),
  KEY `order_id` (`order_id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `promotion_usage_ibfk_1` FOREIGN KEY (`promotion_id`) REFERENCES `promotions` (`id`),
  CONSTRAINT `promotion_usage_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `promotion_usage_ibfk_3` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- =============================================
-- جداول المطبخ وطلبات الطباعة
-- =============================================

-- جدول طلبات المطبخ
CREATE TABLE `kitchen_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_item_id` int(11) NOT NULL,
  `kitchen_printer_id` int(11) NOT NULL,
  `order_number` varchar(20) NOT NULL,
  `table_number` varchar(20),
  `product_name` varchar(150) NOT NULL,
  `product_name_ar` varchar(200) NOT NULL,
  `quantity` int(11) NOT NULL,
  `special_instructions` text,
  `addons` text,
  `status` enum('pending','preparing','ready','served','cancelled') NOT NULL DEFAULT 'pending',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `estimated_time` int(11) DEFAULT 0,
  `preparation_started_at` timestamp NULL DEFAULT NULL,
  `preparation_completed_at` timestamp NULL DEFAULT NULL,
  `assigned_chef_id` int(11),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `order_item_id` (`order_item_id`),
  KEY `kitchen_printer_id` (`kitchen_printer_id`),
  KEY `assigned_chef_id` (`assigned_chef_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `kitchen_orders_ibfk_1` FOREIGN KEY (`order_item_id`) REFERENCES `order_items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `kitchen_orders_ibfk_2` FOREIGN KEY (`kitchen_printer_id`) REFERENCES `kitchen_printers` (`id`),
  CONSTRAINT `kitchen_orders_ibfk_3` FOREIGN KEY (`assigned_chef_id`) REFERENCES `chefs` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول حركة المخزون
CREATE TABLE `stock_movements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ingredient_id` int(11) NOT NULL,
  `movement_type` enum('in','out','adjustment','waste','transfer') NOT NULL,
  `quantity` decimal(10,3) NOT NULL,
  `unit_cost` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `reference_type` enum('purchase','order','adjustment','waste','transfer') NOT NULL,
  `reference_id` int(11),
  `batch_id` int(11),
  `reason` varchar(255),
  `balance_after` decimal(10,3) NOT NULL DEFAULT 0.000,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `ingredient_id` (`ingredient_id`),
  KEY `batch_id` (`batch_id`),
  KEY `created_by` (`created_by`),
  KEY `movement_type` (`movement_type`),
  KEY `reference_type_id` (`reference_type`,`reference_id`),
  CONSTRAINT `stock_movements_ibfk_1` FOREIGN KEY (`ingredient_id`) REFERENCES `ingredients` (`id`),
  CONSTRAINT `stock_movements_ibfk_2` FOREIGN KEY (`batch_id`) REFERENCES `stock_batches` (`id`) ON DELETE SET NULL,
  CONSTRAINT `stock_movements_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول الإعدادات العامة
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `description` text,
  `updated_by` int(11),
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول سجل النشاطات
CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11),
  `action` varchar(100) NOT NULL,
  `module` varchar(50) NOT NULL,
  `record_id` int(11),
  `old_values` text,
  `new_values` text,
  `ip_address` varchar(45),
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `module_record` (`module`,`record_id`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- =============================================
-- إدراج البيانات الأولية
-- =============================================

-- إدراج الأدوار الأساسية
INSERT INTO `roles` (`name`, `name_ar`, `description`) VALUES
('admin', 'مدير النظام', 'مدير النظام - صلاحيات كاملة'),
('manager', 'مدير المطعم', 'مدير المطعم - إدارة العمليات والتقارير'),
('cashier', 'كاشير', 'كاشير - إدارة الطلبات والمبيعات'),
('chef', 'طباخ', 'طباخ - إدارة المطبخ وتحضير الطلبات'),
('waiter', 'نادل', 'نادل - خدمة الطاولات');

-- إدراج الصلاحيات الأساسية
INSERT INTO `permissions` (`name`, `name_ar`, `module`, `description`) VALUES
-- صلاحيات المستخدمين
('users.view', 'عرض المستخدمين', 'users', 'عرض قائمة المستخدمين'),
('users.create', 'إضافة مستخدم', 'users', 'إضافة مستخدم جديد'),
('users.edit', 'تعديل المستخدمين', 'users', 'تعديل بيانات المستخدمين'),
('users.delete', 'حذف المستخدمين', 'users', 'حذف المستخدمين'),

-- صلاحيات المنتجات
('products.view', 'عرض المنتجات', 'products', 'عرض قائمة المنتجات'),
('products.create', 'إضافة منتج', 'products', 'إضافة منتج جديد'),
('products.edit', 'تعديل المنتجات', 'products', 'تعديل بيانات المنتجات'),
('products.delete', 'حذف المنتجات', 'products', 'حذف المنتجات'),

-- صلاحيات الطلبات
('orders.view', 'عرض الطلبات', 'orders', 'عرض قائمة الطلبات'),
('orders.create', 'إنشاء طلب', 'orders', 'إنشاء طلب جديد'),
('orders.edit', 'تعديل الطلبات', 'orders', 'تعديل الطلبات'),
('orders.cancel', 'إلغاء الطلبات', 'orders', 'إلغاء الطلبات'),

-- صلاحيات المطبخ
('kitchen.view', 'عرض المطبخ', 'kitchen', 'عرض طلبات المطبخ'),
('kitchen.update', 'تحديث المطبخ', 'kitchen', 'تحديث حالة الطلبات في المطبخ'),

-- صلاحيات التقارير
('reports.sales', 'تقارير المبيعات', 'reports', 'عرض تقارير المبيعات'),
('reports.inventory', 'تقارير المخزون', 'reports', 'عرض تقارير المخزون'),
('reports.financial', 'التقارير المالية', 'reports', 'عرض التقارير المالية'),

-- صلاحيات المخزون
('inventory.view', 'عرض المخزون', 'inventory', 'عرض المخزون'),
('inventory.manage', 'إدارة المخزون', 'inventory', 'إدارة المخزون والمشتريات'),

-- صلاحيات العملاء
('customers.view', 'عرض العملاء', 'customers', 'عرض قائمة العملاء'),
('customers.manage', 'إدارة العملاء', 'customers', 'إدارة بيانات العملاء'),

-- صلاحيات الورديات
('shifts.view', 'عرض الورديات', 'shifts', 'عرض الورديات'),
('shifts.manage', 'إدارة الورديات', 'shifts', 'إدارة الورديات'),

-- صلاحيات الإعدادات
('settings.view', 'عرض الإعدادات', 'settings', 'عرض إعدادات النظام'),
('settings.manage', 'إدارة الإعدادات', 'settings', 'إدارة إعدادات النظام');

-- ربط الأدوار بالصلاحيات
-- مدير النظام - جميع الصلاحيات
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 1, id FROM `permissions`;

-- مدير المطعم - معظم الصلاحيات عدا إدارة المستخدمين والإعدادات
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 2, id FROM `permissions` WHERE `name` NOT IN ('users.create', 'users.delete', 'settings.manage');

-- الكاشير - صلاحيات الطلبات والعملاء والمنتجات
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 3, id FROM `permissions` WHERE `module` IN ('orders', 'customers', 'products') AND `name` NOT LIKE '%.delete';

-- الطباخ - صلاحيات المطبخ فقط
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 4, id FROM `permissions` WHERE `module` = 'kitchen';

-- النادل - صلاحيات الطلبات والعملاء
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 5, id FROM `permissions` WHERE `module` IN ('orders', 'customers') AND `name` NOT LIKE '%.delete';

-- إنشاء المستخدم الافتراضي (admin)
INSERT INTO `users` (`username`, `password`, `full_name`, `email`, `role_id`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 1);

-- إدراج الفئات الأساسية
INSERT INTO `categories` (`name`, `name_ar`, `description`, `sort_order`) VALUES
('appetizers', 'المقبلات', 'المقبلات والسلطات', 1),
('main_dishes', 'الأطباق الرئيسية', 'الأطباق الرئيسية', 2),
('pizza', 'البيتزا', 'أنواع البيتزا المختلفة', 3),
('burgers', 'البرجر', 'البرجر والساندويتشات', 4),
('beverages', 'المشروبات', 'المشروبات الباردة والساخنة', 5),
('desserts', 'الحلويات', 'الحلويات والآيس كريم', 6);

-- إدراج طابعات المطبخ الأساسية
INSERT INTO `kitchen_printers` (`name`, `name_ar`, `printer_type`) VALUES
('main_kitchen', 'المطبخ الرئيسي', 'thermal'),
('grill_station', 'محطة الشواء', 'thermal'),
('cold_station', 'محطة الباردة', 'thermal'),
('beverage_station', 'محطة المشروبات', 'thermal'),
('kitchen_display', 'شاشة المطبخ', 'display');

-- إدراج الطاولات الأساسية
INSERT INTO `tables` (`table_number`, `capacity`, `location`) VALUES
('T01', 2, 'النافذة'),
('T02', 4, 'الوسط'),
('T03', 4, 'الوسط'),
('T04', 6, 'الزاوية'),
('T05', 2, 'النافذة'),
('T06', 4, 'الوسط'),
('T07', 8, 'القاعة الكبيرة'),
('T08', 4, 'الوسط'),
('T09', 2, 'البار'),
('T10', 6, 'الزاوية');

-- إدراج الإعدادات الأساسية
INSERT INTO `settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('restaurant_name', 'مطعم الذواقة', 'string', 'اسم المطعم'),
('restaurant_name_en', 'Gourmet Restaurant', 'string', 'اسم المطعم بالإنجليزية'),
('restaurant_address', 'شارع الملك فهد، الرياض', 'string', 'عنوان المطعم'),
('restaurant_phone', '+966501234567', 'string', 'رقم هاتف المطعم'),
('restaurant_email', '<EMAIL>', 'string', 'البريد الإلكتروني للمطعم'),
('tax_rate', '15', 'number', 'نسبة الضريبة المضافة'),
('service_charge_rate', '10', 'number', 'نسبة رسوم الخدمة'),
('currency', 'SAR', 'string', 'العملة المستخدمة'),
('currency_symbol', 'ر.س', 'string', 'رمز العملة'),
('loyalty_points_rate', '1', 'number', 'نقطة ولاء لكل ريال'),
('loyalty_redemption_rate', '10', 'number', 'قيمة النقطة الواحدة بالريال'),
('default_preparation_time', '15', 'number', 'وقت التحضير الافتراضي بالدقائق'),
('order_number_prefix', 'ORD', 'string', 'بادئة رقم الطلب'),
('bill_number_prefix', 'BILL', 'string', 'بادئة رقم الفاتورة'),
('auto_print_kitchen', '1', 'boolean', 'طباعة تلقائية للمطبخ'),
('auto_print_receipt', '1', 'boolean', 'طباعة تلقائية للفاتورة'),
('enable_loyalty_system', '1', 'boolean', 'تفعيل نظام الولاء'),
('enable_reservations', '1', 'boolean', 'تفعيل نظام الحجوزات'),
('max_table_session_hours', '4', 'number', 'الحد الأقصى لساعات جلسة الطاولة'),
('low_stock_alert_days', '7', 'number', 'تنبيه المخزون المنخفض بالأيام'),
('expiry_alert_days', '3', 'number', 'تنبيه انتهاء الصلاحية بالأيام');

-- إدراج مكونات أساسية للمخزون
INSERT INTO `ingredients` (`name`, `name_ar`, `unit`, `unit_ar`, `minimum_stock`) VALUES
('flour', 'دقيق', 'kg', 'كيلو', 10.000),
('tomato_sauce', 'صلصة طماطم', 'liter', 'لتر', 5.000),
('cheese', 'جبن موزاريلا', 'kg', 'كيلو', 5.000),
('chicken_breast', 'صدر دجاج', 'kg', 'كيلو', 10.000),
('beef_patty', 'قطعة لحم برجر', 'piece', 'قطعة', 50.000),
('lettuce', 'خس', 'kg', 'كيلو', 2.000),
('tomato', 'طماطم', 'kg', 'كيلو', 5.000),
('onion', 'بصل', 'kg', 'كيلو', 3.000),
('potato', 'بطاطس', 'kg', 'كيلو', 10.000),
('oil', 'زيت طبخ', 'liter', 'لتر', 5.000),
('salt', 'ملح', 'kg', 'كيلو', 1.000),
('pepper', 'فلفل أسود', 'kg', 'كيلو', 0.500),
('coca_cola', 'كوكا كولا', 'can', 'علبة', 100.000),
('water_bottle', 'مياه معبأة', 'bottle', 'زجاجة', 50.000);

-- إدراج منتجات أساسية
INSERT INTO `products` (`name`, `name_ar`, `description`, `category_id`, `kitchen_printer_id`, `base_price`, `is_recipe_based`, `preparation_time`) VALUES
('Margherita Pizza', 'بيتزا مارجريتا', 'بيتزا كلاسيكية بالطماطم والجبن والريحان', 3, 1, 45.00, 1, 20),
('Chicken Burger', 'برجر الدجاج', 'برجر دجاج مشوي مع الخضار', 4, 2, 35.00, 1, 15),
('Caesar Salad', 'سلطة سيزر', 'سلطة خس مع صلصة سيزر والجبن', 1, 3, 25.00, 1, 10),
('French Fries', 'بطاطس مقلية', 'بطاطس مقلية ذهبية مقرمشة', 1, 2, 15.00, 1, 8),
('Coca Cola', 'كوكا كولا', 'مشروب غازي بارد', 5, 4, 8.00, 0, 2),
('Chocolate Cake', 'كيكة الشوكولاتة', 'كيكة شوكولاتة غنية', 6, 1, 20.00, 1, 5);

-- إدراج أحجام المنتجات
INSERT INTO `product_sizes` (`product_id`, `name`, `name_ar`, `price_modifier`) VALUES
(1, 'Small', 'صغير', 0.00),
(1, 'Medium', 'وسط', 10.00),
(1, 'Large', 'كبير', 20.00),
(2, 'Regular', 'عادي', 0.00),
(2, 'Large', 'كبير', 8.00),
(5, 'Can', 'علبة', 0.00),
(5, 'Bottle', 'زجاجة', 3.00);

-- إدراج إضافات المنتجات
INSERT INTO `product_addons` (`product_id`, `name`, `name_ar`, `price`) VALUES
(1, 'Extra Cheese', 'جبن إضافي', 5.00),
(1, 'Mushrooms', 'فطر', 4.00),
(1, 'Pepperoni', 'ببروني', 6.00),
(1, 'Olives', 'زيتون', 3.00),
(2, 'Extra Cheese', 'جبن إضافي', 3.00),
(2, 'Bacon', 'لحم مقدد', 5.00),
(2, 'Avocado', 'أفوكادو', 4.00),
(3, 'Grilled Chicken', 'دجاج مشوي', 8.00),
(3, 'Croutons', 'خبز محمص', 2.00);

COMMIT;
