<?php
/**
 * ثوابت النظام
 * System Constants
 * Restaurant Management System
 */

// منع الوصول المباشر
if (!defined('SYSTEM_INIT')) {
    die('Direct access not allowed');
}

// معلومات النظام
define('SYSTEM_NAME', 'نظام إدارة المطعم');
define('SYSTEM_NAME_EN', 'Restaurant Management System');
define('SYSTEM_VERSION', '1.0.0');
define('SYSTEM_AUTHOR', 'Restaurant Systems Team');
define('SYSTEM_COPYRIGHT', '© 2024 Restaurant Management System');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة
define('PASSWORD_MIN_LENGTH', 6);
define('REQUIRE_STRONG_PASSWORD', false);

// إعدادات رفع الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_IMAGE_TYPES', array('jpg', 'jpeg', 'png', 'gif'));
define('ALLOWED_DOCUMENT_TYPES', array('pdf', 'doc', 'docx', 'xls', 'xlsx'));
// تم نقل ALLOWED_EXTENSIONS من config/database.php
$GLOBALS['ALLOWED_EXTENSIONS'] = array('jpg', 'jpeg', 'png', 'gif', 'pdf');
define('UPLOAD_PATH', 'uploads/');

// إعدادات قاعدة البيانات
define('DB_PREFIX', '');
define('DB_CHARSET', 'utf8');
define('DB_COLLATE', 'utf8_general_ci');

// إعدادات التاريخ والوقت
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DATE_FORMAT', 'd/m/Y');
define('TIME_FORMAT', 'H:i');
define('DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات العملة
define('DEFAULT_CURRENCY', 'SAR');
define('CURRENCY_SYMBOL', 'ر.س');
define('CURRENCY_POSITION', 'after'); // before أو after
define('DECIMAL_PLACES', 2);
define('THOUSANDS_SEPARATOR', ',');
define('DECIMAL_SEPARATOR', '.');

// إعدادات الضرائب والرسوم
define('DEFAULT_TAX_RATE', 15.00);
define('DEFAULT_SERVICE_CHARGE_RATE', 10.00);
define('AUTO_APPLY_TAX', true);
define('AUTO_APPLY_SERVICE_CHARGE', true);

// إعدادات نظام الولاء
define('LOYALTY_ENABLED', true);
define('LOYALTY_POINTS_PER_SAR', 1);
define('LOYALTY_POINT_VALUE', 0.01);
define('LOYALTY_MIN_REDEEM', 100);

// حالات الطلبات
define('ORDER_STATUS_PENDING', 'pending');
define('ORDER_STATUS_CONFIRMED', 'confirmed');
define('ORDER_STATUS_PREPARING', 'preparing');
define('ORDER_STATUS_READY', 'ready');
define('ORDER_STATUS_COMPLETED', 'completed');
define('ORDER_STATUS_CANCELLED', 'cancelled');

// أنواع الطلبات
define('ORDER_TYPE_DINE_IN', 'dine_in');
define('ORDER_TYPE_TAKEAWAY', 'takeaway');
define('ORDER_TYPE_DELIVERY', 'delivery');

// حالات الطاولات
define('TABLE_STATUS_AVAILABLE', 'available');
define('TABLE_STATUS_OCCUPIED', 'occupied');
define('TABLE_STATUS_RESERVED', 'reserved');
define('TABLE_STATUS_MAINTENANCE', 'maintenance');

// حالات الدفع
define('PAYMENT_STATUS_PENDING', 'pending');
define('PAYMENT_STATUS_PAID', 'paid');
define('PAYMENT_STATUS_PARTIAL', 'partial');
define('PAYMENT_STATUS_REFUNDED', 'refunded');

// طرق الدفع
define('PAYMENT_METHOD_CASH', 'cash');
define('PAYMENT_METHOD_CARD', 'card');
define('PAYMENT_METHOD_TRANSFER', 'transfer');
define('PAYMENT_METHOD_LOYALTY', 'loyalty');

// مستويات السجلات
define('LOG_LEVEL_ERROR', 'error');
define('LOG_LEVEL_WARNING', 'warning');
define('LOG_LEVEL_INFO', 'info');
define('LOG_LEVEL_DEBUG', 'debug');

// أنواع الأنشطة
define('ACTIVITY_LOGIN', 'login');
define('ACTIVITY_LOGOUT', 'logout');
define('ACTIVITY_CREATE', 'create');
define('ACTIVITY_UPDATE', 'update');
define('ACTIVITY_DELETE', 'delete');
define('ACTIVITY_VIEW', 'view');

// الصلاحيات الأساسية
define('PERMISSION_ADMIN', 'admin');
define('PERMISSION_MANAGER', 'manager');
define('PERMISSION_CASHIER', 'cashier');
define('PERMISSION_KITCHEN', 'kitchen');
define('PERMISSION_WAITER', 'waiter');

// إعدادات الطباعة
define('RECEIPT_WIDTH_58MM', '58');
define('RECEIPT_WIDTH_80MM', '80');
define('DEFAULT_RECEIPT_WIDTH', RECEIPT_WIDTH_80MM);
define('AUTO_PRINT_RECEIPT', true);
define('AUTO_PRINT_KITCHEN', true);

// إعدادات الإشعارات
define('NOTIFICATIONS_ENABLED', true);
define('EMAIL_NOTIFICATIONS', false);
define('SMS_NOTIFICATIONS', false);
define('LOW_STOCK_THRESHOLD', 10);
define('EXPIRY_ALERT_DAYS', 7);

// إعدادات التقارير
define('REPORTS_PER_PAGE', 50);
define('CHART_COLORS', array(
    '#007bff', '#28a745', '#ffc107', '#dc3545',
    '#17a2b8', '#6f42c1', '#fd7e14', '#20c997'
));

// إعدادات البحث
define('SEARCH_MIN_LENGTH', 2);
define('SEARCH_MAX_RESULTS', 100);

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', false);
define('CACHE_LIFETIME', 3600);

// إعدادات التصدير
define('EXPORT_MAX_RECORDS', 10000);
define('EXPORT_FORMATS', array('excel', 'pdf', 'csv'));

// رسائل النظام
define('MSG_SUCCESS', 'تم بنجاح');
define('MSG_ERROR', 'حدث خطأ');
define('MSG_WARNING', 'تحذير');
define('MSG_INFO', 'معلومات');
define('MSG_CONFIRM', 'تأكيد');

define('MSG_SAVE_SUCCESS', 'تم الحفظ بنجاح');
define('MSG_UPDATE_SUCCESS', 'تم التحديث بنجاح');
define('MSG_DELETE_SUCCESS', 'تم الحذف بنجاح');
define('MSG_DELETE_CONFIRM', 'هل أنت متأكد من الحذف؟');

define('MSG_LOGIN_REQUIRED', 'يجب تسجيل الدخول أولاً');
define('MSG_PERMISSION_DENIED', 'ليس لديك صلاحية للوصول');
define('MSG_INVALID_DATA', 'البيانات المدخلة غير صحيحة');
define('MSG_REQUIRED_FIELDS', 'يرجى ملء جميع الحقول المطلوبة');

// أسماء الجداول
define('TABLE_USERS', 'users');
define('TABLE_ROLES', 'roles');
define('TABLE_PERMISSIONS', 'permissions');
define('TABLE_CUSTOMERS', 'customers');
define('TABLE_PRODUCTS', 'products');
define('TABLE_CATEGORIES', 'categories');
define('TABLE_ORDERS', 'orders');
define('TABLE_ORDER_ITEMS', 'order_items');
define('TABLE_TABLES', 'tables');
define('TABLE_TABLE_SESSIONS', 'table_sessions');
define('TABLE_PAYMENTS', 'payments');
define('TABLE_INVENTORY', 'ingredients');
define('TABLE_SUPPLIERS', 'suppliers');
define('TABLE_PURCHASES', 'purchases');
define('TABLE_SETTINGS', 'settings');
define('TABLE_ACTIVITY_LOG', 'activity_log');

// مسارات النظام
define('PATH_MODELS', 'models/');
define('PATH_VIEWS', 'views/');
define('PATH_INCLUDES', 'includes/');
define('PATH_ASSETS', 'assets/');
define('PATH_UPLOADS', 'uploads/');
define('PATH_LOGS', 'logs/');
define('PATH_TEMP', 'temp/');

// أنماط التاريخ
define('MYSQL_DATE_FORMAT', 'Y-m-d');
define('MYSQL_DATETIME_FORMAT', 'Y-m-d H:i:s');
define('MYSQL_TIME_FORMAT', 'H:i:s');

// إعدادات الأداء
define('ENABLE_GZIP', true);
define('ENABLE_CACHING', false);
define('DEBUG_MODE', false);
define('SHOW_ERRORS', false);

// إعدادات البريد الإلكتروني
define('MAIL_ENABLED', false);
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '');
define('MAIL_PASSWORD', '');
define('MAIL_FROM_EMAIL', '');
define('MAIL_FROM_NAME', SYSTEM_NAME);

// إعدادات الرسائل النصية
define('SMS_ENABLED', false);
define('SMS_PROVIDER', 'local');
define('SMS_API_KEY', '');
define('SMS_SENDER_NAME', '');

// إعدادات النسخ الاحتياطي
define('BACKUP_ENABLED', true);
define('BACKUP_PATH', 'backups/');
define('BACKUP_RETENTION_DAYS', 30);
define('AUTO_BACKUP', false);

// إعدادات الصيانة
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'النظام قيد الصيانة، يرجى المحاولة لاحقاً');

// إعدادات API
define('API_ENABLED', false);
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 100);

// إعدادات متقدمة
define('MULTI_LANGUAGE', false);
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', array('ar', 'en'));

define('MULTI_CURRENCY', false);
define('SUPPORTED_CURRENCIES', array('SAR', 'USD', 'EUR'));

define('MULTI_BRANCH', false);
define('DEFAULT_BRANCH', 1);

// تحديد المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// إعدادات PHP
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
?>
