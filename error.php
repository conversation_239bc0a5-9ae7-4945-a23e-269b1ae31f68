<?php
/**
 * صفحة الأخطاء المخصصة
 * Custom Error Page
 * Restaurant Management System
 */

// تهيئة النظام
require_once 'config/init.php';

$error_code = isset($_GET['code']) ? $_GET['code'] : '404';
$error_messages = array(
    '403' => array(
        'title' => 'ممنوع الوصول',
        'message' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة',
        'icon' => 'fas fa-ban'
    ),
    '404' => array(
        'title' => 'الصفحة غير موجودة',
        'message' => 'الصفحة التي تبحث عنها غير موجودة أو تم نقلها',
        'icon' => 'fas fa-search'
    ),
    '500' => array(
        'title' => 'خطأ في الخادم',
        'message' => 'حدث خطأ داخلي في الخادم، يرجى المحاولة لاحقاً',
        'icon' => 'fas fa-server'
    )
);

$error = isset($error_messages[$error_code]) ? $error_messages[$error_code] : $error_messages['404'];

// تسجيل الخطأ
logError("Error page accessed: $error_code - " . $_SERVER['REQUEST_URI']);

http_response_code(intval($error_code));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $error['title']; ?> - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/custom.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .error-icon {
            font-size: 5rem;
            color: #dc3545;
            margin-bottom: 1.5rem;
        }
        
        .error-code {
            font-size: 4rem;
            font-weight: bold;
            color: #343a40;
            margin-bottom: 1rem;
        }
        
        .error-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 1rem;
        }
        
        .error-message {
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            color: white;
            text-decoration: none;
            transition: transform 0.3s ease;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            color: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 2rem;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .animated {
            animation: fadeInUp 0.8s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="error-container animated">
        <div class="error-icon">
            <i class="<?php echo $error['icon']; ?>"></i>
        </div>
        
        <div class="error-code"><?php echo $error_code; ?></div>
        
        <h1 class="error-title"><?php echo $error['title']; ?></h1>
        
        <p class="error-message"><?php echo $error['message']; ?></p>
        
        <div class="d-grid gap-2 d-md-block">
            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للخلف
            </a>
            <a href="<?php echo SITE_URL; ?>" class="btn-home">
                <i class="fas fa-home me-2"></i>
                الصفحة الرئيسية
            </a>
        </div>
        
        <?php if (DEBUG_MODE): ?>
        <div class="error-details">
            <strong>تفاصيل تقنية:</strong><br>
            <small>
                الرابط: <?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?><br>
                الوقت: <?php echo date('Y-m-d H:i:s'); ?><br>
                IP: <?php echo $_SERVER['REMOTE_ADDR']; ?>
            </small>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الماوس على الأيقونة
            const icon = document.querySelector('.error-icon i');
            icon.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1) rotate(10deg)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            icon.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) rotate(0deg)';
            });
            
            // إعادة توجيه تلقائي للصفحة الرئيسية بعد 30 ثانية
            setTimeout(function() {
                if (confirm('هل تريد الانتقال للصفحة الرئيسية؟')) {
                    window.location.href = '<?php echo SITE_URL; ?>';
                }
            }, 30000);
        });
        
        // تتبع الأخطاء (اختياري)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_view', {
                page_title: 'Error <?php echo $error_code; ?>',
                page_location: window.location.href
            });
        }
    </script>
</body>
</html>
