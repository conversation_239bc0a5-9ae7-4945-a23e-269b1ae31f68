<?php
/**
 * الحصول على تفاصيل الطلب
 * Get Order Details
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';
require_once '../../../models/Order.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('orders.view');

$order_id = $_POST['order_id'] ?? null;

if (!$order_id) {
    echo '<div class="alert alert-danger">معرف الطلب مطلوب</div>';
    exit;
}

try {
    $order_model = new Order();
    $order = $order_model->getById($order_id);
    
    if (!$order) {
        echo '<div class="alert alert-danger">الطلب غير موجود</div>';
        exit;
    }
    
    // الحصول على عناصر الطلب
    $items = $order_model->getItems($order_id);
    
    // حساب الإجماليات
    $subtotal = 0;
    foreach ($items as $item) {
        $subtotal += $item['total_price'];
    }
    
    $tax_amount = calculateTax($subtotal);
    $service_charge = calculateServiceCharge($subtotal);
    $total = $subtotal + $tax_amount + $service_charge;
    
    ?>
    
    <div class="row">
        <div class="col-md-6">
            <h6>معلومات الطلب</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>رقم الطلب:</strong></td>
                    <td><?php echo $order['order_number']; ?></td>
                </tr>
                <tr>
                    <td><strong>النوع:</strong></td>
                    <td>
                        <?php
                        $type_names = [
                            'dine_in' => 'طاولة',
                            'takeaway' => 'سفري',
                            'delivery' => 'توصيل'
                        ];
                        echo $type_names[$order['order_type']];
                        ?>
                    </td>
                </tr>
                <tr>
                    <td><strong>الحالة:</strong></td>
                    <td>
                        <?php
                        $status_names = [
                            'pending' => 'قيد الانتظار',
                            'confirmed' => 'مؤكد',
                            'preparing' => 'قيد التحضير',
                            'ready' => 'جاهز',
                            'completed' => 'مكتمل',
                            'cancelled' => 'ملغي'
                        ];
                        $status_classes = [
                            'pending' => 'warning',
                            'confirmed' => 'info',
                            'preparing' => 'primary',
                            'ready' => 'success',
                            'completed' => 'dark',
                            'cancelled' => 'danger'
                        ];
                        ?>
                        <span class="badge bg-<?php echo $status_classes[$order['status']]; ?>">
                            <?php echo $status_names[$order['status']]; ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>التاريخ:</strong></td>
                    <td><?php echo formatDate($order['created_at'], 'd/m/Y H:i'); ?></td>
                </tr>
                <?php if ($order['table_number']): ?>
                <tr>
                    <td><strong>الطاولة:</strong></td>
                    <td><?php echo $order['table_number']; ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td><strong>المنشئ:</strong></td>
                    <td><?php echo $order['created_by_name']; ?></td>
                </tr>
            </table>
        </div>
        
        <div class="col-md-6">
            <h6>معلومات العميل</h6>
            <table class="table table-sm">
                <?php if ($order['customer_name']): ?>
                <tr>
                    <td><strong>الاسم:</strong></td>
                    <td><?php echo $order['customer_name']; ?></td>
                </tr>
                <tr>
                    <td><strong>الهاتف:</strong></td>
                    <td><?php echo $order['customer_phone']; ?></td>
                </tr>
                <?php else: ?>
                <tr>
                    <td colspan="2" class="text-muted">لا توجد معلومات عميل</td>
                </tr>
                <?php endif; ?>
                
                <?php if ($order['delivery_address']): ?>
                <tr>
                    <td><strong>عنوان التوصيل:</strong></td>
                    <td><?php echo $order['delivery_address']; ?></td>
                </tr>
                <?php endif; ?>
                
                <?php if ($order['special_instructions']): ?>
                <tr>
                    <td><strong>تعليمات خاصة:</strong></td>
                    <td><?php echo $order['special_instructions']; ?></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
    </div>
    
    <hr>
    
    <h6>عناصر الطلب</h6>
    <div class="table-responsive">
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($items as $item): ?>
                <tr>
                    <td>
                        <div><?php echo $item['product_name']; ?></div>
                        <?php if ($item['size_name']): ?>
                            <small class="text-muted">الحجم: <?php echo $item['size_name']; ?></small>
                        <?php endif; ?>
                        
                        <?php
                        // الحصول على الإضافات
                        $addons = $order_model->getItemAddons($item['id']);
                        if ($addons):
                        ?>
                            <div class="mt-1">
                                <?php foreach ($addons as $addon): ?>
                                    <small class="badge bg-light text-dark me-1">
                                        <?php echo $addon['addon_name']; ?>
                                        <?php if ($addon['quantity'] > 1): ?>
                                            (<?php echo $addon['quantity']; ?>)
                                        <?php endif; ?>
                                    </small>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($item['special_instructions']): ?>
                            <div class="mt-1">
                                <small class="text-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?php echo $item['special_instructions']; ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </td>
                    <td><?php echo $item['quantity']; ?></td>
                    <td><?php echo formatCurrency($item['unit_price']); ?></td>
                    <td><?php echo formatCurrency($item['total_price']); ?></td>
                    <td>
                        <?php
                        $item_status_names = [
                            'pending' => 'قيد الانتظار',
                            'preparing' => 'قيد التحضير',
                            'ready' => 'جاهز',
                            'served' => 'تم التقديم',
                            'cancelled' => 'ملغي'
                        ];
                        $item_status_classes = [
                            'pending' => 'warning',
                            'preparing' => 'primary',
                            'ready' => 'success',
                            'served' => 'dark',
                            'cancelled' => 'danger'
                        ];
                        ?>
                        <span class="badge bg-<?php echo $item_status_classes[$item['status']]; ?>">
                            <?php echo $item_status_names[$item['status']]; ?>
                        </span>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <hr>
    
    <div class="row">
        <div class="col-md-6 offset-md-6">
            <table class="table table-sm">
                <tr>
                    <td><strong>المجموع الفرعي:</strong></td>
                    <td class="text-end"><?php echo formatCurrency($order['subtotal']); ?></td>
                </tr>
                <tr>
                    <td>الضريبة (15%):</td>
                    <td class="text-end"><?php echo formatCurrency($order['tax_amount']); ?></td>
                </tr>
                <tr>
                    <td>رسوم الخدمة (10%):</td>
                    <td class="text-end"><?php echo formatCurrency($order['service_charge']); ?></td>
                </tr>
                <?php if ($order['discount_amount'] > 0): ?>
                <tr>
                    <td>الخصم:</td>
                    <td class="text-end text-success">-<?php echo formatCurrency($order['discount_amount']); ?></td>
                </tr>
                <?php endif; ?>
                <tr class="table-dark">
                    <td><strong>الإجمالي:</strong></td>
                    <td class="text-end"><strong><?php echo formatCurrency($order['total_amount']); ?></strong></td>
                </tr>
            </table>
        </div>
    </div>
    
    <?php if ($order['loyalty_points_earned'] > 0): ?>
    <div class="alert alert-info">
        <i class="fas fa-star me-2"></i>
        نقاط الولاء المكتسبة: <strong><?php echo $order['loyalty_points_earned']; ?></strong> نقطة
    </div>
    <?php endif; ?>
    
    <div class="text-center mt-3">
        <button type="button" class="btn btn-success" onclick="printReceipt(<?php echo $order['id']; ?>)">
            <i class="fas fa-print me-2"></i>
            طباعة الفاتورة
        </button>
        
        <?php if ($order['status'] != 'completed' && $order['status'] != 'cancelled'): ?>
        <button type="button" class="btn btn-primary" onclick="editOrder(<?php echo $order['id']; ?>)">
            <i class="fas fa-edit me-2"></i>
            تعديل الطلب
        </button>
        <?php endif; ?>
    </div>
    
    <?php
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">حدث خطأ: ' . $e->getMessage() . '</div>';
}
?>
