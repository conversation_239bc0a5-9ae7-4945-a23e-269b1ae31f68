<?php
/**
 * الشريط الجانبي المحدث
 * Updated Sidebar Navigation
 * Restaurant Management System
 */

// التحقق من الصفحة الحالية لتمييز القائمة النشطة
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

function isActiveNew($page, $dir = null) {
    global $current_page, $current_dir;
    if ($dir) {
        return $current_dir === $dir ? 'active' : '';
    }
    return $current_page === $page ? 'active' : '';
}
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <!-- معلومات المستخدم -->
        <div class="user-info p-3 border-bottom">
            <div class="d-flex align-items-center">
                <div class="avatar-circle me-2">
                    <?php echo mb_substr($_SESSION['full_name'], 0, 1); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo $_SESSION['full_name']; ?></div>
                    <small class="text-muted"><?php echo $_SESSION['role_name']; ?></small>
                </div>
            </div>
        </div>
        
        <ul class="nav flex-column">
            <!-- الصفحة الرئيسية -->
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('index.php'); ?>" href="../../index.php">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </a>
            </li>
            
            <!-- نقاط البيع -->
            <?php if (hasPermission('orders.create')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'pos'); ?>" href="../pos/index.php">
                    <i class="fas fa-cash-register me-2"></i>
                    نقاط البيع
                </a>
            </li>
            <?php endif; ?>
            
            <!-- شاشة المطبخ -->
            <?php if (hasPermission('kitchen.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'kitchen'); ?>" href="../kitchen/display.php">
                    <i class="fas fa-utensils me-2"></i>
                    شاشة المطبخ
                </a>
            </li>
            <?php endif; ?>
            
            <li><hr class="dropdown-divider"></li>
            
            <!-- إدارة الطلبات -->
            <?php if (hasPermission('orders.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'orders'); ?>" href="../orders/list.php">
                    <i class="fas fa-shopping-cart me-2"></i>
                    إدارة الطلبات
                    <?php
                    // عرض عدد الطلبات النشطة
                    $active_orders = DB::selectOne("SELECT COUNT(*) as count FROM orders WHERE status IN ('pending', 'confirmed', 'preparing')");
                    if ($active_orders['count'] > 0):
                    ?>
                    <span class="badge bg-danger ms-auto"><?php echo $active_orders['count']; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            <?php endif; ?>
            
            <!-- إدارة الطاولات -->
            <?php if (hasPermission('tables.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'tables'); ?>" href="../tables/list.php">
                    <i class="fas fa-chair me-2"></i>
                    إدارة الطاولات
                </a>
            </li>
            <?php endif; ?>
            
            <!-- إدارة العملاء -->
            <?php if (hasPermission('customers.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'customers'); ?>" href="../customers/list.php">
                    <i class="fas fa-users me-2"></i>
                    إدارة العملاء
                </a>
            </li>
            <?php endif; ?>
            
            <li><hr class="dropdown-divider"></li>
            
            <!-- إدارة المنتجات -->
            <?php if (hasPermission('products.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'products'); ?>" 
                   data-bs-toggle="collapse" data-bs-target="#productsSubmenu" 
                   aria-expanded="false">
                    <i class="fas fa-box me-2"></i>
                    إدارة المنتجات
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo $current_dir === 'products' || $current_dir === 'categories' ? 'show' : ''; ?>" id="productsSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActiveNew('', 'products'); ?>" href="../products/list.php">
                                <i class="fas fa-list me-2"></i>
                                قائمة المنتجات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActiveNew('', 'categories'); ?>" href="../categories/list.php">
                                <i class="fas fa-tags me-2"></i>
                                الفئات
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
            
            <!-- إدارة المخزون -->
            <?php if (hasPermission('inventory.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'inventory'); ?>" 
                   data-bs-toggle="collapse" data-bs-target="#inventorySubmenu" 
                   aria-expanded="false">
                    <i class="fas fa-warehouse me-2"></i>
                    إدارة المخزون
                    <i class="fas fa-chevron-down ms-auto"></i>
                    <?php
                    // عرض تنبيه المخزون المنخفض
                    $low_stock = DB::selectOne("SELECT COUNT(*) as count FROM ingredients WHERE current_stock <= minimum_stock AND is_active = 1");
                    if ($low_stock['count'] > 0):
                    ?>
                    <span class="badge bg-warning ms-auto"><?php echo $low_stock['count']; ?></span>
                    <?php endif; ?>
                </a>
                <div class="collapse <?php echo $current_dir === 'inventory' ? 'show' : ''; ?>" id="inventorySubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="../inventory/ingredients.php">
                                <i class="fas fa-apple-alt me-2"></i>
                                المكونات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../inventory/stock.php">
                                <i class="fas fa-boxes me-2"></i>
                                حالة المخزون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../inventory/purchases.php">
                                <i class="fas fa-shopping-basket me-2"></i>
                                المشتريات
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
            
            <li><hr class="dropdown-divider"></li>
            
            <!-- التقارير -->
            <?php if (hasPermission('reports.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'reports'); ?>" 
                   data-bs-toggle="collapse" data-bs-target="#reportsSubmenu" 
                   aria-expanded="false">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo $current_dir === 'reports' ? 'show' : ''; ?>" id="reportsSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/sales.php">
                                <i class="fas fa-dollar-sign me-2"></i>
                                تقارير المبيعات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/inventory.php">
                                <i class="fas fa-warehouse me-2"></i>
                                تقارير المخزون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/customers.php">
                                <i class="fas fa-users me-2"></i>
                                تقارير العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/financial.php">
                                <i class="fas fa-calculator me-2"></i>
                                التقارير المالية
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
            
            <!-- الورديات -->
            <?php if (hasPermission('shifts.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'shifts'); ?>" href="../shifts/list.php">
                    <i class="fas fa-clock me-2"></i>
                    الورديات
                    <?php if (hasActiveShift()): ?>
                    <span class="badge bg-success ms-auto">نشطة</span>
                    <?php endif; ?>
                </a>
            </li>
            <?php endif; ?>
            
            <li><hr class="dropdown-divider"></li>
            
            <!-- إدارة المستخدمين -->
            <?php if (hasPermission('users.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'users'); ?>" href="../users/list.php">
                    <i class="fas fa-user-cog me-2"></i>
                    إدارة المستخدمين
                </a>
            </li>
            <?php endif; ?>
            
            <!-- الإعدادات -->
            <?php if (hasPermission('settings.view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveNew('', 'settings'); ?>" 
                   data-bs-toggle="collapse" data-bs-target="#settingsSubmenu" 
                   aria-expanded="false">
                    <i class="fas fa-cogs me-2"></i>
                    الإعدادات
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo $current_dir === 'settings' ? 'show' : ''; ?>" id="settingsSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/index.php">
                                <i class="fas fa-sliders-h me-2"></i>
                                الإعدادات العامة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/printers.php">
                                <i class="fas fa-print me-2"></i>
                                طابعات المطبخ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/backup.php">
                                <i class="fas fa-database me-2"></i>
                                النسخ الاحتياطي
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
            
            <li><hr class="dropdown-divider"></li>
            
            <!-- تسجيل الخروج -->
            <li class="nav-item">
                <a class="nav-link text-danger" href="../../logout.php" 
                   onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    تسجيل الخروج
                </a>
            </li>
        </ul>
        
        <!-- معلومات النظام -->
        <div class="mt-auto p-3 border-top">
            <div class="text-center">
                <small class="text-muted">
                    <div>نظام إدارة المطعم</div>
                    <div>الإصدار 1.0.0</div>
                    <?php if (hasActiveShift()): ?>
                    <div class="text-success">
                        <i class="fas fa-circle"></i>
                        وردية نشطة
                    </div>
                    <?php endif; ?>
                </small>
            </div>
        </div>
    </div>
</nav>
