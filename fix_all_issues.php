<?php
/**
 * إصلاح شامل لجميع المشاكل
 * Complete Fix for All Issues
 * Restaurant Management System
 */

echo "بدء الإصلاح الشامل لجميع المشاكل...\n\n";

$fixes_applied = 0;
$errors = array();

// 1. إنشاء المجلدات المطلوبة
echo "1. إنشاء المجلدات المطلوبة:\n";
$required_dirs = array('logs', 'uploads', 'temp', 'backups', 'uploads/products', 'uploads/receipts', 'uploads/avatars');

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "   ✓ تم إنشاء مجلد: $dir\n";
            $fixes_applied++;
        } else {
            echo "   ✗ فشل في إنشاء مجلد: $dir\n";
            $errors[] = "فشل في إنشاء مجلد: $dir";
        }
    } else {
        echo "   - مجلد موجود: $dir\n";
    }
}

// 2. إصلاح التوافق مع PHP 5.6
echo "\n2. إصلاح التوافق مع PHP 5.6:\n";

$files_to_fix = array(
    'config/init.php',
    'config/constants.php',
    'config/database.php',
    'views/tables/list.php',
    'views/products/add.php',
    'views/customers/list.php',
    'views/categories/list.php',
    'views/reports/sales.php',
    'views/settings/index.php',
    'views/print/receipt.php',
    'error.php'
);

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        echo "   إصلاح ملف: $file\n";
        
        $content = file_get_contents($file);
        $original_content = $content;
        
        // إصلاح Null Coalescing Operator (??)
        $content = preg_replace('/\$_([A-Z]+)\[([\'"][^\'"]*)([\'"])\]\s*\?\?\s*([^;,)]+)/', 'isset($_\\1[\\2\\3]) ? $_\\1[\\2\\3] : \\4', $content);
        $content = preg_replace('/\$([a-zA-Z_][a-zA-Z0-9_]*)\s*\?\?\s*([^;,)]+)/', 'isset($\\1) ? $\\1 : \\2', $content);
        
        // إصلاح Array Syntax الجديد []
        $content = preg_replace('/=\s*\[\s*\]/', '= array()', $content);
        $content = preg_replace('/=\s*\[([^\]]+)\]/', '= array(\\1)', $content);
        
        // إصلاح function parameters
        $content = preg_replace('/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\$[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*\[\s*\]/', 'function \\1(\\2 = array()', $content);
        
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "     ✓ تم إصلاح الملف\n";
                $fixes_applied++;
            } else {
                echo "     ✗ فشل في حفظ الملف\n";
                $errors[] = "فشل في حفظ الملف: $file";
            }
        } else {
            echo "     - لا يحتاج إصلاح\n";
        }
    } else {
        echo "     ✗ الملف غير موجود: $file\n";
        $errors[] = "الملف غير موجود: $file";
    }
}

// 3. إصلاح تعريف الدوال المزدوج
echo "\n3. إصلاح تعريف الدوال المزدوج:\n";

// إزالة setSetting من config/database.php إذا كانت موجودة
if (file_exists('config/database.php')) {
    $content = file_get_contents('config/database.php');
    
    // البحث عن تعريف setSetting وإزالته
    $pattern = '/function\s+setSetting\s*\([^}]+\}/s';
    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, '// تم نقل دالة setSetting إلى config/init.php لتجنب التعريف المزدوج', $content);
        
        if (file_put_contents('config/database.php', $content)) {
            echo "   ✓ تم إزالة setSetting من config/database.php\n";
            $fixes_applied++;
        } else {
            echo "   ✗ فشل في إزالة setSetting من config/database.php\n";
            $errors[] = "فشل في إزالة setSetting من config/database.php";
        }
    } else {
        echo "   - setSetting غير موجودة في config/database.php\n";
    }
}

// 4. إنشاء ملفات الحماية
echo "\n4. إنشاء ملفات الحماية:\n";

$protected_dirs = array('logs', 'temp', 'backups', 'config');
$htaccess_content = "Order deny,allow\nDeny from all";
$index_content = "<?php\n// Access denied\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n?>";

foreach ($protected_dirs as $dir) {
    if (is_dir($dir)) {
        // إنشاء .htaccess
        $htaccess_file = $dir . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            if (file_put_contents($htaccess_file, $htaccess_content)) {
                echo "   ✓ تم إنشاء حماية: $htaccess_file\n";
                $fixes_applied++;
            }
        }
        
        // إنشاء index.php
        $index_file = $dir . '/index.php';
        if (!file_exists($index_file)) {
            if (file_put_contents($index_file, $index_content)) {
                echo "   ✓ تم إنشاء حماية إضافية: $index_file\n";
                $fixes_applied++;
            }
        }
    }
}

// 5. إنشاء ملف robots.txt
echo "\n5. إنشاء ملف robots.txt:\n";
$robots_content = "User-agent: *\nDisallow: /config/\nDisallow: /logs/\nDisallow: /temp/\nDisallow: /backups/\nDisallow: /includes/\nDisallow: /models/\n";

if (!file_exists('robots.txt')) {
    if (file_put_contents('robots.txt', $robots_content)) {
        echo "   ✓ تم إنشاء ملف robots.txt\n";
        $fixes_applied++;
    }
} else {
    echo "   - ملف robots.txt موجود\n";
}

// 6. إنشاء ملف .gitignore
echo "\n6. إنشاء ملف .gitignore:\n";
$gitignore_content = "# Logs\nlogs/\n*.log\n\n# Uploads\nuploads/\n\n# Temporary files\ntemp/\n\n# Backups\nbackups/\n\n# Configuration (if contains sensitive data)\n# config/database.php\n\n# IDE files\n.vscode/\n.idea/\n*.swp\n*.swo\n\n# OS files\n.DS_Store\nThumbs.db\n";

if (!file_exists('.gitignore')) {
    if (file_put_contents('.gitignore', $gitignore_content)) {
        echo "   ✓ تم إنشاء ملف .gitignore\n";
        $fixes_applied++;
    }
} else {
    echo "   - ملف .gitignore موجود\n";
}

// 7. إنشاء ملف error_log فارغ
echo "\n7. إنشاء ملف سجل الأخطاء:\n";
if (!file_exists('logs/error.log')) {
    if (file_put_contents('logs/error.log', '')) {
        echo "   ✓ تم إنشاء ملف logs/error.log\n";
        $fixes_applied++;
    }
} else {
    echo "   - ملف logs/error.log موجود\n";
}

// 8. التحقق من صلاحيات الملفات
echo "\n8. إصلاح صلاحيات الملفات:\n";
$writable_dirs = array('logs', 'uploads', 'temp', 'backups');

foreach ($writable_dirs as $dir) {
    if (is_dir($dir)) {
        if (chmod($dir, 0755)) {
            echo "   ✓ تم إصلاح صلاحيات: $dir\n";
            $fixes_applied++;
        } else {
            echo "   ✗ فشل في إصلاح صلاحيات: $dir\n";
            $errors[] = "فشل في إصلاح صلاحيات: $dir";
        }
    }
}

// 9. إنشاء ملف اختبار قاعدة البيانات
echo "\n9. إنشاء ملف اختبار قاعدة البيانات:\n";
$db_test_content = '<?php
// اختبار الاتصال بقاعدة البيانات
require_once "config/database.php";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS
    );
    
    echo "✓ الاتصال بقاعدة البيانات نجح\n";
    
    // اختبار جدول المستخدمين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "✓ عدد المستخدمين: " . $result["count"] . "\n";
    
} catch (Exception $e) {
    echo "✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
}
?>';

if (file_put_contents('test_database_simple.php', $db_test_content)) {
    echo "   ✓ تم إنشاء ملف اختبار قاعدة البيانات\n";
    $fixes_applied++;
}

// النتائج النهائية
echo "\n" . str_repeat("=", 50) . "\n";
echo "تم الانتهاء من الإصلاح الشامل!\n\n";

echo "الإحصائيات:\n";
echo "- عدد الإصلاحات المطبقة: $fixes_applied\n";
echo "- عدد الأخطاء: " . count($errors) . "\n\n";

if (!empty($errors)) {
    echo "الأخطاء المتبقية:\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
    echo "\n";
}

echo "الخطوات التالية:\n";
echo "1. قم بزيارة: test_web.php في المتصفح\n";
echo "2. اختبر الاتصال: test_database_simple.php\n";
echo "3. ابدأ النظام: start_system.php\n";
echo "4. سجل الدخول: login.php\n\n";

echo "في حالة استمرار المشاكل:\n";
echo "• تحقق من سجل الأخطاء: logs/error.log\n";
echo "• تأكد من إعدادات Apache و MySQL\n";
echo "• راجع ملف README.md\n";

echo "\nتم بنجاح! ✓\n";
?>
