/**
 * ملف JavaScript مخصص لنظام إدارة المطعم
 * Custom JavaScript for Restaurant Management System
 */

// إعدادات عامة
const AppConfig = {
    currency: 'SAR',
    currencySymbol: 'ر.س',
    dateFormat: 'dd/mm/yyyy',
    timeFormat: '24h',
    language: 'ar'
};

// دوال مساعدة عامة
const Utils = {
    // تنسيق العملة
    formatCurrency: function(amount) {
        if (isNaN(amount)) return '0.00 ' + AppConfig.currencySymbol;
        
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: AppConfig.currency,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    },

    // تنسيق الأرقام
    formatNumber: function(number) {
        if (isNaN(number)) return '0';
        return new Intl.NumberFormat('ar-SA').format(number);
    },

    // تنسيق التاريخ
    formatDate: function(date, format = 'dd/mm/yyyy') {
        if (!date) return '';
        
        const d = new Date(date);
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        
        switch (format) {
            case 'dd/mm/yyyy':
                return `${day}/${month}/${year}`;
            case 'dd/mm/yyyy hh:mm':
                return `${day}/${month}/${year} ${hours}:${minutes}`;
            case 'hh:mm':
                return `${hours}:${minutes}`;
            default:
                return d.toLocaleDateString('ar-SA');
        }
    },

    // إظهار رسالة نجاح
    showSuccess: function(title, message) {
        Swal.fire({
            title: title,
            text: message,
            icon: 'success',
            confirmButtonText: 'موافق'
        });
    },

    // إظهار رسالة خطأ
    showError: function(title, message) {
        Swal.fire({
            title: title,
            text: message,
            icon: 'error',
            confirmButtonText: 'موافق'
        });
    },

    // إظهار رسالة تأكيد
    showConfirm: function(title, message, callback) {
        Swal.fire({
            title: title,
            text: message,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed && callback) {
                callback();
            }
        });
    },

    // إظهار مؤشر التحميل
    showLoading: function(message = 'جاري التحميل...') {
        Swal.fire({
            title: message,
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    },

    // إخفاء مؤشر التحميل
    hideLoading: function() {
        Swal.close();
    },

    // تحديث الوقت المنقضي
    updateElapsedTime: function(startTime, elementId) {
        const start = new Date(startTime);
        const now = new Date();
        const elapsed = Math.floor((now - start) / 1000);
        
        const hours = Math.floor(elapsed / 3600);
        const minutes = Math.floor((elapsed % 3600) / 60);
        
        const timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
        
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = timeString;
        }
        
        return timeString;
    },

    // تحديث الصفحة بعد تأخير
    reloadPage: function(delay = 1000) {
        setTimeout(() => {
            location.reload();
        }, delay);
    },

    // إعادة توجيه إلى صفحة أخرى
    redirect: function(url, delay = 0) {
        setTimeout(() => {
            window.location.href = url;
        }, delay);
    }
};

// إدارة الطلبات
const OrderManager = {
    // تحديث حالة الطلب
    updateStatus: function(orderId, status, callback) {
        Utils.showLoading('جاري تحديث حالة الطلب...');
        
        $.ajax({
            url: 'ajax/update_order_status.php',
            type: 'POST',
            data: {
                order_id: orderId,
                status: status
            },
            dataType: 'json',
            success: function(response) {
                Utils.hideLoading();
                if (response.success) {
                    Utils.showSuccess('تم التحديث', response.message);
                    if (callback) callback(response);
                } else {
                    Utils.showError('خطأ', response.message);
                }
            },
            error: function() {
                Utils.hideLoading();
                Utils.showError('خطأ', 'حدث خطأ في الاتصال');
            }
        });
    },

    // طباعة الفاتورة
    printReceipt: function(orderId) {
        const printWindow = window.open(
            `../print/receipt.php?id=${orderId}`,
            'receipt',
            'width=400,height=600,scrollbars=yes'
        );
        
        printWindow.onload = function() {
            printWindow.print();
        };
    },

    // طباعة تذكرة المطبخ
    printKitchenTicket: function(orderId) {
        const printWindow = window.open(
            `../print/kitchen_ticket.php?id=${orderId}`,
            'kitchen_ticket',
            'width=400,height=600,scrollbars=yes'
        );
        
        printWindow.onload = function() {
            printWindow.print();
        };
    }
};

// إدارة الطاولات
const TableManager = {
    // تحديث حالة الطاولة
    updateStatus: function(tableId, status, callback) {
        Utils.showConfirm(
            'تحديث حالة الطاولة',
            `هل تريد تغيير حالة الطاولة؟`,
            function() {
                $.ajax({
                    url: 'ajax/update_table_status.php',
                    type: 'POST',
                    data: {
                        table_id: tableId,
                        status: status
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            Utils.showSuccess('تم التحديث', response.message);
                            if (callback) callback(response);
                        } else {
                            Utils.showError('خطأ', response.message);
                        }
                    },
                    error: function() {
                        Utils.showError('خطأ', 'حدث خطأ في الاتصال');
                    }
                });
            }
        );
    },

    // تحديث حالة الطاولات تلقائياً
    autoUpdateStatus: function() {
        $.ajax({
            url: 'ajax/get_tables_status.php',
            type: 'GET',
            dataType: 'json',
            success: function(tables) {
                tables.forEach(function(table) {
                    const tableCard = $(`[data-table-id="${table.id}"]`);
                    
                    if (tableCard.length) {
                        // تحديث الحالة
                        tableCard.removeClass('available occupied reserved maintenance')
                                 .addClass(table.status);
                        
                        // تحديث معلومات الجلسة
                        if (table.status === 'occupied' && table.session_info) {
                            let sessionInfo = `
                                <div class="session-time">${table.session_info.elapsed_time}</div>
                                <div class="session-orders">${table.session_info.active_orders} طلب</div>
                            `;
                            if (table.session_info.total && table.session_info.total !== '0.00 ر.س') {
                                sessionInfo += `<div class="session-total">${table.session_info.total}</div>`;
                            }
                            
                            tableCard.find('.table-session-info').html(sessionInfo);
                        } else {
                            tableCard.find('.table-session-info').empty();
                        }
                    }
                });
            }
        });
    }
};

// إدارة النماذج
const FormManager = {
    // التحقق من صحة النموذج
    validate: function(formId, rules) {
        const form = document.getElementById(formId);
        if (!form) return false;
        
        let isValid = true;
        const errors = [];
        
        for (const field in rules) {
            const element = form.querySelector(`[name="${field}"]`);
            if (!element) continue;
            
            const rule = rules[field];
            const value = element.value.trim();
            
            // التحقق من الحقول المطلوبة
            if (rule.required && !value) {
                isValid = false;
                errors.push(`${rule.label} مطلوب`);
                element.classList.add('is-invalid');
            } else {
                element.classList.remove('is-invalid');
            }
            
            // التحقق من الحد الأدنى للطول
            if (rule.minLength && value.length < rule.minLength) {
                isValid = false;
                errors.push(`${rule.label} يجب أن يكون ${rule.minLength} أحرف على الأقل`);
                element.classList.add('is-invalid');
            }
            
            // التحقق من البريد الإلكتروني
            if (rule.email && value && !this.isValidEmail(value)) {
                isValid = false;
                errors.push(`${rule.label} غير صحيح`);
                element.classList.add('is-invalid');
            }
            
            // التحقق من رقم الهاتف
            if (rule.phone && value && !this.isValidPhone(value)) {
                isValid = false;
                errors.push(`${rule.label} غير صحيح`);
                element.classList.add('is-invalid');
            }
        }
        
        if (!isValid) {
            Utils.showError('خطأ في البيانات', errors.join('\n'));
        }
        
        return isValid;
    },

    // التحقق من صحة البريد الإلكتروني
    isValidEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // التحقق من صحة رقم الهاتف
    isValidPhone: function(phone) {
        const phoneRegex = /^[0-9+\-\s()]+$/;
        return phoneRegex.test(phone) && phone.length >= 10;
    },

    // تنظيف النموذج
    reset: function(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.reset();
            form.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
        }
    }
};

// إدارة الإشعارات
const NotificationManager = {
    // إظهار إشعار
    show: function(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },

    // إشعار نجاح
    success: function(message, duration = 5000) {
        this.show(message, 'success', duration);
    },

    // إشعار خطأ
    error: function(message, duration = 5000) {
        this.show(message, 'danger', duration);
    },

    // إشعار تحذير
    warning: function(message, duration = 5000) {
        this.show(message, 'warning', duration);
    },

    // إشعار معلومات
    info: function(message, duration = 5000) {
        this.show(message, 'info', duration);
    }
};

// تهيئة النظام عند تحميل الصفحة
$(document).ready(function() {
    // تفعيل tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // تفعيل popovers
    $('[data-bs-toggle="popover"]').popover();
    
    // تحديث الوقت كل دقيقة
    setInterval(function() {
        $('.elapsed-time').each(function() {
            const startTime = $(this).data('start-time');
            if (startTime) {
                Utils.updateElapsedTime(startTime, this.id);
            }
        });
    }, 60000);
    
    // تحديث حالة الطاولات كل 30 ثانية
    if (window.location.pathname.includes('/tables/')) {
        setInterval(function() {
            if (!document.hidden) {
                TableManager.autoUpdateStatus();
            }
        }, 30000);
    }
    
    // تحسين تجربة المستخدم للنماذج
    $('form').on('submit', function(e) {
        const form = this;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            
            // إعادة تفعيل الزر بعد 5 ثوان كحد أقصى
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || 'حفظ';
            }, 5000);
        }
    });
    
    // حفظ النص الأصلي للأزرار
    $('button[type="submit"]').each(function() {
        $(this).attr('data-original-text', $(this).html());
    });
});

// تصدير الدوال للاستخدام العام
window.Utils = Utils;
window.OrderManager = OrderManager;
window.TableManager = TableManager;
window.FormManager = FormManager;
window.NotificationManager = NotificationManager;
