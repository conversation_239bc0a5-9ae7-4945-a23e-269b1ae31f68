<?php
/**
 * نقاط البيع - الواجهة الرئيسية
 * POS - Main Interface
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Product.php';
require_once '../../models/Customer.php';
require_once '../../models/Order.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('orders.create');

// التحقق من وجود وردية نشطة
if (!hasActiveShift()) {
    $_SESSION['error'] = 'يجب بدء وردية أولاً قبل استخدام نقاط البيع';
    redirect('../../views/shifts/start.php');
}

$product_model = new Product();
$customer_model = new Customer();
$order_model = new Order();

// الحصول على الفئات والمنتجات
$categories = DB::select("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order");
$products = $product_model->getAll();

// تجميع المنتجات حسب الفئة
$products_by_category = array();
foreach ($products as $product) {
    $products_by_category[$product['category_id']][] = $product;
}

// الحصول على الطاولات المتاحة
$tables = DB::select("SELECT * FROM tables WHERE status = 'available' ORDER BY table_number");

$page_title = 'نقاط البيع';
$additional_css = '
<style>
    .pos-container {
        height: calc(100vh - 100px);
        overflow: hidden;
    }
    
    .pos-left {
        height: 100%;
        overflow-y: auto;
        border-left: 1px solid #dee2e6;
    }
    
    .pos-right {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .cart-items {
        flex: 1;
        overflow-y: auto;
        max-height: 400px;
    }
    
    .category-tabs {
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 1rem;
    }
    
    .category-tab {
        padding: 0.75rem 1rem;
        border: none;
        background: none;
        cursor: pointer;
        border-bottom: 3px solid transparent;
        transition: all 0.2s;
    }
    
    .category-tab.active {
        border-bottom-color: #0d6efd;
        color: #0d6efd;
        font-weight: 600;
    }
    
    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }
    
    .product-card {
        border: 2px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
        background: white;
    }
    
    .product-card:hover {
        border-color: #0d6efd;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .product-image {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 0.25rem;
        margin-bottom: 0.5rem;
    }
    
    .product-name {
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }
    
    .product-price {
        color: #198754;
        font-weight: 600;
    }
    
    .cart-item {
        border-bottom: 1px solid #dee2e6;
        padding: 0.75rem;
    }
    
    .cart-item:last-child {
        border-bottom: none;
    }
    
    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .quantity-btn {
        width: 30px;
        height: 30px;
        border: 1px solid #dee2e6;
        background: white;
        border-radius: 0.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    
    .quantity-input {
        width: 60px;
        text-align: center;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 0.25rem;
    }
    
    .order-summary {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-top: 1rem;
    }
    
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }
    
    .summary-total {
        font-size: 1.25rem;
        font-weight: 700;
        border-top: 2px solid #dee2e6;
        padding-top: 0.5rem;
        margin-top: 0.5rem;
    }
</style>
';

include '../layouts/header.php';
?>

<div class="container-fluid pos-container">
    <div class="row h-100">
        <!-- قائمة المنتجات -->
        <div class="col-md-8 pos-left">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4>المنتجات</h4>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="showCustomerModal()">
                        <i class="fas fa-user-plus"></i> عميل
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="showTableModal()">
                        <i class="fas fa-chair"></i> طاولة
                    </button>
                </div>
            </div>
            
            <!-- تبويبات الفئات -->
            <div class="category-tabs">
                <button class="category-tab active" onclick="showCategory('all')">الكل</button>
                <?php foreach ($categories as $category): ?>
                <button class="category-tab" onclick="showCategory(<?php echo $category['id']; ?>)">
                    <?php echo $category['name_ar']; ?>
                </button>
                <?php endforeach; ?>
            </div>
            
            <!-- شبكة المنتجات -->
            <div id="products-container">
                <div class="product-grid" id="category-all">
                    <?php foreach ($products as $product): ?>
                    <div class="product-card" onclick="addToCart(<?php echo $product['id']; ?>)">
                        <?php if ($product['image']): ?>
                        <img src="../../uploads/<?php echo $product['image']; ?>" 
                             alt="<?php echo $product['name_ar']; ?>" class="product-image">
                        <?php else: ?>
                        <div class="product-image bg-light d-flex align-items-center justify-content-center">
                            <i class="fas fa-utensils text-muted"></i>
                        </div>
                        <?php endif; ?>
                        <div class="product-name"><?php echo $product['name_ar']; ?></div>
                        <div class="product-price"><?php echo formatCurrency($product['base_price']); ?></div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <?php foreach ($categories as $category): ?>
                <div class="product-grid" id="category-<?php echo $category['id']; ?>" style="display: none;">
                    <?php if (isset($products_by_category[$category['id']])): ?>
                        <?php foreach ($products_by_category[$category['id']] as $product): ?>
                        <div class="product-card" onclick="addToCart(<?php echo $product['id']; ?>)">
                            <?php if ($product['image']): ?>
                            <img src="../../uploads/<?php echo $product['image']; ?>" 
                                 alt="<?php echo $product['name_ar']; ?>" class="product-image">
                            <?php else: ?>
                            <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                <i class="fas fa-utensils text-muted"></i>
                            </div>
                            <?php endif; ?>
                            <div class="product-name"><?php echo $product['name_ar']; ?></div>
                            <div class="product-price"><?php echo formatCurrency($product['base_price']); ?></div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- سلة الطلبات -->
        <div class="col-md-4 pos-right">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الطلب الحالي</h5>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearCart()">
                        <i class="fas fa-trash"></i> مسح
                    </button>
                </div>
                
                <div class="card-body p-0">
                    <!-- معلومات الطلب -->
                    <div class="p-3 border-bottom">
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">نوع الطلب</label>
                                <select class="form-select form-select-sm" id="order-type">
                                    <option value="dine_in">طاولة</option>
                                    <option value="takeaway">سفري</option>
                                    <option value="delivery">توصيل</option>
                                </select>
                            </div>
                            <div class="col-6">
                                <label class="form-label">العميل</label>
                                <div class="d-flex">
                                    <input type="text" class="form-control form-control-sm" 
                                           id="customer-name" placeholder="اسم العميل" readonly>
                                    <input type="hidden" id="customer-id">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-2" id="table-row" style="display: none;">
                            <div class="col-12">
                                <label class="form-label">الطاولة</label>
                                <input type="text" class="form-control form-control-sm" 
                                       id="table-name" placeholder="رقم الطاولة" readonly>
                                <input type="hidden" id="table-session-id">
                            </div>
                        </div>
                    </div>
                    
                    <!-- عناصر السلة -->
                    <div class="cart-items" id="cart-items">
                        <div class="text-center text-muted p-4">
                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                            <p>لا توجد عناصر في السلة</p>
                        </div>
                    </div>
                </div>
                
                <!-- ملخص الطلب -->
                <div class="card-footer">
                    <div class="order-summary">
                        <div class="summary-row">
                            <span>المجموع الفرعي:</span>
                            <span id="subtotal">0.00 ر.س</span>
                        </div>
                        <div class="summary-row">
                            <span>الضريبة (15%):</span>
                            <span id="tax-amount">0.00 ر.س</span>
                        </div>
                        <div class="summary-row">
                            <span>رسوم الخدمة (10%):</span>
                            <span id="service-charge">0.00 ر.س</span>
                        </div>
                        <div class="summary-row summary-total">
                            <span>الإجمالي:</span>
                            <span id="total-amount">0.00 ر.س</span>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 mt-3">
                        <button type="button" class="btn btn-success btn-lg" onclick="processOrder()">
                            <i class="fas fa-check"></i> تأكيد الطلب
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="holdOrder()">
                            <i class="fas fa-pause"></i> تعليق الطلب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة اختيار العميل -->
<div class="modal fade" id="customerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اختيار العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="customer-search" 
                           placeholder="البحث برقم الهاتف أو الاسم">
                </div>
                <div id="customer-results"></div>
                
                <hr>
                
                <h6>إضافة عميل جديد</h6>
                <form id="new-customer-form">
                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="name" placeholder="الاسم" required>
                        </div>
                        <div class="col-md-6">
                            <input type="tel" class="form-control" name="phone" placeholder="رقم الهاتف" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary mt-2">إضافة</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نافذة اختيار الطاولة -->
<div class="modal fade" id="tableModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اختيار الطاولة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <?php foreach ($tables as $table): ?>
                    <div class="col-md-4 mb-3">
                        <div class="card table-card" onclick="selectTable(<?php echo $table['id']; ?>, '<?php echo $table['table_number']; ?>')">
                            <div class="card-body text-center">
                                <i class="fas fa-chair fa-2x mb-2"></i>
                                <h6><?php echo $table['table_number']; ?></h6>
                                <small class="text-muted"><?php echo $table['capacity']; ?> أشخاص</small>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$additional_js = '
<script src="pos.js"></script>
';
include '../layouts/footer.php';
?>
