<?php
/**
 * إضافة مستخدم جديد
 * Add New User
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/User.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('users.create');

$user_model = new User();
$error = '';
$success = '';

// معالجة إضافة المستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize(isset($_POST['username']) ? $_POST['username'] : '');
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
    $full_name = sanitize(isset($_POST['full_name']) ? $_POST['full_name'] : '');
    $email = sanitize(isset($_POST['email']) ? $_POST['email'] : '');
    $phone = sanitize(isset($_POST['phone']) ? $_POST['phone'] : '');
    $role_id = intval(isset($_POST['role_id']) ? $_POST['role_id'] : 0);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // التحقق من البيانات المطلوبة
    if (empty($username)) {
        $error = 'اسم المستخدم مطلوب';
    } elseif (empty($password)) {
        $error = 'كلمة المرور مطلوبة';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } elseif (empty($full_name)) {
        $error = 'الاسم الكامل مطلوب';
    } elseif ($role_id == 0) {
        $error = 'يجب اختيار دور للمستخدم';
    } else {
        // التحقق من عدم وجود اسم المستخدم مسبقاً
        $existing_user = DB::selectOne("SELECT id FROM users WHERE username = ?", array($username));
        if ($existing_user) {
            $error = 'اسم المستخدم موجود مسبقاً';
        } else {
            // التحقق من البريد الإلكتروني إذا تم إدخاله
            if (!empty($email)) {
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $error = 'البريد الإلكتروني غير صحيح';
                } else {
                    $existing_email = DB::selectOne("SELECT id FROM users WHERE email = ?", array($email));
                    if ($existing_email) {
                        $error = 'البريد الإلكتروني موجود مسبقاً';
                    }
                }
            }
            
            if (empty($error)) {
                // إنشاء المستخدم
                $user_data = array(
                    'username' => $username,
                    'password' => password_hash($password, PASSWORD_DEFAULT),
                    'full_name' => $full_name,
                    'email' => $email,
                    'phone' => $phone,
                    'role_id' => $role_id,
                    'is_active' => $is_active
                );
                
                $user_id = $user_model->create($user_data);
                if ($user_id) {
                    $success = 'تم إضافة المستخدم بنجاح';
                    logActivity('create_user', 'users', $user_id, null, $user_data);
                    
                    // إعادة تعيين النموذج
                    $_POST = array();
                } else {
                    $error = 'فشل في إضافة المستخدم';
                }
            }
        }
    }
}

// الحصول على الأدوار
$roles = DB::select("SELECT * FROM roles WHERE is_active = 1 ORDER BY name_ar");

$page_title = 'إضافة مستخدم جديد';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إضافة مستخدم جديد</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="list.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للقائمة
                    </a>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                    <div class="mt-2">
                        <a href="list.php" class="btn btn-sm btn-success">عرض قائمة المستخدمين</a>
                        <a href="add.php" class="btn btn-sm btn-primary">إضافة مستخدم آخر</a>
                    </div>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                بيانات المستخدم الجديد
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="addUserForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="username" name="username" 
                                                   value="<?php echo htmlspecialchars(isset($_POST['username']) ? $_POST['username'] : ''); ?>" 
                                                   required maxlength="50">
                                            <div class="form-text">يجب أن يكون فريداً ولا يحتوي على مسافات</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                                   value="<?php echo htmlspecialchars(isset($_POST['full_name']) ? $_POST['full_name'] : ''); ?>" 
                                                   required maxlength="100">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="password" name="password" 
                                                       required minlength="6">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                                    <i class="fas fa-eye" id="password-icon"></i>
                                                </button>
                                            </div>
                                            <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                       required minlength="6">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                                    <i class="fas fa-eye" id="confirm_password-icon"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars(isset($_POST['email']) ? $_POST['email'] : ''); ?>" 
                                                   maxlength="100">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo htmlspecialchars(isset($_POST['phone']) ? $_POST['phone'] : ''); ?>" 
                                                   maxlength="20">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="role_id" class="form-label">الدور <span class="text-danger">*</span></label>
                                            <select class="form-select" id="role_id" name="role_id" required>
                                                <option value="">اختر الدور</option>
                                                <?php foreach ($roles as $role): ?>
                                                <option value="<?php echo $role['id']; ?>" 
                                                        <?php echo (isset($_POST['role_id']) ? $_POST['role_id'] : '') == $role['id'] ? 'selected' : ''; ?>>
                                                    <?php echo $role['name_ar']; ?>
                                                    <?php if ($role['description']): ?>
                                                        - <?php echo $role['description']; ?>
                                                    <?php endif; ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                                       value="1" <?php echo (isset($_POST['is_active']) || !isset($_POST['username'])) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="is_active">
                                                    مستخدم نشط
                                                </label>
                                                <div class="form-text">المستخدمون غير النشطين لا يمكنهم تسجيل الدخول</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="list.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ المستخدم
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات مهمة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6>نصائح لإنشاء مستخدم آمن:</h6>
                                <ul class="mb-0">
                                    <li>استخدم اسم مستخدم فريد</li>
                                    <li>اختر كلمة مرور قوية</li>
                                    <li>حدد الدور المناسب للمستخدم</li>
                                    <li>تأكد من صحة البريد الإلكتروني</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6>الأدوار المتاحة:</h6>
                                <ul class="mb-0">
                                    <?php foreach ($roles as $role): ?>
                                    <li><strong><?php echo $role['name_ar']; ?></strong>
                                        <?php if ($role['description']): ?>
                                            <br><small class="text-muted"><?php echo $role['description']; ?></small>
                                        <?php endif; ?>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
$additional_js = '
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + "-icon");
    
    if (field.type === "password") {
        field.type = "text";
        icon.classList.remove("fa-eye");
        icon.classList.add("fa-eye-slash");
    } else {
        field.type = "password";
        icon.classList.remove("fa-eye-slash");
        icon.classList.add("fa-eye");
    }
}

// التحقق من تطابق كلمات المرور
document.getElementById("confirm_password").addEventListener("input", function() {
    const password = document.getElementById("password").value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity("كلمة المرور غير متطابقة");
        this.classList.add("is-invalid");
    } else {
        this.setCustomValidity("");
        this.classList.remove("is-invalid");
        this.classList.add("is-valid");
    }
});

// التحقق من قوة كلمة المرور
document.getElementById("password").addEventListener("input", function() {
    const password = this.value;
    const confirmPassword = document.getElementById("confirm_password");
    
    // إعادة التحقق من تطابق كلمة المرور
    if (confirmPassword.value && password !== confirmPassword.value) {
        confirmPassword.setCustomValidity("كلمة المرور غير متطابقة");
        confirmPassword.classList.add("is-invalid");
    } else if (confirmPassword.value) {
        confirmPassword.setCustomValidity("");
        confirmPassword.classList.remove("is-invalid");
        confirmPassword.classList.add("is-valid");
    }
});

// التحقق من اسم المستخدم
document.getElementById("username").addEventListener("input", function() {
    const username = this.value;
    
    // إزالة المسافات
    if (username.includes(" ")) {
        this.value = username.replace(/\s/g, "");
    }
    
    // التحقق من الأحرف المسموحة
    const validPattern = /^[a-zA-Z0-9_.-]+$/;
    if (username && !validPattern.test(username)) {
        this.setCustomValidity("اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط");
        this.classList.add("is-invalid");
    } else {
        this.setCustomValidity("");
        this.classList.remove("is-invalid");
    }
});

// تأكيد الإرسال
document.getElementById("addUserForm").addEventListener("submit", function(e) {
    e.preventDefault();
    
    Swal.fire({
        title: "إضافة مستخدم جديد",
        text: "هل أنت متأكد من إضافة هذا المستخدم؟",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "نعم، أضف المستخدم",
        cancelButtonText: "إلغاء"
    }).then((result) => {
        if (result.isConfirmed) {
            this.submit();
        }
    });
});
</script>
';

include '../layouts/footer.php';
?>
