<?php
/**
 * ملف تهيئة النظام
 * System Initialization
 * Restaurant Management System
 */

// منع الوصول المباشر
if (!defined('SYSTEM_INIT')) {
    define('SYSTEM_INIT', true);
}

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تحديد مسار الجذر
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(__FILE__)) . '/');
}

// تضمين ملفات التكوين
require_once ROOT_PATH . 'config/database.php';
require_once ROOT_PATH . 'config/constants.php';

// تضمين الملفات الأساسية
require_once ROOT_PATH . 'includes/functions.php';
require_once ROOT_PATH . 'includes/auth.php';

// تهيئة قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
        ]
    );
    
    // تعيين متغير قاعدة البيانات العام
    $GLOBALS['pdo'] = $pdo;
    
} catch (PDOException $e) {
    // في حالة فشل الاتصال بقاعدة البيانات
    if (DEBUG_MODE) {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("خطأ في الاتصال بقاعدة البيانات");
    }
}

// تهيئة فئة قاعدة البيانات
class DB {
    private static $pdo = null;
    
    public static function init() {
        if (self::$pdo === null) {
            self::$pdo = $GLOBALS['pdo'];
        }
    }
    
    public static function select($sql, $params = array()) {
        self::init();
        try {
            $stmt = self::$pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            logError("Database select error: " . $e->getMessage());
            return array();
        }
    }
    
    public static function selectOne($sql, $params = []) {
        self::init();
        try {
            $stmt = self::$pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            logError("Database selectOne error: " . $e->getMessage());
            return false;
        }
    }
    
    public static function execute($sql, $params = []) {
        self::init();
        try {
            $stmt = self::$pdo->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            logError("Database execute error: " . $e->getMessage());
            return false;
        }
    }
    
    public static function insert($sql, $params = []) {
        self::init();
        try {
            $stmt = self::$pdo->prepare($sql);
            if ($stmt->execute($params)) {
                return self::$pdo->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            logError("Database insert error: " . $e->getMessage());
            return false;
        }
    }
    
    public static function beginTransaction() {
        self::init();
        return self::$pdo->beginTransaction();
    }
    
    public static function commit() {
        self::init();
        return self::$pdo->commit();
    }
    
    public static function rollback() {
        self::init();
        return self::$pdo->rollback();
    }
    
    public static function lastInsertId() {
        self::init();
        return self::$pdo->lastInsertId();
    }
}

// تحميل الإعدادات من قاعدة البيانات
function loadSettings() {
    $settings = [];
    try {
        $results = DB::select("SELECT setting_key, setting_value FROM settings");
        foreach ($results as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    } catch (Exception $e) {
        // في حالة عدم وجود جدول الإعدادات بعد
        logError("Settings table not found: " . $e->getMessage());
    }
    
    return $settings;
}

// الحصول على قيمة إعداد
function getSetting($key, $default = '') {
    static $settings = null;
    
    if ($settings === null) {
        $settings = loadSettings();
    }
    
    return isset($settings[$key]) ? $settings[$key] : $default;
}

// حفظ إعداد
function setSetting($key, $value) {
    $sql = "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
    return DB::execute($sql, [$key, $value]);
}

// تسجيل الأخطاء
function logError($message, $file = '', $line = '') {
    $log_message = date('Y-m-d H:i:s') . " - ERROR: " . $message;
    if ($file) {
        $log_message .= " in " . $file;
    }
    if ($line) {
        $log_message .= " on line " . $line;
    }
    $log_message .= "\n";
    
    $log_file = ROOT_PATH . 'logs/error.log';
    
    // إنشاء مجلد logs إذا لم يكن موجوداً
    if (!is_dir(dirname($log_file))) {
        mkdir(dirname($log_file), 0755, true);
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
}

// تسجيل النشاط
function logActivity($action, $table_name = '', $record_id = null, $old_data = null, $new_data = null) {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    $sql = "INSERT INTO activity_log (user_id, action, table_name, record_id, old_data, new_data, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $params = [
        $_SESSION['user_id'],
        $action,
        $table_name,
        $record_id,
        $old_data ? json_encode($old_data) : null,
        $new_data ? json_encode($new_data) : null,
        isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '',
        isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''
    ];
    
    return DB::execute($sql, $params);
}

// التحقق من وضع الصيانة
function checkMaintenanceMode() {
    if (getSetting('maintenance_mode', MAINTENANCE_MODE)) {
        // السماح للمديرين بالوصول
        if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
            $message = getSetting('maintenance_message', MAINTENANCE_MESSAGE);
            die('
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>وضع الصيانة</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .maintenance { background: #f8f9fa; padding: 30px; border-radius: 10px; max-width: 500px; margin: 0 auto; }
                    .icon { font-size: 4em; color: #ffc107; margin-bottom: 20px; }
                </style>
            </head>
            <body>
                <div class="maintenance">
                    <div class="icon">🔧</div>
                    <h1>وضع الصيانة</h1>
                    <p>' . htmlspecialchars($message) . '</p>
                </div>
            </body>
            </html>
            ');
        }
    }
}

// تهيئة معالج الأخطاء
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    logError($message, $file, $line);
    
    if (DEBUG_MODE) {
        echo "<b>Error:</b> $message in <b>$file</b> on line <b>$line</b><br>";
    }
    
    return true;
});

// تهيئة معالج الاستثناءات
set_exception_handler(function($exception) {
    logError("Uncaught exception: " . $exception->getMessage(), $exception->getFile(), $exception->getLine());
    
    if (DEBUG_MODE) {
        echo "<b>Uncaught exception:</b> " . $exception->getMessage() . "<br>";
        echo "<b>File:</b> " . $exception->getFile() . "<br>";
        echo "<b>Line:</b> " . $exception->getLine() . "<br>";
    } else {
        echo "حدث خطأ غير متوقع. يرجى المحاولة لاحقاً.";
    }
});

// تفعيل ضغط الإخراج إذا كان مدعوماً
if (ENABLE_GZIP && extension_loaded('zlib') && !ob_get_level()) {
    ob_start('ob_gzhandler');
}

// تحديد ترميز المحتوى
header('Content-Type: text/html; charset=UTF-8');

// إعدادات الأمان
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// التحقق من وضع الصيانة
checkMaintenanceMode();

// تنظيف البيانات المدخلة تلقائياً
if (get_magic_quotes_gpc()) {
    function stripslashes_deep($value) {
        return is_array($value) ? array_map('stripslashes_deep', $value) : stripslashes($value);
    }
    $_POST = stripslashes_deep($_POST);
    $_GET = stripslashes_deep($_GET);
    $_COOKIE = stripslashes_deep($_COOKIE);
}

// تهيئة متغيرات الجلسة الافتراضية
if (!isset($_SESSION['language'])) {
    $_SESSION['language'] = DEFAULT_LANGUAGE;
}

if (!isset($_SESSION['currency'])) {
    $_SESSION['currency'] = DEFAULT_CURRENCY;
}

// تحديث آخر نشاط للمستخدم
if (isset($_SESSION['user_id'])) {
    $sql = "UPDATE users SET last_activity = NOW() WHERE id = ?";
    DB::execute($sql, [$_SESSION['user_id']]);
    
    // التحقق من انتهاء الجلسة
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            session_destroy();
            header('Location: ' . SITE_URL . '/login.php?timeout=1');
            exit;
        }
    }
    $_SESSION['last_activity'] = time();
}

// تنظيف الملفات المؤقتة القديمة
if (rand(1, 100) == 1) { // 1% احتمال
    $temp_path = ROOT_PATH . PATH_TEMP;
    if (is_dir($temp_path)) {
        $files = glob($temp_path . '*');
        foreach ($files as $file) {
            if (is_file($file) && time() - filemtime($file) > 3600) { // ساعة واحدة
                unlink($file);
            }
        }
    }
}

// تحديد متغيرات عامة مفيدة
$GLOBALS['current_user'] = null;
if (isset($_SESSION['user_id'])) {
    $GLOBALS['current_user'] = DB::selectOne("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);
}

$GLOBALS['system_settings'] = loadSettings();

// إشارة إلى أن النظام تم تهيئته بنجاح
define('SYSTEM_INITIALIZED', true);
?>
