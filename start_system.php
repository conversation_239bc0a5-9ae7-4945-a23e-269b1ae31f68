<?php
/**
 * تشغيل سريع للنظام
 * Quick System Startup
 * Restaurant Management System
 */

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "<title>تشغيل النظام</title>\n";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>\n";
echo "<style>\n";
echo "body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }\n";
echo ".container { margin-top: 2rem; }\n";
echo ".card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }\n";
echo ".btn { border-radius: 25px; padding: 0.75rem 2rem; }\n";
echo ".status-success { color: #28a745; }\n";
echo ".status-error { color: #dc3545; }\n";
echo ".status-warning { color: #ffc107; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<div class='container'>\n";
echo "<div class='row justify-content-center'>\n";
echo "<div class='col-lg-8'>\n";

echo "<div class='card'>\n";
echo "<div class='card-header bg-primary text-white text-center'>\n";
echo "<h2><i class='fas fa-utensils me-2'></i>نظام إدارة المطعم</h2>\n";
echo "<p class='mb-0'>Restaurant Management System</p>\n";
echo "</div>\n";
echo "<div class='card-body'>\n";

// التحقق من المتطلبات الأساسية
echo "<h4><i class='fas fa-cogs me-2'></i>فحص المتطلبات الأساسية</h4>\n";

$requirements_ok = true;

// فحص PHP
$php_version = PHP_VERSION;
$php_ok = version_compare($php_version, '5.6.0', '>=');
echo "<div class='d-flex justify-content-between align-items-center mb-2'>\n";
echo "<span>إصدار PHP: $php_version</span>\n";
echo "<span class='" . ($php_ok ? 'status-success' : 'status-error') . "'>\n";
echo "<i class='fas fa-" . ($php_ok ? 'check' : 'times') . "'></i>\n";
echo "</span>\n";
echo "</div>\n";
if (!$php_ok) $requirements_ok = false;

// فحص الامتدادات
$extensions = array('pdo', 'pdo_mysql', 'json', 'mbstring');
foreach ($extensions as $ext) {
    $ext_ok = extension_loaded($ext);
    echo "<div class='d-flex justify-content-between align-items-center mb-2'>\n";
    echo "<span>امتداد $ext</span>\n";
    echo "<span class='" . ($ext_ok ? 'status-success' : 'status-error') . "'>\n";
    echo "<i class='fas fa-" . ($ext_ok ? 'check' : 'times') . "'></i>\n";
    echo "</span>\n";
    echo "</div>\n";
    if (!$ext_ok) $requirements_ok = false;
}

// فحص الملفات
$files = array('config/database.php', 'config/init.php', 'login.php', 'index.php');
foreach ($files as $file) {
    $file_ok = file_exists($file);
    echo "<div class='d-flex justify-content-between align-items-center mb-2'>\n";
    echo "<span>ملف $file</span>\n";
    echo "<span class='" . ($file_ok ? 'status-success' : 'status-error') . "'>\n";
    echo "<i class='fas fa-" . ($file_ok ? 'check' : 'times') . "'></i>\n";
    echo "</span>\n";
    echo "</div>\n";
    if (!$file_ok) $requirements_ok = false;
}

echo "<hr>\n";

// فحص قاعدة البيانات
echo "<h4><i class='fas fa-database me-2'></i>فحص قاعدة البيانات</h4>\n";

$db_ok = false;
try {
    require_once 'config/database.php';
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS
    );
    
    echo "<div class='d-flex justify-content-between align-items-center mb-2'>\n";
    echo "<span>الاتصال بقاعدة البيانات</span>\n";
    echo "<span class='status-success'><i class='fas fa-check'></i></span>\n";
    echo "</div>\n";
    
    // فحص الجداول الأساسية
    $tables = array('users', 'roles', 'customers', 'products', 'orders', 'tables', 'settings');
    $tables_ok = true;
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $table_exists = $stmt->rowCount() > 0;
        echo "<div class='d-flex justify-content-between align-items-center mb-2'>\n";
        echo "<span>جدول $table</span>\n";
        echo "<span class='" . ($table_exists ? 'status-success' : 'status-error') . "'>\n";
        echo "<i class='fas fa-" . ($table_exists ? 'check' : 'times') . "'></i>\n";
        echo "</span>\n";
        echo "</div>\n";
        if (!$table_exists) $tables_ok = false;
    }
    
    $db_ok = $tables_ok;
    
} catch (Exception $e) {
    echo "<div class='d-flex justify-content-between align-items-center mb-2'>\n";
    echo "<span>الاتصال بقاعدة البيانات</span>\n";
    echo "<span class='status-error'><i class='fas fa-times'></i></span>\n";
    echo "</div>\n";
    echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>\n";
}

echo "<hr>\n";

// النتيجة النهائية
if ($requirements_ok && $db_ok) {
    echo "<div class='alert alert-success text-center'>\n";
    echo "<h4><i class='fas fa-check-circle me-2'></i>النظام جاهز للتشغيل!</h4>\n";
    echo "<p class='mb-3'>جميع المتطلبات متوفرة ويمكنك الآن استخدام النظام</p>\n";
    
    echo "<div class='d-grid gap-2 d-md-block'>\n";
    echo "<a href='login.php' class='btn btn-success btn-lg'>\n";
    echo "<i class='fas fa-sign-in-alt me-2'></i>تسجيل الدخول\n";
    echo "</a>\n";
    echo "<a href='test_system.php' class='btn btn-info btn-lg'>\n";
    echo "<i class='fas fa-vial me-2'></i>اختبار شامل\n";
    echo "</a>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    echo "<div class='row mt-4'>\n";
    echo "<div class='col-md-6'>\n";
    echo "<div class='card bg-light'>\n";
    echo "<div class='card-body'>\n";
    echo "<h5><i class='fas fa-user-shield me-2'></i>بيانات تسجيل الدخول</h5>\n";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>\n";
    echo "<p><strong>كلمة المرور:</strong> admin123</p>\n";
    echo "<small class='text-muted'>يرجى تغيير كلمة المرور بعد تسجيل الدخول</small>\n";
    echo "</div>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    echo "<div class='col-md-6'>\n";
    echo "<div class='card bg-light'>\n";
    echo "<div class='card-body'>\n";
    echo "<h5><i class='fas fa-link me-2'></i>روابط مفيدة</h5>\n";
    echo "<ul class='list-unstyled'>\n";
    echo "<li><a href='views/dashboard/index.php'>لوحة التحكم</a></li>\n";
    echo "<li><a href='views/tables/index.php'>خريطة الطاولات</a></li>\n";
    echo "<li><a href='views/products/add.php'>إضافة منتج</a></li>\n";
    echo "<li><a href='views/users/list.php'>إدارة المستخدمين</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    echo "</div>\n";
    echo "</div>\n";
    echo "</div>\n";
    
} else {
    echo "<div class='alert alert-danger text-center'>\n";
    echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>يوجد مشاكل تحتاج إصلاح</h4>\n";
    echo "<p class='mb-3'>يرجى إصلاح المشاكل أعلاه قبل تشغيل النظام</p>\n";
    
    echo "<div class='d-grid gap-2 d-md-block'>\n";
    echo "<a href='quick_fix.php' class='btn btn-warning btn-lg'>\n";
    echo "<i class='fas fa-tools me-2'></i>إصلاح سريع\n";
    echo "</a>\n";
    echo "<a href='test_system.php' class='btn btn-info btn-lg'>\n";
    echo "<i class='fas fa-vial me-2'></i>اختبار مفصل\n";
    echo "</a>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    echo "<div class='alert alert-info'>\n";
    echo "<h5><i class='fas fa-info-circle me-2'></i>خطوات الإصلاح</h5>\n";
    echo "<ol>\n";
    echo "<li>تأكد من تشغيل Apache و MySQL</li>\n";
    echo "<li>تحقق من إعدادات قاعدة البيانات في config/database.php</li>\n";
    echo "<li>قم بتشغيل ملف quick_fix.php</li>\n";
    echo "<li>استورد ملف database.sql إذا لم تكن الجداول موجودة</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
}

echo "</div>\n"; // card-body
echo "</div>\n"; // card

echo "<div class='text-center mt-4'>\n";
echo "<small class='text-white'>نظام إدارة المطعم - الإصدار 1.0.1</small>\n";
echo "</div>\n";

echo "</div>\n"; // col
echo "</div>\n"; // row
echo "</div>\n"; // container

echo "</body>\n";
echo "</html>\n";
?>
