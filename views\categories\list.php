<?php
/**
 * قائمة فئات المنتجات
 * Product Categories List
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('products.view');

$error = '';
$success = '';

// معالجة الحذف
if (isset($_POST['action']) && $_POST['action'] === 'delete' && hasPermission('products.delete')) {
    $category_id = isset($_POST['category_id']) ? $_POST['category_id'] : null;
    if ($category_id) {
        // التحقق من وجود منتجات في الفئة
        $products_count = DB::selectOne("SELECT COUNT(*) as count FROM products WHERE category_id = ?", [$category_id]);
        
        if ($products_count['count'] > 0) {
            $error = 'لا يمكن حذف الفئة لأنها تحتوي على منتجات';
        } else {
            $sql = "UPDATE categories SET is_active = 0 WHERE id = ?";
            if (DB::execute($sql, [$category_id])) {
                logActivity('delete_category', 'categories', $category_id);
                $success = 'تم حذف الفئة بنجاح';
            } else {
                $error = 'فشل في حذف الفئة';
            }
        }
    }
}

// معالجة إضافة فئة جديدة
if (isset($_POST['action']) && $_POST['action'] === 'add' && hasPermission('products.create')) {
    $name = sanitize(isset($_POST['name']) ? $_POST['name'] : '');
    $name_ar = sanitize(isset($_POST['name_ar']) ? $_POST['name_ar'] : '');
    $description = sanitize(isset($_POST['description']) ? $_POST['description'] : '');
    $sort_order = intval(isset($_POST['sort_order']) ? $_POST['sort_order'] : 0);
    
    if (empty($name_ar)) {
        $error = 'اسم الفئة بالعربية مطلوب';
    } else {
        // معالجة رفع الصورة
        $image_filename = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] == UPLOAD_ERR_OK) {
            $upload_result = uploadFile($_FILES['image'], ['jpg', 'jpeg', 'png', 'gif']);
            if ($upload_result['success']) {
                $image_filename = $upload_result['filename'];
            } else {
                $error = $upload_result['error'];
            }
        }
        
        if (!$error) {
            $sql = "INSERT INTO categories (name, name_ar, description, image, sort_order) VALUES (?, ?, ?, ?, ?)";
            $category_id = DB::insert($sql, [$name, $name_ar, $description, $image_filename, $sort_order]);
            
            if ($category_id) {
                logActivity('create_category', 'categories', $category_id, null, [
                    'name' => $name,
                    'name_ar' => $name_ar
                ]);
                $success = 'تم إضافة الفئة بنجاح';
            } else {
                $error = 'فشل في إضافة الفئة';
            }
        }
    }
}

// الحصول على الفئات
$categories = DB::select("SELECT c.*, COUNT(p.id) as products_count 
                         FROM categories c 
                         LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
                         WHERE c.is_active = 1 
                         GROUP BY c.id 
                         ORDER BY c.sort_order, c.name_ar");

$page_title = 'إدارة فئات المنتجات';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة فئات المنتجات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <?php if (hasPermission('products.create')): ?>
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="fas fa-plus"></i> فئة جديدة
                        </button>
                        <?php endif; ?>
                        <a href="../products/list.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-box"></i> المنتجات
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <!-- بطاقات الفئات -->
            <div class="row">
                <?php foreach ($categories as $category): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 category-card">
                        <?php if ($category['image']): ?>
                        <img src="../../uploads/<?php echo $category['image']; ?>" 
                             class="card-img-top category-image" 
                             alt="<?php echo $category['name_ar']; ?>">
                        <?php else: ?>
                        <div class="card-img-top category-image-placeholder d-flex align-items-center justify-content-center">
                            <i class="fas fa-tags fa-3x text-muted"></i>
                        </div>
                        <?php endif; ?>
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo $category['name_ar']; ?></h5>
                            <?php if ($category['name']): ?>
                            <h6 class="card-subtitle mb-2 text-muted"><?php echo $category['name']; ?></h6>
                            <?php endif; ?>
                            
                            <?php if ($category['description']): ?>
                            <p class="card-text"><?php echo $category['description']; ?></p>
                            <?php endif; ?>
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="badge bg-primary"><?php echo $category['products_count']; ?> منتج</span>
                                    <small class="text-muted">ترتيب: <?php echo $category['sort_order']; ?></small>
                                </div>
                                
                                <div class="btn-group w-100">
                                    <a href="../products/list.php?category=<?php echo $category['id']; ?>" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> عرض المنتجات
                                    </a>
                                    <?php if (hasPermission('products.edit')): ?>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" 
                                            onclick="editCategory(<?php echo $category['id']; ?>)">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                    <?php endif; ?>
                                    <?php if (hasPermission('products.delete')): ?>
                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                            onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo addslashes($category['name_ar']); ?>')">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <?php if (empty($categories)): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-tags fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد فئات</h4>
                        <p class="text-muted">ابدأ بإضافة فئة جديدة لتنظيم منتجاتك</p>
                        <?php if (hasPermission('products.create')): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة فئة جديدة
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>

<!-- نافذة إضافة فئة جديدة -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة فئة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label for="name_ar" class="form-label">اسم الفئة بالعربية <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name_ar" name="name_ar" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الفئة بالإنجليزية</label>
                        <input type="text" class="form-control" id="name" name="name">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">صورة الفئة</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">الحد الأقصى: 5 ميجابايت. الأنواع المدعومة: JPG, PNG, GIF</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ترتيب العرض</label>
                        <input type="number" class="form-control" id="sort_order" name="sort_order" value="0" min="0">
                        <div class="form-text">الرقم الأصغر يظهر أولاً</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ الفئة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل الفئة -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الفئة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="editCategoryContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<?php
$additional_css = '
<style>
.category-card {
    transition: transform 0.2s;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.category-image {
    height: 200px;
    object-fit: cover;
}

.category-image-placeholder {
    height: 200px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
</style>
';

$additional_js = '
<script>
function editCategory(categoryId) {
    $.ajax({
        url: "ajax/get_category.php",
        type: "POST",
        data: { category_id: categoryId },
        success: function(response) {
            $("#editCategoryContent").html(response);
            $("#editCategoryModal").modal("show");
        }
    });
}

function deleteCategory(categoryId, categoryName) {
    Swal.fire({
        title: "حذف الفئة",
        text: "هل أنت متأكد من حذف الفئة: " + categoryName + "؟",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "نعم، احذف",
        cancelButtonText: "إلغاء"
    }).then((result) => {
        if (result.isConfirmed) {
            let form = document.createElement("form");
            form.method = "POST";
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="category_id" value="${categoryId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// معاينة الصورة
$("#image").on("change", function() {
    let file = this.files[0];
    if (file) {
        let reader = new FileReader();
        reader.onload = function(e) {
            // يمكن إضافة معاينة الصورة هنا
        };
        reader.readAsDataURL(file);
    }
});
</script>
';

include '../layouts/footer.php';
?>
