<?php
/**
 * نموذج المنتجات
 * Product Model
 * Restaurant Management System
 */

class Product {
    private $table = 'products';
    
    // الحصول على جميع المنتجات
    public function getAll($active_only = true) {
        $where = $active_only ? "WHERE p.is_active = 1" : "";
        
        $sql = "SELECT p.*, c.name_ar as category_name, kp.name_ar as kitchen_printer_name
                FROM {$this->table} p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN kitchen_printers kp ON p.kitchen_printer_id = kp.id 
                {$where}
                ORDER BY p.sort_order, p.name_ar";
        
        return DB::select($sql);
    }
    
    // الحصول على منتج بالمعرف
    public function getById($id) {
        $sql = "SELECT p.*, c.name_ar as category_name, kp.name_ar as kitchen_printer_name
                FROM {$this->table} p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN kitchen_printers kp ON p.kitchen_printer_id = kp.id 
                WHERE p.id = ?";
        
        return DB::selectOne($sql, array($id));
    }
    
    // الحصول على المنتجات حسب الفئة
    public function getByCategory($category_id, $active_only = true) {
        $where = $active_only ? "AND p.is_active = 1" : "";
        
        $sql = "SELECT p.*, c.name_ar as category_name
                FROM {$this->table} p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.category_id = ? {$where}
                ORDER BY p.sort_order, p.name_ar";
        
        return DB::select($sql, array($category_id));
    }
    
    // إنشاء منتج جديد
    public function create($data) {
        $sql = "INSERT INTO {$this->table} 
                (name, name_ar, description, image, category_id, kitchen_printer_id, 
                 base_price, cost_price, is_recipe_based, is_stock_item, 
                 preparation_time, calories, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = array(
            $data['name'],
            $data['name_ar'],
            $data['description'],
            $data['image'] ?? null,
            $data['category_id'],
            $data['kitchen_printer_id'] ?? null,
            $data['base_price'],
            $data['cost_price'] ?? 0,
            $data['is_recipe_based'] ?? 0,
            $data['is_stock_item'] ?? 0,
            $data['preparation_time'] ?? 0,
            $data['calories'] ?? 0,
            $data['sort_order'] ?? 0
        );
        
        $product_id = DB::insert($sql, $params);
        
        if ($product_id) {
            logActivity('create_product', 'products', $product_id, null, $data);
            return array('success' => true, 'id' => $product_id, 'message' => 'تم إنشاء المنتج بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في إنشاء المنتج');
    }
    
    // تحديث منتج
    public function update($id, $data) {
        $old_data = $this->getById($id);
        if (!$old_data) {
            return array('success' => false, 'message' => 'المنتج غير موجود');
        }
        
        $fields = array();
        $params = array();
        
        foreach ($data as $key => $value) {
            $fields[] = "{$key} = ?";
            $params[] = $value;
        }
        
        $params[] = $id;
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";
        
        if (DB::execute($sql, $params)) {
            logActivity('update_product', 'products', $id, $old_data, $data);
            return array('success' => true, 'message' => 'تم تحديث المنتج بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في تحديث المنتج');
    }
    
    // حذف منتج (إلغاء تفعيل)
    public function delete($id) {
        $product = $this->getById($id);
        if (!$product) {
            return array('success' => false, 'message' => 'المنتج غير موجود');
        }
        
        $sql = "UPDATE {$this->table} SET is_active = 0 WHERE id = ?";
        
        if (DB::execute($sql, array($id))) {
            logActivity('delete_product', 'products', $id, $product);
            return array('success' => true, 'message' => 'تم حذف المنتج بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في حذف المنتج');
    }
    
    // الحصول على أحجام المنتج
    public function getSizes($product_id) {
        $sql = "SELECT * FROM product_sizes 
                WHERE product_id = ? AND is_active = 1 
                ORDER BY sort_order";
        
        return DB::select($sql, array($product_id));
    }
    
    // الحصول على إضافات المنتج
    public function getAddons($product_id) {
        $sql = "SELECT * FROM product_addons 
                WHERE product_id = ? AND is_active = 1 
                ORDER BY sort_order";
        
        return DB::select($sql, array($product_id));
    }
    
    // الحصول على وصفة المنتج
    public function getRecipe($product_id) {
        $sql = "SELECT pr.*, i.name_ar as ingredient_name, i.unit_ar as unit
                FROM product_recipes pr 
                JOIN ingredients i ON pr.ingredient_id = i.id 
                WHERE pr.product_id = ?
                ORDER BY i.name_ar";
        
        return DB::select($sql, array($product_id));
    }
    
    // إضافة حجم للمنتج
    public function addSize($product_id, $data) {
        $sql = "INSERT INTO product_sizes (product_id, name, name_ar, price_modifier, sort_order) 
                VALUES (?, ?, ?, ?, ?)";
        
        $params = array(
            $product_id,
            $data['name'],
            $data['name_ar'],
            $data['price_modifier'],
            $data['sort_order'] ?? 0
        );
        
        return DB::insert($sql, $params);
    }
    
    // إضافة إضافة للمنتج
    public function addAddon($product_id, $data) {
        $sql = "INSERT INTO product_addons 
                (product_id, name, name_ar, price, is_required, max_quantity, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $params = array(
            $product_id,
            $data['name'],
            $data['name_ar'],
            $data['price'],
            $data['is_required'] ?? 0,
            $data['max_quantity'] ?? 1,
            $data['sort_order'] ?? 0
        );
        
        return DB::insert($sql, $params);
    }
    
    // إضافة مكون للوصفة
    public function addRecipeIngredient($product_id, $ingredient_id, $quantity, $cost_per_unit = 0) {
        $sql = "INSERT INTO product_recipes (product_id, ingredient_id, quantity, cost_per_unit) 
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                quantity = VALUES(quantity), 
                cost_per_unit = VALUES(cost_per_unit)";
        
        return DB::execute($sql, array($product_id, $ingredient_id, $quantity, $cost_per_unit));
    }
    
    // حذف مكون من الوصفة
    public function removeRecipeIngredient($product_id, $ingredient_id) {
        $sql = "DELETE FROM product_recipes WHERE product_id = ? AND ingredient_id = ?";
        return DB::execute($sql, array($product_id, $ingredient_id));
    }
    
    // حساب تكلفة التحضير
    public function calculateCost($product_id) {
        $sql = "SELECT SUM(pr.quantity * pr.cost_per_unit) as total_cost
                FROM product_recipes pr 
                WHERE pr.product_id = ?";
        
        $result = DB::selectOne($sql, array($product_id));
        return $result['total_cost'] ?? 0;
    }
    
    // تحديث تكلفة المنتج
    public function updateCost($product_id) {
        $cost = $this->calculateCost($product_id);
        
        $sql = "UPDATE {$this->table} SET cost_price = ? WHERE id = ?";
        return DB::execute($sql, array($cost, $product_id));
    }
    
    // البحث في المنتجات
    public function search($term, $category_id = null) {
        $where = "WHERE (p.name LIKE ? OR p.name_ar LIKE ? OR p.description LIKE ?) AND p.is_active = 1";
        $params = array("%{$term}%", "%{$term}%", "%{$term}%");
        
        if ($category_id) {
            $where .= " AND p.category_id = ?";
            $params[] = $category_id;
        }
        
        $sql = "SELECT p.*, c.name_ar as category_name
                FROM {$this->table} p 
                LEFT JOIN categories c ON p.category_id = c.id 
                {$where}
                ORDER BY p.name_ar";
        
        return DB::select($sql, $params);
    }
    
    // الحصول على المنتجات الأكثر مبيعاً
    public function getBestSellers($limit = 10, $days = 30) {
        $sql = "SELECT p.*, c.name_ar as category_name, 
                       SUM(oi.quantity) as total_sold,
                       SUM(oi.total_price) as total_revenue
                FROM {$this->table} p 
                LEFT JOIN categories c ON p.category_id = c.id 
                JOIN order_items oi ON p.id = oi.product_id 
                JOIN orders o ON oi.order_id = o.id 
                WHERE o.status = 'completed' 
                AND o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY p.id 
                ORDER BY total_sold DESC 
                LIMIT ?";
        
        return DB::select($sql, array($days, $limit));
    }
    
    // إحصائيات المنتجات
    public function getStats() {
        $sql = "SELECT 
                    COUNT(*) as total_products,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_products,
                    SUM(CASE WHEN is_recipe_based = 1 THEN 1 ELSE 0 END) as recipe_based_products,
                    AVG(base_price) as average_price
                FROM {$this->table}";
        
        return DB::selectOne($sql);
    }
}
?>
