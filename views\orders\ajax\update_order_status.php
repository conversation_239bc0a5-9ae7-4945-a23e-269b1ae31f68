<?php
/**
 * تحديث حالة الطلب
 * Update Order Status
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';
require_once '../../../models/Order.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('orders.edit');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

$order_id = $_POST['order_id'] ?? null;
$status = $_POST['status'] ?? '';

if (!$order_id || !$status) {
    echo json_encode(['success' => false, 'message' => 'معرف الطلب والحالة مطلوبان']);
    exit;
}

if (!in_array($status, ['pending', 'confirmed', 'preparing', 'ready', 'completed', 'cancelled'])) {
    echo json_encode(['success' => false, 'message' => 'حالة غير صحيحة']);
    exit;
}

try {
    $order_model = new Order();
    $result = $order_model->updateStatus($order_id, $status);
    
    if ($result['success']) {
        // إذا تم إكمال الطلب، تحديث إحصائيات العميل
        if ($status === 'completed') {
            $order = $order_model->getById($order_id);
            if ($order && $order['customer_id']) {
                require_once '../../../models/Customer.php';
                $customer_model = new Customer();
                $customer_model->updateStats($order['customer_id']);
            }
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث حالة الطلب بنجاح'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => $result['message']]);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
