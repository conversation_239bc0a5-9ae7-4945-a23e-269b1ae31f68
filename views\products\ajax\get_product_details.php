<?php
/**
 * الحصول على تفاصيل المنتج
 * Get Product Details
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';
require_once '../../../models/Product.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('products.view');

$product_id = isset($_POST['product_id']) ? $_POST['product_id'] : null;

if (!$product_id) {
    echo '<div class="alert alert-danger">معرف المنتج مطلوب</div>';
    exit;
}

$product_model = new Product();
$product = $product_model->getById($product_id);

if (!$product) {
    echo '<div class="alert alert-danger">المنتج غير موجود</div>';
    exit;
}

// الحصول على الأحجام والإضافات
$sizes = $product_model->getSizes($product_id);
$addons = $product_model->getAddons($product_id);
$recipe = $product_model->getRecipe($product_id);
?>

<div class="row">
    <div class="col-md-6">
        <?php if ($product['image']): ?>
        <img src="../../../uploads/<?php echo $product['image']; ?>" 
             alt="<?php echo $product['name_ar']; ?>" 
             class="img-fluid rounded mb-3">
        <?php else: ?>
        <div class="bg-light d-flex align-items-center justify-content-center rounded mb-3" 
             style="height: 200px;">
            <i class="fas fa-utensils fa-5x text-muted"></i>
        </div>
        <?php endif; ?>
    </div>
    
    <div class="col-md-6">
        <h4><?php echo $product['name_ar']; ?></h4>
        <?php if ($product['name']): ?>
        <h6 class="text-muted"><?php echo $product['name']; ?></h6>
        <?php endif; ?>
        
        <?php if ($product['description']): ?>
        <p class="text-muted"><?php echo $product['description']; ?></p>
        <?php endif; ?>
        
        <div class="row mb-3">
            <div class="col-6">
                <strong>الفئة:</strong><br>
                <span class="badge bg-secondary"><?php echo $product['category_name']; ?></span>
            </div>
            <div class="col-6">
                <strong>الحالة:</strong><br>
                <?php if ($product['is_active']): ?>
                <span class="badge bg-success">نشط</span>
                <?php else: ?>
                <span class="badge bg-danger">غير نشط</span>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-6">
                <strong>السعر الأساسي:</strong><br>
                <span class="h5 text-primary"><?php echo formatCurrency($product['base_price']); ?></span>
            </div>
            <div class="col-6">
                <strong>تكلفة التحضير:</strong><br>
                <?php if ($product['cost_price'] > 0): ?>
                <span class="text-muted"><?php echo formatCurrency($product['cost_price']); ?></span>
                <div>
                    <small class="text-success">
                        ربح: <?php echo formatCurrency($product['base_price'] - $product['cost_price']); ?>
                    </small>
                </div>
                <?php else: ?>
                <span class="text-muted">غير محدد</span>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-6">
                <strong>وقت التحضير:</strong><br>
                <?php if ($product['preparation_time'] > 0): ?>
                <span class="badge bg-info"><?php echo $product['preparation_time']; ?> دقيقة</span>
                <?php else: ?>
                <span class="text-muted">فوري</span>
                <?php endif; ?>
            </div>
            <div class="col-6">
                <strong>السعرات الحرارية:</strong><br>
                <?php if ($product['calories'] > 0): ?>
                <span class="text-info"><?php echo $product['calories']; ?> سعرة</span>
                <?php else: ?>
                <span class="text-muted">غير محدد</span>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-6">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" disabled 
                           <?php echo $product['is_recipe_based'] ? 'checked' : ''; ?>>
                    <label class="form-check-label">منتج مبني على وصفة</label>
                </div>
            </div>
            <div class="col-6">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" disabled 
                           <?php echo $product['is_stock_item'] ? 'checked' : ''; ?>>
                    <label class="form-check-label">عنصر مخزني</label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الأحجام -->
<?php if (!empty($sizes)): ?>
<hr>
<h6>الأحجام المتاحة</h6>
<div class="table-responsive">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>الحجم</th>
                <th>السعر الإضافي</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($sizes as $size): ?>
            <tr>
                <td><?php echo $size['name_ar']; ?></td>
                <td>
                    <?php if ($size['price_modifier'] > 0): ?>
                    +<?php echo formatCurrency($size['price_modifier']); ?>
                    <?php elseif ($size['price_modifier'] < 0): ?>
                    <?php echo formatCurrency($size['price_modifier']); ?>
                    <?php else: ?>
                    <span class="text-muted">بدون تغيير</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if ($size['is_active']): ?>
                    <span class="badge bg-success">متاح</span>
                    <?php else: ?>
                    <span class="badge bg-danger">غير متاح</span>
                    <?php endif; ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<?php endif; ?>

<!-- الإضافات -->
<?php if (!empty($addons)): ?>
<hr>
<h6>الإضافات المتاحة</h6>
<div class="table-responsive">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>الإضافة</th>
                <th>السعر</th>
                <th>الحد الأقصى</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($addons as $addon): ?>
            <tr>
                <td><?php echo $addon['name_ar']; ?></td>
                <td><?php echo formatCurrency($addon['price']); ?></td>
                <td>
                    <?php if ($addon['max_quantity'] > 0): ?>
                    <?php echo $addon['max_quantity']; ?>
                    <?php else: ?>
                    <span class="text-muted">بلا حدود</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if ($addon['is_active']): ?>
                    <span class="badge bg-success">متاح</span>
                    <?php else: ?>
                    <span class="badge bg-danger">غير متاح</span>
                    <?php endif; ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<?php endif; ?>

<!-- الوصفة -->
<?php if (!empty($recipe) && $product['is_recipe_based']): ?>
<hr>
<h6>مكونات الوصفة</h6>
<div class="table-responsive">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>المكون</th>
                <th>الكمية المطلوبة</th>
                <th>الوحدة</th>
                <th>المخزون الحالي</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($recipe as $ingredient): ?>
            <tr>
                <td><?php echo $ingredient['ingredient_name']; ?></td>
                <td><?php echo $ingredient['quantity_needed']; ?></td>
                <td><?php echo $ingredient['unit']; ?></td>
                <td>
                    <span class="<?php echo $ingredient['current_stock'] < $ingredient['quantity_needed'] ? 'text-danger' : 'text-success'; ?>">
                        <?php echo $ingredient['current_stock']; ?>
                    </span>
                    <?php if ($ingredient['current_stock'] < $ingredient['quantity_needed']): ?>
                    <i class="fas fa-exclamation-triangle text-warning ms-1" title="مخزون منخفض"></i>
                    <?php endif; ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<?php endif; ?>

<!-- معلومات إضافية -->
<hr>
<div class="row">
    <div class="col-md-6">
        <h6>معلومات النشر</h6>
        <p class="mb-1"><strong>تاريخ الإنشاء:</strong> <?php echo formatDate($product['created_at'], 'd/m/Y H:i'); ?></p>
        <p class="mb-1"><strong>آخر تحديث:</strong> <?php echo formatDate($product['updated_at'], 'd/m/Y H:i'); ?></p>
        <p class="mb-1"><strong>ترتيب العرض:</strong> <?php echo $product['sort_order']; ?></p>
    </div>
    <div class="col-md-6">
        <h6>إحصائيات المبيعات</h6>
        <?php
        // الحصول على إحصائيات المبيعات
        $sales_stats = DB::selectOne("
            SELECT 
                COUNT(oi.id) as total_orders,
                SUM(oi.quantity) as total_quantity,
                SUM(oi.total_price) as total_revenue
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE oi.product_id = ? AND o.status = 'completed'
        ", [$product_id]);
        ?>
        <p class="mb-1"><strong>إجمالي الطلبات:</strong> <?php echo number_format($sales_stats['total_orders']); ?></p>
        <p class="mb-1"><strong>الكمية المباعة:</strong> <?php echo number_format($sales_stats['total_quantity']); ?></p>
        <p class="mb-1"><strong>إجمالي المبيعات:</strong> <?php echo formatCurrency($sales_stats['total_revenue']); ?></p>
    </div>
</div>
