<?php
/**
 * الحصول على تفاصيل الفئة للتعديل
 * Get Category Details for Editing
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('products.edit');

$category_id = isset($_POST['category_id']) ? $_POST['category_id'] : null;

if (!$category_id) {
    echo '<div class="alert alert-danger">معرف الفئة مطلوب</div>';
    exit;
}

// الحصول على بيانات الفئة
$category = DB::selectOne("SELECT * FROM categories WHERE id = ? AND is_active = 1", [$category_id]);

if (!$category) {
    echo '<div class="alert alert-danger">الفئة غير موجودة</div>';
    exit;
}

// معالجة التحديث
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_category'])) {
    $name = sanitize(isset($_POST['name']) ? $_POST['name'] : '');
    $name_ar = sanitize(isset($_POST['name_ar']) ? $_POST['name_ar'] : '');
    $description = sanitize(isset($_POST['description']) ? $_POST['description'] : '');
    $sort_order = intval(isset($_POST['sort_order']) ? $_POST['sort_order'] : 0);
    
    if (empty($name_ar)) {
        echo '<div class="alert alert-danger">اسم الفئة بالعربية مطلوب</div>';
        exit;
    }
    
    // معالجة رفع الصورة الجديدة
    $image_filename = $category['image']; // الاحتفاظ بالصورة الحالية
    if (isset($_FILES['image']) && $_FILES['image']['error'] == UPLOAD_ERR_OK) {
        $upload_result = uploadFile($_FILES['image'], ['jpg', 'jpeg', 'png', 'gif']);
        if ($upload_result['success']) {
            // حذف الصورة القديمة
            if ($category['image'] && file_exists('../../../uploads/' . $category['image'])) {
                unlink('../../../uploads/' . $category['image']);
            }
            $image_filename = $upload_result['filename'];
        } else {
            echo '<div class="alert alert-danger">' . $upload_result['error'] . '</div>';
            exit;
        }
    }
    
    $sql = "UPDATE categories SET name = ?, name_ar = ?, description = ?, image = ?, sort_order = ? WHERE id = ?";
    if (DB::execute($sql, [$name, $name_ar, $description, $image_filename, $sort_order, $category_id])) {
        logActivity('update_category', 'categories', $category_id, null, [
            'name' => $name,
            'name_ar' => $name_ar
        ]);
        
        echo '<script>
            Swal.fire({
                title: "تم التحديث",
                text: "تم تحديث الفئة بنجاح",
                icon: "success"
            }).then(() => {
                location.reload();
            });
        </script>';
        exit;
    } else {
        echo '<div class="alert alert-danger">فشل في تحديث الفئة</div>';
        exit;
    }
}
?>

<form method="POST" enctype="multipart/form-data">
    <input type="hidden" name="update_category" value="1">
    
    <div class="mb-3">
        <label for="edit_name_ar" class="form-label">اسم الفئة بالعربية <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="edit_name_ar" name="name_ar" 
               value="<?php echo htmlspecialchars($category['name_ar']); ?>" required>
    </div>
    
    <div class="mb-3">
        <label for="edit_name" class="form-label">اسم الفئة بالإنجليزية</label>
        <input type="text" class="form-control" id="edit_name" name="name" 
               value="<?php echo htmlspecialchars($category['name']); ?>">
    </div>
    
    <div class="mb-3">
        <label for="edit_description" class="form-label">الوصف</label>
        <textarea class="form-control" id="edit_description" name="description" rows="3"><?php echo htmlspecialchars($category['description']); ?></textarea>
    </div>
    
    <div class="mb-3">
        <label for="edit_image" class="form-label">صورة الفئة</label>
        <?php if ($category['image']): ?>
        <div class="mb-2">
            <img src="../../../uploads/<?php echo $category['image']; ?>" 
                 alt="<?php echo $category['name_ar']; ?>" 
                 class="img-thumbnail" style="max-width: 150px;">
        </div>
        <?php endif; ?>
        <input type="file" class="form-control" id="edit_image" name="image" accept="image/*">
        <div class="form-text">اتركه فارغاً للاحتفاظ بالصورة الحالية</div>
    </div>
    
    <div class="mb-3">
        <label for="edit_sort_order" class="form-label">ترتيب العرض</label>
        <input type="number" class="form-control" id="edit_sort_order" name="sort_order" 
               value="<?php echo $category['sort_order']; ?>" min="0">
        <div class="form-text">الرقم الأصغر يظهر أولاً</div>
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-2"></i>
            حفظ التغييرات
        </button>
    </div>
</form>

<script>
// معاينة الصورة الجديدة
$("#edit_image").on("change", function() {
    let file = this.files[0];
    if (file) {
        let reader = new FileReader();
        reader.onload = function(e) {
            // يمكن إضافة معاينة الصورة هنا
        };
        reader.readAsDataURL(file);
    }
});
</script>
