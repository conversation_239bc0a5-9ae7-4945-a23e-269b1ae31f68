/**
 * ملف CSS مخصص لنظام إدارة المطعم
 * Custom CSS for Restaurant Management System
 */

/* الخطوط والنصوص */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}

/* الألوان المخصصة */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --sidebar-bg: #f8f9fa;
    --sidebar-border: #dee2e6;
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset 1px 0 0 var(--sidebar-border);
    background-color: var(--sidebar-bg);
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: var(--dark-color);
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
    font-weight: 600;
}

.sidebar .nav-link i {
    width: 16px;
    text-align: center;
}

/* معلومات المستخدم في الشريط الجانبي */
.user-info .avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

/* البطاقات */
.card {
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.5rem;
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* الأزرار */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* الجداول */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
    border-bottom: 2px solid var(--secondary-color);
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 0, 0, 0.025);
}

/* النماذج */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* الشارات */
.badge {
    font-size: 0.75em;
    font-weight: 600;
    border-radius: 0.375rem;
}

/* التنبيهات */
.alert {
    border-radius: 0.5rem;
    border: 1px solid transparent;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid var(--sidebar-border);
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-footer {
    border-top: 1px solid var(--sidebar-border);
    border-radius: 0 0 0.5rem 0.5rem;
}

/* خريطة الطاولات */
.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.table-card {
    aspect-ratio: 1;
    border: 3px solid;
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: white;
}

.table-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.table-card.available {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.table-card.occupied {
    border-color: var(--danger-color);
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.table-card.reserved {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.table-card.maintenance {
    border-color: var(--secondary-color);
    background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
}

.table-card.selected {
    border-width: 4px;
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0,123,255,0.5);
}

.table-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.table-capacity {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.table-session-info {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem;
    border-radius: 5px;
    font-size: 0.7rem;
    text-align: center;
    min-width: 60px;
}

.table-status-badge {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
}

/* بطاقات الفئات */
.category-card {
    transition: transform 0.2s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.category-image {
    height: 200px;
    object-fit: cover;
}

.category-image-placeholder {
    height: 200px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* دائرة الصورة الرمزية */
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

/* إحصائيات البطاقات */
.border-right-primary {
    border-right: 0.25rem solid var(--primary-color) !important;
}

.border-right-success {
    border-right: 0.25rem solid var(--success-color) !important;
}

.border-right-info {
    border-right: 0.25rem solid var(--info-color) !important;
}

.border-right-warning {
    border-right: 0.25rem solid var(--warning-color) !important;
}

.border-right-danger {
    border-right: 0.25rem solid var(--danger-color) !important;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

/* التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .sidebar {
        display: none !important;
    }
    
    main {
        margin-right: 0 !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* الشاشات الصغيرة */
@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
    
    .tables-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.5rem;
        padding: 0.5rem;
    }
    
    .table-card {
        padding: 0.5rem;
    }
    
    .table-number {
        font-size: 1.5rem;
    }
}

/* تحسينات إضافية */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* تحسين الأداء */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* تحسين إمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسين التركيز */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين التمرير */
.scroll-smooth {
    scroll-behavior: smooth;
}

/* تحسين الانتقالات */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
