<?php
/**
 * بدء وردية جديدة
 * Start New Shift
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من عدم وجود وردية نشطة
if (hasActiveShift()) {
    $_SESSION['error'] = 'لديك وردية نشطة بالفعل';
    redirect('../../index.php');
}

$error = '';
$success = '';

// معالجة بدء الوردية
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $opening_cash = floatval($_POST['opening_cash'] ?? 0);
    $notes = sanitize($_POST['notes'] ?? '');
    
    if ($opening_cash < 0) {
        $error = 'المبلغ الافتتاحي لا يمكن أن يكون سالباً';
    } else {
        $shift_id = startShift($opening_cash, $notes);
        
        if ($shift_id) {
            $_SESSION['success'] = 'تم بدء الوردية بنجاح';
            redirect('../../index.php');
        } else {
            $error = 'فشل في بدء الوردية';
        }
    }
}

$page_title = 'بدء وردية جديدة';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        بدء وردية جديدة
                    </h4>
                </div>
                
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        يجب بدء وردية جديدة قبل استخدام نقاط البيع
                    </div>
                    
                    <form method="POST" action="">
                        <div class="mb-4">
                            <label for="opening_cash" class="form-label">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المبلغ الافتتاحي (ر.س)
                            </label>
                            <input type="number" class="form-control form-control-lg" 
                                   id="opening_cash" name="opening_cash" 
                                   step="0.01" min="0" required autofocus
                                   placeholder="0.00">
                            <div class="form-text">
                                أدخل المبلغ النقدي الموجود في الصندوق عند بداية الوردية
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="notes" class="form-label">
                                <i class="fas fa-sticky-note me-2"></i>
                                ملاحظات (اختياري)
                            </label>
                            <textarea class="form-control" id="notes" name="notes" 
                                      rows="3" placeholder="أي ملاحظات خاصة بالوردية..."></textarea>
                        </div>
                        
                        <!-- معلومات الوردية -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-calendar-day fa-2x text-primary mb-2"></i>
                                        <h6>تاريخ الوردية</h6>
                                        <p class="mb-0"><?php echo formatDate(date('Y-m-d')); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-clock fa-2x text-success mb-2"></i>
                                        <h6>وقت البداية</h6>
                                        <p class="mb-0" id="current-time"><?php echo date('H:i:s'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-play me-2"></i>
                                بدء الوردية
                            </button>
                            <a href="../../index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للصفحة الرئيسية
                            </a>
                        </div>
                    </form>
                </div>
                
                <div class="card-footer bg-light">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                <?php echo $_SESSION['full_name']; ?>
                            </small>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">
                                <i class="fas fa-building me-1"></i>
                                <?php echo getSetting('restaurant_name', SITE_NAME); ?>
                            </small>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo formatDate(date('Y-m-d')); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- نصائح سريعة -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من عد النقود بدقة قبل إدخال المبلغ الافتتاحي
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تحقق من عمل الطابعات وأجهزة نقاط البيع
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            راجع قائمة المنتجات والأسعار
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            لا تنس إنهاء الوردية في نهاية اليوم
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث الوقت كل ثانية
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('current-time').textContent = timeString;
}

setInterval(updateTime, 1000);

// تركيز على حقل المبلغ الافتتاحي
document.getElementById('opening_cash').focus();

// تنسيق المبلغ أثناء الكتابة
document.getElementById('opening_cash').addEventListener('input', function() {
    let value = this.value;
    if (value && !isNaN(value)) {
        // يمكن إضافة تنسيق للعملة هنا
    }
});

// التحقق من صحة النموذج قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const openingCash = parseFloat(document.getElementById('opening_cash').value);
    
    if (isNaN(openingCash) || openingCash < 0) {
        e.preventDefault();
        Swal.fire({
            title: 'خطأ',
            text: 'يرجى إدخال مبلغ افتتاحي صحيح',
            icon: 'error',
            confirmButtonText: 'موافق'
        });
        return;
    }
    
    // تأكيد بدء الوردية
    e.preventDefault();
    
    Swal.fire({
        title: 'تأكيد بدء الوردية',
        html: `
            <p>هل أنت متأكد من بدء وردية جديدة؟</p>
            <div class="alert alert-info mt-3">
                <strong>المبلغ الافتتاحي:</strong> ${formatCurrency(openingCash)}
            </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، ابدأ الوردية',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            this.submit();
        }
    });
});

// دالة تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}
</script>

<?php include '../layouts/footer.php'; ?>
