<?php
/**
 * شاشة المطبخ
 * Kitchen Display System
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('kitchen.view');

// الحصول على طلبات المطبخ النشطة
$sql = "SELECT ko.*, kp.name_ar as printer_name
        FROM kitchen_orders ko 
        JOIN kitchen_printers kp ON ko.kitchen_printer_id = kp.id 
        WHERE ko.status IN ('pending', 'preparing') 
        ORDER BY ko.priority DESC, ko.created_at ASC";

$kitchen_orders = DB::select($sql);

// تجميع الطلبات حسب الطابعة/المحطة
$orders_by_printer = array();
foreach ($kitchen_orders as $order) {
    $orders_by_printer[$order['kitchen_printer_id']][] = $order;
}

// الحصول على طابعات المطبخ
$printers = DB::select("SELECT * FROM kitchen_printers WHERE is_active = 1 ORDER BY name_ar");

$page_title = 'شاشة المطبخ';
$additional_css = '
<style>
    body {
        background-color: #1a1a1a;
        color: white;
    }
    
    .kitchen-container {
        height: calc(100vh - 60px);
        overflow: hidden;
    }
    
    .kitchen-station {
        background: #2d2d2d;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        height: calc(100vh - 120px);
        overflow-y: auto;
    }
    
    .station-header {
        background: #007bff;
        color: white;
        padding: 0.75rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        text-align: center;
        font-weight: bold;
        font-size: 1.1rem;
    }
    
    .kitchen-order-card {
        background: #3d3d3d;
        border: 2px solid #555;
        border-radius: 8px;
        margin-bottom: 1rem;
        padding: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .kitchen-order-card.pending {
        border-color: #ffc107;
        background: #2d2416;
    }
    
    .kitchen-order-card.preparing {
        border-color: #17a2b8;
        background: #162329;
        animation: pulse 2s infinite;
    }
    
    .kitchen-order-card.urgent {
        border-color: #dc3545;
        background: #2d1619;
        animation: blink 1s infinite;
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(23, 162, 184, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(23, 162, 184, 0); }
        100% { box-shadow: 0 0 0 0 rgba(23, 162, 184, 0); }
    }
    
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.7; }
    }
    
    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #555;
    }
    
    .order-number {
        font-size: 1.25rem;
        font-weight: bold;
        color: #ffc107;
    }
    
    .order-time {
        font-size: 0.9rem;
        color: #aaa;
    }
    
    .order-timer {
        font-size: 1.1rem;
        font-weight: bold;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        background: #555;
    }
    
    .order-timer.warning {
        background: #ffc107;
        color: #000;
    }
    
    .order-timer.danger {
        background: #dc3545;
        color: white;
    }
    
    .product-info {
        margin-bottom: 0.75rem;
    }
    
    .product-name {
        font-size: 1.1rem;
        font-weight: bold;
        margin-bottom: 0.25rem;
    }
    
    .product-quantity {
        background: #007bff;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 50%;
        font-weight: bold;
        display: inline-block;
        min-width: 30px;
        text-align: center;
        margin-left: 0.5rem;
    }
    
    .product-addons {
        font-size: 0.9rem;
        color: #ffc107;
        margin-top: 0.25rem;
    }
    
    .special-instructions {
        background: #dc3545;
        color: white;
        padding: 0.5rem;
        border-radius: 4px;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
    
    .order-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .btn-kitchen {
        flex: 1;
        padding: 0.75rem;
        border: none;
        border-radius: 6px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .btn-start {
        background: #17a2b8;
        color: white;
    }
    
    .btn-ready {
        background: #28a745;
        color: white;
    }
    
    .btn-cancel {
        background: #dc3545;
        color: white;
    }
    
    .btn-kitchen:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    
    .table-info {
        background: #495057;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        display: inline-block;
    }
    
    .priority-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        font-weight: bold;
    }
    
    .stats-bar {
        background: #2d2d2d;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: flex;
        justify-content: space-around;
        text-align: center;
    }
    
    .stat-item {
        flex: 1;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #ffc107;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #aaa;
    }
</style>
';

include '../layouts/header.php';
?>

<div class="container-fluid kitchen-container">
    <!-- شريط الإحصائيات -->
    <div class="stats-bar">
        <div class="stat-item">
            <div class="stat-number" id="pending-count">
                <?php echo count(array_filter($kitchen_orders, function($o) { return $o['status'] == 'pending'; })); ?>
            </div>
            <div class="stat-label">في الانتظار</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="preparing-count">
                <?php echo count(array_filter($kitchen_orders, function($o) { return $o['status'] == 'preparing'; })); ?>
            </div>
            <div class="stat-label">قيد التحضير</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="total-count">
                <?php echo count($kitchen_orders); ?>
            </div>
            <div class="stat-label">إجمالي الطلبات</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="current-time">
                <?php echo date('H:i:s'); ?>
            </div>
            <div class="stat-label">الوقت الحالي</div>
        </div>
    </div>
    
    <div class="row">
        <?php foreach ($printers as $printer): ?>
        <div class="col-md-<?php echo count($printers) <= 2 ? '6' : (count($printers) <= 3 ? '4' : '3'); ?>">
            <div class="kitchen-station">
                <div class="station-header">
                    <i class="fas fa-utensils me-2"></i>
                    <?php echo $printer['name_ar']; ?>
                    <span class="badge bg-light text-dark ms-2">
                        <?php echo isset($orders_by_printer[$printer['id']]) ? count($orders_by_printer[$printer['id']]) : 0; ?>
                    </span>
                </div>
                
                <div class="orders-container" id="printer-<?php echo $printer['id']; ?>">
                    <?php if (isset($orders_by_printer[$printer['id']])): ?>
                        <?php foreach ($orders_by_printer[$printer['id']] as $order): ?>
                            <?php
                            $created_time = strtotime($order['created_at']);
                            $elapsed_minutes = floor((time() - $created_time) / 60);
                            $timer_class = '';
                            
                            if ($elapsed_minutes > 20) {
                                $timer_class = 'danger';
                            } elseif ($elapsed_minutes > 15) {
                                $timer_class = 'warning';
                            }
                            
                            $card_class = $order['status'];
                            if ($order['priority'] == 'urgent' || $elapsed_minutes > 20) {
                                $card_class .= ' urgent';
                            }
                            ?>
                            
                            <div class="kitchen-order-card <?php echo $card_class; ?>" 
                                 data-order-id="<?php echo $order['id']; ?>"
                                 data-created-time="<?php echo $order['created_at']; ?>">
                                
                                <?php if ($order['priority'] == 'urgent'): ?>
                                <div class="priority-badge">!</div>
                                <?php endif; ?>
                                
                                <div class="order-header">
                                    <div>
                                        <div class="order-number">#<?php echo $order['order_number']; ?></div>
                                        <?php if ($order['table_number']): ?>
                                        <div class="table-info">
                                            <i class="fas fa-chair me-1"></i>
                                            طاولة <?php echo $order['table_number']; ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end">
                                        <div class="order-timer <?php echo $timer_class; ?>" 
                                             data-start-time="<?php echo $order['created_at']; ?>">
                                            <?php echo sprintf('%02d:%02d', floor($elapsed_minutes / 60), $elapsed_minutes % 60); ?>
                                        </div>
                                        <div class="order-time">
                                            <?php echo formatDate($order['created_at'], 'H:i'); ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="product-info">
                                    <div class="product-name">
                                        <span class="product-quantity"><?php echo $order['quantity']; ?></span>
                                        <?php echo $order['product_name_ar']; ?>
                                    </div>
                                    
                                    <?php if ($order['addons']): ?>
                                    <div class="product-addons">
                                        <i class="fas fa-plus me-1"></i>
                                        <?php echo $order['addons']; ?>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($order['special_instructions']): ?>
                                    <div class="special-instructions">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        <?php echo $order['special_instructions']; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="order-actions">
                                    <?php if ($order['status'] == 'pending'): ?>
                                    <button class="btn-kitchen btn-start" 
                                            onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'preparing')">
                                        <i class="fas fa-play me-1"></i>
                                        بدء التحضير
                                    </button>
                                    <?php elseif ($order['status'] == 'preparing'): ?>
                                    <button class="btn-kitchen btn-ready" 
                                            onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'ready')">
                                        <i class="fas fa-check me-1"></i>
                                        جاهز
                                    </button>
                                    <?php endif; ?>
                                    
                                    <button class="btn-kitchen btn-cancel" 
                                            onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'cancelled')">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center text-muted p-4">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <p>لا توجد طلبات</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>

<?php
$additional_js = '
<script>
// تحديث العدادات كل ثانية
setInterval(function() {
    updateTimers();
    updateCurrentTime();
}, 1000);

// تحديث الصفحة كل 30 ثانية
setInterval(function() {
    location.reload();
}, 30000);

// تحديث العدادات الزمنية
function updateTimers() {
    $(".order-timer").each(function() {
        const startTime = new Date($(this).data("start-time"));
        const now = new Date();
        const elapsed = Math.floor((now - startTime) / 1000 / 60);
        
        const hours = Math.floor(elapsed / 60);
        const minutes = elapsed % 60;
        
        $(this).text(String(hours).padStart(2, "0") + ":" + String(minutes).padStart(2, "0"));
        
        // تحديث الألوان حسب الوقت
        $(this).removeClass("warning danger");
        if (elapsed > 20) {
            $(this).addClass("danger");
        } else if (elapsed > 15) {
            $(this).addClass("warning");
        }
    });
}

// تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString("ar-SA", {
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit"
    });
    $("#current-time").text(timeString);
}

// تحديث حالة الطلب
function updateOrderStatus(orderId, status) {
    let confirmMessage = "";
    let confirmButton = "";
    
    switch(status) {
        case "preparing":
            confirmMessage = "هل تريد بدء تحضير هذا الطلب؟";
            confirmButton = "بدء التحضير";
            break;
        case "ready":
            confirmMessage = "هل الطلب جاهز للتقديم؟";
            confirmButton = "جاهز";
            break;
        case "cancelled":
            confirmMessage = "هل تريد إلغاء هذا الطلب؟";
            confirmButton = "إلغاء الطلب";
            break;
    }
    
    Swal.fire({
        title: "تأكيد العملية",
        text: confirmMessage,
        icon: "question",
        showCancelButton: true,
        confirmButtonText: confirmButton,
        cancelButtonText: "تراجع",
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: "ajax/update_kitchen_order.php",
                type: "POST",
                data: {
                    order_id: orderId,
                    status: status
                },
                dataType: "json",
                success: function(response) {
                    if (response.success) {
                        // إزالة البطاقة أو تحديثها
                        if (status === "ready" || status === "cancelled") {
                            $("[data-order-id=\"" + orderId + "\"]").fadeOut(300, function() {
                                $(this).remove();
                                updateStats();
                            });
                        } else {
                            location.reload();
                        }
                        
                        // إشعار صوتي
                        playNotificationSound();
                        
                    } else {
                        showError("خطأ", response.message);
                    }
                },
                error: function() {
                    showError("خطأ", "فشل في تحديث حالة الطلب");
                }
            });
        }
    });
}

// تحديث الإحصائيات
function updateStats() {
    const pendingCount = $(".kitchen-order-card.pending").length;
    const preparingCount = $(".kitchen-order-card.preparing").length;
    const totalCount = $(".kitchen-order-card").length;
    
    $("#pending-count").text(pendingCount);
    $("#preparing-count").text(preparingCount);
    $("#total-count").text(totalCount);
}

// تشغيل صوت الإشعار
function playNotificationSound() {
    // يمكن إضافة ملف صوتي هنا
    if ("speechSynthesis" in window) {
        const utterance = new SpeechSynthesisUtterance("تم تحديث الطلب");
        utterance.lang = "ar-SA";
        utterance.volume = 0.5;
        speechSynthesis.speak(utterance);
    }
}

// اختصارات لوحة المفاتيح
$(document).keydown(function(e) {
    // F5 - تحديث الصفحة
    if (e.key === "F5") {
        e.preventDefault();
        location.reload();
    }
    
    // Escape - إلغاء أي نافذة مفتوحة
    if (e.key === "Escape") {
        Swal.close();
    }
});

// تحديث الصفحة عند فقدان التركيز واستعادته
let isPageVisible = true;

document.addEventListener("visibilitychange", function() {
    if (document.hidden) {
        isPageVisible = false;
    } else {
        isPageVisible = true;
        // تحديث الصفحة عند العودة إليها
        setTimeout(function() {
            location.reload();
        }, 1000);
    }
});
</script>
';

include '../layouts/footer.php';
?>
