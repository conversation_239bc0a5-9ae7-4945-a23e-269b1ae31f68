<?php
/**
 * إدارة الطاولات
 * Tables Management
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('tables.view');

// معالجة تحديث حالة الطاولة
if (isset($_POST['action']) && $_POST['action'] === 'update_status') {
   $table_id = isset($_POST['table_id']) ? $_POST['table_id'] : null;
$status   = isset($_POST['status']) ? $_POST['status'] : '';
    
    if ($table_id && in_array($status, ['available', 'occupied', 'reserved', 'maintenance'])) {
        $sql = "UPDATE tables SET status = ? WHERE id = ?";
        if (DB::execute($sql, [$status, $table_id])) {
            logActivity('update_table_status', 'tables', $table_id, null, ['status' => $status]);
            $_SESSION['success'] = 'تم تحديث حالة الطاولة بنجاح';
        } else {
            $_SESSION['error'] = 'فشل في تحديث حالة الطاولة';
        }
        redirect('list.php');
    }
}

// الحصول على الطاولات مع معلومات الجلسات النشطة
$sql = "SELECT t.*, 
               ts.id as session_id, ts.session_code, ts.total_customers, ts.start_time,
               COUNT(o.id) as active_orders,
               SUM(o.total_amount) as session_total
        FROM tables t 
        LEFT JOIN table_sessions ts ON t.id = ts.table_id AND ts.status = 'active'
        LEFT JOIN orders o ON ts.id = o.table_session_id AND o.status NOT IN ('completed', 'cancelled')
        GROUP BY t.id
        ORDER BY t.table_number";

$tables = DB::select($sql);

// إحصائيات الطاولات
$stats = DB::selectOne("
    SELECT 
        COUNT(*) as total_tables,
        SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available_tables,
        SUM(CASE WHEN status = 'occupied' THEN 1 ELSE 0 END) as occupied_tables,
        SUM(CASE WHEN status = 'reserved' THEN 1 ELSE 0 END) as reserved_tables,
        SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_tables
    FROM tables
");

$page_title = 'إدارة الطاولات';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الطاولات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <?php if (hasPermission('tables.create')): ?>
                        <a href="add.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> طاولة جديدة
                        </a>
                        <?php endif; ?>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="refreshTables()">
                            <i class="fas fa-sync"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>

            <?php showSessionMessages(); ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي الطاولات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['total_tables']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chair fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        متاحة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['available_tables']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                        مشغولة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['occupied_tables']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        محجوزة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['reserved_tables']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض الطاولات -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">خريطة الطاولات</h5>
                        </div>
                        <div class="card-body">
                            <div class="tables-grid">
                                <?php foreach ($tables as $table): ?>
                                <div class="table-card <?php echo $table['status']; ?>" 
                                     data-table-id="<?php echo $table['id']; ?>"
                                     onclick="selectTable(<?php echo $table['id']; ?>)">
                                    <div class="table-number"><?php echo $table['table_number']; ?></div>
                                    <div class="table-capacity">
                                        <i class="fas fa-users"></i>
                                        <?php echo $table['capacity']; ?>
                                    </div>
                                    
                                    <?php if ($table['status'] == 'occupied' && $table['session_id']): ?>
                                    <div class="table-session-info">
                                        <div class="session-time">
                                            <?php 
                                            $start_time = strtotime($table['start_time']);
                                            $elapsed = time() - $start_time;
                                            echo gmdate('H:i', $elapsed);
                                            ?>
                                        </div>
                                        <div class="session-orders">
                                            <?php echo $table['active_orders']; ?> طلب
                                        </div>
                                        <?php if ($table['session_total'] > 0): ?>
                                        <div class="session-total">
                                            <?php echo formatCurrency($table['session_total']); ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div class="table-status-badge">
                                        <?php
                                        $status_names = [
                                            'available' => 'متاحة',
                                            'occupied' => 'مشغولة',
                                            'reserved' => 'محجوزة',
                                            'maintenance' => 'صيانة'
                                        ];
                                        echo $status_names[$table['status']];
                                        ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">تفاصيل الطاولة</h5>
                        </div>
                        <div class="card-body" id="tableDetails">
                            <div class="text-center text-muted">
                                <i class="fas fa-chair fa-3x mb-3"></i>
                                <p>اختر طاولة لعرض التفاصيل</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">إجراءات سريعة</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-success" id="makeAvailableBtn" 
                                        onclick="updateTableStatus('available')" disabled>
                                    <i class="fas fa-check"></i> جعل متاحة
                                </button>
                                <button type="button" class="btn btn-warning" id="makeReservedBtn" 
                                        onclick="updateTableStatus('reserved')" disabled>
                                    <i class="fas fa-calendar"></i> حجز
                                </button>
                                <button type="button" class="btn btn-secondary" id="makeMaintenanceBtn" 
                                        onclick="updateTableStatus('maintenance')" disabled>
                                    <i class="fas fa-tools"></i> صيانة
                                </button>
                                <button type="button" class="btn btn-primary" id="newOrderBtn" 
                                        onclick="createNewOrder()" disabled>
                                    <i class="fas fa-plus"></i> طلب جديد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
$additional_css = '
<style>
.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.table-card {
    aspect-ratio: 1;
    border: 3px solid;
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: white;
}

.table-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.table-card.available {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.table-card.occupied {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.table-card.reserved {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.table-card.maintenance {
    border-color: #6c757d;
    background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
}

.table-card.selected {
    border-width: 4px;
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0,123,255,0.5);
}

.table-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.table-capacity {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.table-session-info {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem;
    border-radius: 5px;
    font-size: 0.7rem;
    text-align: center;
    min-width: 60px;
}

.table-status-badge {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
}

.session-time {
    font-weight: bold;
    color: #ffc107;
}

.session-orders {
    font-size: 0.6rem;
}

.session-total {
    font-size: 0.6rem;
    color: #28a745;
}
</style>
';

$additional_js = '
<script>
let selectedTableId = null;

function selectTable(tableId) {
    // إزالة التحديد السابق
    $(".table-card").removeClass("selected");
    
    // تحديد الطاولة الجديدة
    $(`[data-table-id="${tableId}"]`).addClass("selected");
    selectedTableId = tableId;
    
    // تحميل تفاصيل الطاولة
    loadTableDetails(tableId);
    
    // تفعيل الأزرار
    enableActionButtons();
}

function loadTableDetails(tableId) {
    $.ajax({
        url: "ajax/get_table_details.php",
        type: "POST",
        data: { table_id: tableId },
        success: function(response) {
            $("#tableDetails").html(response);
        }
    });
}

function enableActionButtons() {
    $("#makeAvailableBtn, #makeReservedBtn, #makeMaintenanceBtn, #newOrderBtn").prop("disabled", false);
}

function updateTableStatus(status) {
    if (!selectedTableId) {
        showError("خطأ", "يرجى اختيار طاولة أولاً");
        return;
    }
    
    let statusNames = {
        "available": "متاحة",
        "reserved": "محجوزة", 
        "maintenance": "صيانة"
    };
    
    Swal.fire({
        title: "تحديث حالة الطاولة",
        text: `هل تريد جعل الطاولة ${statusNames[status]}؟`,
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "نعم",
        cancelButtonText: "إلغاء"
    }).then((result) => {
        if (result.isConfirmed) {
            let form = document.createElement("form");
            form.method = "POST";
            form.innerHTML = `
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" name="table_id" value="${selectedTableId}">
                <input type="hidden" name="status" value="${status}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function createNewOrder() {
    if (!selectedTableId) {
        showError("خطأ", "يرجى اختيار طاولة أولاً");
        return;
    }
    
    window.location.href = `../pos/index.php?table_id=${selectedTableId}`;
}

function refreshTables() {
    location.reload();
}

// تحديث تلقائي كل 30 ثانية
setInterval(function() {
    if (!document.hidden) {
        // تحديث معلومات الطاولات دون إعادة تحميل الصفحة
        updateTablesStatus();
    }
}, 30000);

function updateTablesStatus() {
    $.ajax({
        url: "ajax/get_tables_status.php",
        type: "GET",
        dataType: "json",
        success: function(tables) {
            tables.forEach(function(table) {
                let tableCard = $(`[data-table-id="${table.id}"]`);
                
                // تحديث الحالة
                tableCard.removeClass("available occupied reserved maintenance")
                         .addClass(table.status);
                
                // تحديث معلومات الجلسة
                if (table.status === "occupied" && table.session_info) {
                    let sessionInfo = `
                        <div class="session-time">${table.session_info.elapsed_time}</div>
                        <div class="session-orders">${table.session_info.active_orders} طلب</div>
                    `;
                    if (table.session_info.total > 0) {
                        sessionInfo += `<div class="session-total">${table.session_info.total}</div>`;
                    }
                    
                    tableCard.find(".table-session-info").html(sessionInfo);
                } else {
                    tableCard.find(".table-session-info").empty();
                }
            });
        }
    });
}

// تحديث الوقت المنقضي للجلسات النشطة
setInterval(function() {
    $(".session-time").each(function() {
        // يمكن تحديث الوقت هنا إذا لزم الأمر
    });
}, 60000); // كل دقيقة
</script>
';

include '../layouts/footer.php';
?>
