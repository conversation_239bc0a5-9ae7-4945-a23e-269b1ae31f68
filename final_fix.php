<?php
/**
 * الإصلاح النهائي الشامل
 * Final Complete Fix
 * Restaurant Management System
 */

echo "بدء الإصلاح النهائي الشامل...\n\n";

$fixes_applied = 0;
$errors = array();

// 1. إصلاح مشكلة array() في define() في config/constants.php
echo "1. إصلاح مشكلة array() في define():\n";

if (file_exists('config/constants.php')) {
    $content = file_get_contents('config/constants.php');
    $original_content = $content;
    
    // استبدال جميع define مع array() بمتغيرات عامة
    $replacements = array(
        '/define\s*\(\s*[\'"]ALLOWED_IMAGE_TYPES[\'"],\s*array\([^)]+\)\s*\);/' => '$GLOBALS[\'ALLOWED_IMAGE_TYPES\'] = array(\'jpg\', \'jpeg\', \'png\', \'gif\');',
        '/define\s*\(\s*[\'"]ALLOWED_DOCUMENT_TYPES[\'"],\s*array\([^)]+\)\s*\);/' => '$GLOBALS[\'ALLOWED_DOCUMENT_TYPES\'] = array(\'pdf\', \'doc\', \'docx\', \'xls\', \'xlsx\');',
        '/define\s*\(\s*[\'"]CHART_COLORS[\'"],\s*array\([^)]+\)\s*\);/s' => '$GLOBALS[\'CHART_COLORS\'] = array(\'#007bff\', \'#28a745\', \'#ffc107\', \'#dc3545\', \'#17a2b8\', \'#6f42c1\', \'#fd7e14\', \'#20c997\');',
        '/define\s*\(\s*[\'"]EXPORT_FORMATS[\'"],\s*array\([^)]+\)\s*\);/' => '$GLOBALS[\'EXPORT_FORMATS\'] = array(\'excel\', \'pdf\', \'csv\');',
        '/define\s*\(\s*[\'"]SUPPORTED_LANGUAGES[\'"],\s*array\([^)]+\)\s*\);/' => '$GLOBALS[\'SUPPORTED_LANGUAGES\'] = array(\'ar\', \'en\');',
        '/define\s*\(\s*[\'"]SUPPORTED_CURRENCIES[\'"],\s*array\([^)]+\)\s*\);/' => '$GLOBALS[\'SUPPORTED_CURRENCIES\'] = array(\'SAR\', \'USD\', \'EUR\');'
    );
    
    foreach ($replacements as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            echo "   ✓ تم إصلاح pattern: " . substr($pattern, 0, 30) . "...\n";
            $fixes_applied++;
        }
    }
    
    if ($content !== $original_content) {
        if (file_put_contents('config/constants.php', $content)) {
            echo "   ✓ تم حفظ config/constants.php\n";
        } else {
            echo "   ✗ فشل في حفظ config/constants.php\n";
            $errors[] = "فشل في حفظ config/constants.php";
        }
    } else {
        echo "   - config/constants.php لا يحتاج تعديل\n";
    }
}

// 2. إصلاح تعريف الدوال المزدوج
echo "\n2. إصلاح تعريف الدوال المزدوج:\n";

// إزالة logActivity من includes/functions.php
if (file_exists('includes/functions.php')) {
    $content = file_get_contents('includes/functions.php');
    
    $pattern = '/\/\/\s*تسجيل النشاط.*?function\s+logActivity\s*\([^}]+\}/s';
    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, '// تم نقل دالة logActivity إلى config/init.php لتجنب التعريف المزدوج', $content);
        
        if (file_put_contents('includes/functions.php', $content)) {
            echo "   ✓ تم إزالة logActivity من includes/functions.php\n";
            $fixes_applied++;
        } else {
            echo "   ✗ فشل في إزالة logActivity من includes/functions.php\n";
            $errors[] = "فشل في إزالة logActivity من includes/functions.php";
        }
    } else {
        echo "   - logActivity غير موجودة في includes/functions.php\n";
    }
}

// 3. إنشاء دوال مساعدة للوصول للمتغيرات العامة
echo "\n3. إنشاء دوال مساعدة:\n";

$helper_functions = '<?php
/**
 * دوال مساعدة للوصول للمتغيرات العامة
 * Helper functions for global variables
 */

// دالة للحصول على أنواع الصور المسموحة
function getAllowedImageTypes() {
    return isset($GLOBALS[\'ALLOWED_IMAGE_TYPES\']) ? $GLOBALS[\'ALLOWED_IMAGE_TYPES\'] : array(\'jpg\', \'jpeg\', \'png\', \'gif\');
}

// دالة للحصول على أنواع المستندات المسموحة
function getAllowedDocumentTypes() {
    return isset($GLOBALS[\'ALLOWED_DOCUMENT_TYPES\']) ? $GLOBALS[\'ALLOWED_DOCUMENT_TYPES\'] : array(\'pdf\', \'doc\', \'docx\', \'xls\', \'xlsx\');
}

// دالة للحصول على الامتدادات المسموحة
function getAllowedExtensions() {
    return isset($GLOBALS[\'ALLOWED_EXTENSIONS\']) ? $GLOBALS[\'ALLOWED_EXTENSIONS\'] : array(\'jpg\', \'jpeg\', \'png\', \'gif\', \'pdf\');
}

// دالة للحصول على ألوان الرسوم البيانية
function getChartColors() {
    return isset($GLOBALS[\'CHART_COLORS\']) ? $GLOBALS[\'CHART_COLORS\'] : array(\'#007bff\', \'#28a745\', \'#ffc107\', \'#dc3545\');
}

// دالة للحصول على صيغ التصدير
function getExportFormats() {
    return isset($GLOBALS[\'EXPORT_FORMATS\']) ? $GLOBALS[\'EXPORT_FORMATS\'] : array(\'excel\', \'pdf\', \'csv\');
}

// دالة للحصول على اللغات المدعومة
function getSupportedLanguages() {
    return isset($GLOBALS[\'SUPPORTED_LANGUAGES\']) ? $GLOBALS[\'SUPPORTED_LANGUAGES\'] : array(\'ar\', \'en\');
}

// دالة للحصول على العملات المدعومة
function getSupportedCurrencies() {
    return isset($GLOBALS[\'SUPPORTED_CURRENCIES\']) ? $GLOBALS[\'SUPPORTED_CURRENCIES\'] : array(\'SAR\', \'USD\', \'EUR\');
}
?>';

if (file_put_contents('includes/helpers.php', $helper_functions)) {
    echo "   ✓ تم إنشاء includes/helpers.php\n";
    $fixes_applied++;
} else {
    echo "   ✗ فشل في إنشاء includes/helpers.php\n";
    $errors[] = "فشل في إنشاء includes/helpers.php";
}

// 4. إنشاء المجلدات المطلوبة
echo "\n4. إنشاء المجلدات المطلوبة:\n";
$required_dirs = array('logs', 'uploads', 'temp', 'backups', 'uploads/products', 'uploads/receipts', 'uploads/avatars');

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "   ✓ تم إنشاء مجلد: $dir\n";
            $fixes_applied++;
        } else {
            echo "   ✗ فشل في إنشاء مجلد: $dir\n";
            $errors[] = "فشل في إنشاء مجلد: $dir";
        }
    } else {
        echo "   - مجلد موجود: $dir\n";
    }
}

// 5. إنشاء ملفات الحماية
echo "\n5. إنشاء ملفات الحماية:\n";

$protected_dirs = array('logs', 'temp', 'backups', 'config', 'includes');
$htaccess_content = "Order deny,allow\nDeny from all";
$index_content = "<?php\n// Access denied\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n?>";

foreach ($protected_dirs as $dir) {
    if (is_dir($dir)) {
        // إنشاء .htaccess
        $htaccess_file = $dir . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            if (file_put_contents($htaccess_file, $htaccess_content)) {
                echo "   ✓ تم إنشاء حماية: $htaccess_file\n";
                $fixes_applied++;
            }
        }
        
        // إنشاء index.php
        $index_file = $dir . '/index.php';
        if (!file_exists($index_file)) {
            if (file_put_contents($index_file, $index_content)) {
                echo "   ✓ تم إنشاء حماية إضافية: $index_file\n";
                $fixes_applied++;
            }
        }
    }
}

// 6. إنشاء ملف error_log فارغ
echo "\n6. إنشاء ملف سجل الأخطاء:\n";
if (!file_exists('logs/error.log')) {
    if (file_put_contents('logs/error.log', '')) {
        echo "   ✓ تم إنشاء ملف logs/error.log\n";
        $fixes_applied++;
    }
} else {
    echo "   - ملف logs/error.log موجود\n";
}

// 7. إنشاء ملف اختبار نهائي
echo "\n7. إنشاء ملف اختبار نهائي:\n";

$test_content = '<?php
// اختبار نهائي للنظام
echo "<!DOCTYPE html><html lang=\"ar\" dir=\"rtl\"><head><meta charset=\"UTF-8\"><title>اختبار نهائي</title></head><body>";
echo "<h1>اختبار النظام النهائي</h1>";

try {
    require_once "config/init.php";
    echo "<p style=\"color: green;\">✓ تم تحميل config/init.php بنجاح</p>";
    
    if (class_exists("DB")) {
        echo "<p style=\"color: green;\">✓ فئة DB متوفرة</p>";
        
        $test = DB::selectOne("SELECT 1 as test");
        if ($test) {
            echo "<p style=\"color: green;\">✓ قاعدة البيانات تعمل</p>";
        } else {
            echo "<p style=\"color: red;\">✗ مشكلة في قاعدة البيانات</p>";
        }
    } else {
        echo "<p style=\"color: red;\">✗ فئة DB غير متوفرة</p>";
    }
    
    if (function_exists("getSetting")) {
        echo "<p style=\"color: green;\">✓ دالة getSetting متوفرة</p>";
    } else {
        echo "<p style=\"color: red;\">✗ دالة getSetting غير متوفرة</p>";
    }
    
    if (function_exists("logActivity")) {
        echo "<p style=\"color: green;\">✓ دالة logActivity متوفرة</p>";
    } else {
        echo "<p style=\"color: red;\">✗ دالة logActivity غير متوفرة</p>";
    }
    
    echo "<h2>النظام جاهز!</h2>";
    echo "<p><a href=\"login.php\" style=\"background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">تسجيل الدخول</a></p>";
    
} catch (Exception $e) {
    echo "<p style=\"color: red;\">خطأ: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>';

if (file_put_contents('test_final.php', $test_content)) {
    echo "   ✓ تم إنشاء ملف test_final.php\n";
    $fixes_applied++;
}

// النتائج النهائية
echo "\n" . str_repeat("=", 50) . "\n";
echo "تم الانتهاء من الإصلاح النهائي الشامل!\n\n";

echo "الإحصائيات:\n";
echo "- عدد الإصلاحات المطبقة: $fixes_applied\n";
echo "- عدد الأخطاء: " . count($errors) . "\n\n";

if (!empty($errors)) {
    echo "الأخطاء المتبقية:\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
    echo "\n";
}

echo "الخطوات التالية:\n";
echo "1. قم بزيارة: test_final.php في المتصفح\n";
echo "2. إذا نجح الاختبار، ادخل على: login.php\n";
echo "3. بيانات الدخول: admin / admin123\n\n";

echo "روابط مفيدة:\n";
echo "- الاختبار النهائي: http://127.0.0.1/3/test_final.php\n";
echo "- تسجيل الدخول: http://127.0.0.1/3/login.php\n";
echo "- لوحة التحكم: http://127.0.0.1/3/views/dashboard/index.php\n\n";

echo "تم بنجاح! ✓\n";
?>
