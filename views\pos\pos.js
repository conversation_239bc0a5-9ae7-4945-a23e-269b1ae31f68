/**
 * نقاط البيع - JavaScript
 * POS JavaScript Functions
 * Restaurant Management System
 */

// متغيرات عامة
let cart = [];
let currentCustomer = null;
let currentTable = null;
let taxRate = 15;
let serviceChargeRate = 10;

// تحميل الصفحة
$(document).ready(function() {
    // إعداد البحث عن العملاء
    $('#customer-search').on('input', function() {
        searchCustomers($(this).val());
    });
    
    // إعداد نموذج العميل الجديد
    $('#new-customer-form').on('submit', function(e) {
        e.preventDefault();
        createNewCustomer();
    });
    
    // تغيير نوع الطلب
    $('#order-type').on('change', function() {
        const orderType = $(this).val();
        if (orderType === 'dine_in') {
            $('#table-row').show();
        } else {
            $('#table-row').hide();
            currentTable = null;
            $('#table-name').val('');
            $('#table-session-id').val('');
        }
    });
    
    // تحديث الملخص عند تحميل الصفحة
    updateOrderSummary();
});

// عرض فئة معينة
function showCategory(categoryId) {
    // إخفاء جميع الفئات
    $('.product-grid').hide();
    
    // إظهار الفئة المحددة
    $('#category-' + categoryId).show();
    
    // تحديث التبويبات
    $('.category-tab').removeClass('active');
    $('[onclick="showCategory(\'' + categoryId + '\')"]').addClass('active');
}

// إضافة منتج للسلة
function addToCart(productId) {
    // البحث عن المنتج
    $.ajax({
        url: 'ajax/get_product.php',
        type: 'POST',
        data: { product_id: productId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                const product = response.product;
                
                // التحقق من وجود المنتج في السلة
                const existingItem = cart.find(item => item.product_id === productId);
                
                if (existingItem) {
                    existingItem.quantity++;
                    existingItem.total_price = existingItem.unit_price * existingItem.quantity;
                } else {
                    cart.push({
                        product_id: productId,
                        name: product.name_ar,
                        unit_price: parseFloat(product.base_price),
                        quantity: 1,
                        total_price: parseFloat(product.base_price),
                        addons: []
                    });
                }
                
                updateCartDisplay();
                updateOrderSummary();
            }
        }
    });
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartContainer = $('#cart-items');
    
    if (cart.length === 0) {
        cartContainer.html(`
            <div class="text-center text-muted p-4">
                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                <p>لا توجد عناصر في السلة</p>
            </div>
        `);
        return;
    }
    
    let html = '';
    cart.forEach((item, index) => {
        html += `
            <div class="cart-item">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${item.name}</h6>
                        <small class="text-muted">${formatCurrency(item.unit_price)} × ${item.quantity}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold">${formatCurrency(item.total_price)}</div>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="updateQuantity(${index}, ${item.quantity - 1})">
                        <i class="fas fa-minus"></i>
                    </button>
                    <input type="number" class="quantity-input" value="${item.quantity}" 
                           onchange="updateQuantity(${index}, this.value)" min="1">
                    <button class="quantity-btn" onclick="updateQuantity(${index}, ${item.quantity + 1})">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="showAddonsModal(${index})">
                        <i class="fas fa-plus"></i> إضافات
                    </button>
                </div>
            </div>
        `;
    });
    
    cartContainer.html(html);
}

// تحديث كمية المنتج
function updateQuantity(index, newQuantity) {
    newQuantity = parseInt(newQuantity);
    
    if (newQuantity <= 0) {
        removeFromCart(index);
        return;
    }
    
    cart[index].quantity = newQuantity;
    cart[index].total_price = cart[index].unit_price * newQuantity;
    
    updateCartDisplay();
    updateOrderSummary();
}

// حذف منتج من السلة
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
    updateOrderSummary();
}

// مسح السلة
function clearCart() {
    if (cart.length === 0) return;
    
    Swal.fire({
        title: 'مسح السلة؟',
        text: 'هل أنت متأكد من مسح جميع العناصر؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، امسح',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            cart = [];
            updateCartDisplay();
            updateOrderSummary();
        }
    });
}

// تحديث ملخص الطلب
function updateOrderSummary() {
    const subtotal = cart.reduce((sum, item) => sum + item.total_price, 0);
    const taxAmount = subtotal * (taxRate / 100);
    const serviceCharge = subtotal * (serviceChargeRate / 100);
    const total = subtotal + taxAmount + serviceCharge;
    
    $('#subtotal').text(formatCurrency(subtotal));
    $('#tax-amount').text(formatCurrency(taxAmount));
    $('#service-charge').text(formatCurrency(serviceCharge));
    $('#total-amount').text(formatCurrency(total));
}

// عرض نافذة العملاء
function showCustomerModal() {
    $('#customerModal').modal('show');
}

// البحث عن العملاء
function searchCustomers(term) {
    if (term.length < 2) {
        $('#customer-results').empty();
        return;
    }
    
    $.ajax({
        url: 'ajax/search_customers.php',
        type: 'POST',
        data: { term: term },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                let html = '';
                response.customers.forEach(customer => {
                    html += `
                        <div class="card mb-2 customer-card" onclick="selectCustomer(${customer.id}, '${customer.name}', '${customer.phone}')">
                            <div class="card-body p-2">
                                <h6 class="mb-1">${customer.name}</h6>
                                <small class="text-muted">${customer.phone}</small>
                                <span class="badge bg-primary float-end">${customer.loyalty_points} نقطة</span>
                            </div>
                        </div>
                    `;
                });
                $('#customer-results').html(html);
            }
        }
    });
}

// اختيار عميل
function selectCustomer(id, name, phone) {
    currentCustomer = { id: id, name: name, phone: phone };
    $('#customer-id').val(id);
    $('#customer-name').val(name);
    $('#customerModal').modal('hide');
}

// إنشاء عميل جديد
function createNewCustomer() {
    const formData = new FormData($('#new-customer-form')[0]);
    
    $.ajax({
        url: 'ajax/create_customer.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                selectCustomer(response.id, formData.get('name'), formData.get('phone'));
                $('#new-customer-form')[0].reset();
                showSuccess('تم إنشاء العميل بنجاح');
            } else {
                showError('خطأ', response.message);
            }
        }
    });
}

// عرض نافذة الطاولات
function showTableModal() {
    if ($('#order-type').val() !== 'dine_in') {
        showError('خطأ', 'يجب اختيار نوع الطلب "طاولة" أولاً');
        return;
    }
    $('#tableModal').modal('show');
}

// اختيار طاولة
function selectTable(tableId, tableNumber) {
    // إنشاء جلسة طاولة جديدة
    $.ajax({
        url: 'ajax/create_table_session.php',
        type: 'POST',
        data: { 
            table_id: tableId,
            customer_id: currentCustomer ? currentCustomer.id : null
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                currentTable = { id: tableId, number: tableNumber, session_id: response.session_id };
                $('#table-session-id').val(response.session_id);
                $('#table-name').val(tableNumber);
                $('#tableModal').modal('hide');
            } else {
                showError('خطأ', response.message);
            }
        }
    });
}

// معالجة الطلب
function processOrder() {
    if (cart.length === 0) {
        showError('خطأ', 'السلة فارغة');
        return;
    }
    
    const orderType = $('#order-type').val();
    
    if (orderType === 'dine_in' && !currentTable) {
        showError('خطأ', 'يجب اختيار طاولة للطلبات الداخلية');
        return;
    }
    
    const orderData = {
        order_type: orderType,
        customer_id: currentCustomer ? currentCustomer.id : null,
        customer_name: currentCustomer ? currentCustomer.name : null,
        customer_phone: currentCustomer ? currentCustomer.phone : null,
        table_session_id: currentTable ? currentTable.session_id : null,
        items: cart
    };
    
    $.ajax({
        url: 'ajax/create_order.php',
        type: 'POST',
        data: JSON.stringify(orderData),
        contentType: 'application/json',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showSuccess('تم إنشاء الطلب بنجاح', 'رقم الطلب: ' + response.order_number);
                
                // طباعة الفاتورة
                if (confirm('هل تريد طباعة الفاتورة؟')) {
                    printReceipt(response.order_id);
                }
                
                // مسح السلة
                cart = [];
                currentCustomer = null;
                currentTable = null;
                
                // إعادة تعيين النموذج
                $('#customer-id').val('');
                $('#customer-name').val('');
                $('#table-session-id').val('');
                $('#table-name').val('');
                $('#order-type').val('dine_in');
                $('#table-row').hide();
                
                updateCartDisplay();
                updateOrderSummary();
                
            } else {
                showError('خطأ', response.message);
            }
        }
    });
}

// تعليق الطلب
function holdOrder() {
    if (cart.length === 0) {
        showError('خطأ', 'السلة فارغة');
        return;
    }
    
    // حفظ الطلب في التخزين المحلي
    const heldOrder = {
        cart: cart,
        customer: currentCustomer,
        table: currentTable,
        order_type: $('#order-type').val(),
        timestamp: new Date().toISOString()
    };
    
    const heldOrders = JSON.parse(localStorage.getItem('heldOrders') || '[]');
    heldOrders.push(heldOrder);
    localStorage.setItem('heldOrders', JSON.stringify(heldOrders));
    
    // مسح السلة الحالية
    cart = [];
    currentCustomer = null;
    currentTable = null;
    
    updateCartDisplay();
    updateOrderSummary();
    
    showSuccess('تم تعليق الطلب بنجاح');
}

// استرجاع الطلبات المعلقة
function showHeldOrders() {
    const heldOrders = JSON.parse(localStorage.getItem('heldOrders') || '[]');
    
    if (heldOrders.length === 0) {
        showError('لا توجد طلبات معلقة');
        return;
    }
    
    let html = '<div class="list-group">';
    heldOrders.forEach((order, index) => {
        const total = order.cart.reduce((sum, item) => sum + item.total_price, 0);
        html += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">طلب معلق ${index + 1}</h6>
                    <small class="text-muted">${new Date(order.timestamp).toLocaleString('ar-SA')}</small>
                    <br>
                    <small>العناصر: ${order.cart.length} | الإجمالي: ${formatCurrency(total)}</small>
                </div>
                <div>
                    <button class="btn btn-sm btn-primary" onclick="restoreHeldOrder(${index})">استرجاع</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteHeldOrder(${index})">حذف</button>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    Swal.fire({
        title: 'الطلبات المعلقة',
        html: html,
        width: '600px',
        showConfirmButton: false,
        showCloseButton: true
    });
}

// استرجاع طلب معلق
function restoreHeldOrder(index) {
    const heldOrders = JSON.parse(localStorage.getItem('heldOrders') || '[]');
    const order = heldOrders[index];
    
    cart = order.cart;
    currentCustomer = order.customer;
    currentTable = order.table;
    
    $('#order-type').val(order.order_type);
    $('#customer-id').val(order.customer ? order.customer.id : '');
    $('#customer-name').val(order.customer ? order.customer.name : '');
    $('#table-session-id').val(order.table ? order.table.session_id : '');
    $('#table-name').val(order.table ? order.table.number : '');
    
    if (order.order_type === 'dine_in') {
        $('#table-row').show();
    }
    
    // حذف الطلب من القائمة
    heldOrders.splice(index, 1);
    localStorage.setItem('heldOrders', JSON.stringify(heldOrders));
    
    updateCartDisplay();
    updateOrderSummary();
    
    Swal.close();
    showSuccess('تم استرجاع الطلب بنجاح');
}

// حذف طلب معلق
function deleteHeldOrder(index) {
    const heldOrders = JSON.parse(localStorage.getItem('heldOrders') || '[]');
    heldOrders.splice(index, 1);
    localStorage.setItem('heldOrders', JSON.stringify(heldOrders));
    
    showHeldOrders(); // إعادة عرض القائمة
}

// اختصارات لوحة المفاتيح
$(document).keydown(function(e) {
    // F1 - عميل جديد
    if (e.key === 'F1') {
        e.preventDefault();
        showCustomerModal();
    }
    
    // F2 - طاولة
    if (e.key === 'F2') {
        e.preventDefault();
        showTableModal();
    }
    
    // F3 - تأكيد الطلب
    if (e.key === 'F3') {
        e.preventDefault();
        processOrder();
    }
    
    // F4 - تعليق الطلب
    if (e.key === 'F4') {
        e.preventDefault();
        holdOrder();
    }
    
    // F5 - الطلبات المعلقة
    if (e.key === 'F5') {
        e.preventDefault();
        showHeldOrders();
    }
    
    // Escape - مسح السلة
    if (e.key === 'Escape') {
        e.preventDefault();
        clearCart();
    }
});

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}
