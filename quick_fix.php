<?php
/**
 * إصلاح سريع للمشاكل الشائعة
 * Quick Fix for Common Issues
 * Restaurant Management System
 */

echo "بدء الإصلاح السريع...\n\n";

// 1. إنشاء المجلدات المطلوبة
$required_dirs = array('logs', 'uploads', 'temp', 'backups', 'uploads/products', 'uploads/receipts');

echo "1. إنشاء المجلدات المطلوبة:\n";
foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "   ✓ تم إنشاء مجلد: $dir\n";
        } else {
            echo "   ✗ فشل في إنشاء مجلد: $dir\n";
        }
    } else {
        echo "   - مجلد موجود: $dir\n";
    }
}

// 2. إنشاء ملفات .htaccess للحماية
echo "\n2. إنشاء ملفات الحماية:\n";

$protected_dirs = array('logs', 'temp', 'backups', 'config');
$htaccess_content = "Order deny,allow\nDeny from all";

foreach ($protected_dirs as $dir) {
    if (is_dir($dir)) {
        $htaccess_file = $dir . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            if (file_put_contents($htaccess_file, $htaccess_content)) {
                echo "   ✓ تم إنشاء حماية: $htaccess_file\n";
            } else {
                echo "   ✗ فشل في إنشاء: $htaccess_file\n";
            }
        } else {
            echo "   - ملف حماية موجود: $htaccess_file\n";
        }
    }
}

// 3. إصلاح صلاحيات الملفات
echo "\n3. إصلاح صلاحيات الملفات:\n";
$writable_dirs = array('logs', 'uploads', 'temp', 'backups');

foreach ($writable_dirs as $dir) {
    if (is_dir($dir)) {
        if (chmod($dir, 0755)) {
            echo "   ✓ تم إصلاح صلاحيات: $dir\n";
        } else {
            echo "   ✗ فشل في إصلاح صلاحيات: $dir\n";
        }
    }
}

// 4. إنشاء ملف index.php للحماية في المجلدات الحساسة
echo "\n4. إنشاء ملفات الحماية الإضافية:\n";
$index_content = "<?php\n// Access denied\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n?>";

foreach ($protected_dirs as $dir) {
    if (is_dir($dir)) {
        $index_file = $dir . '/index.php';
        if (!file_exists($index_file)) {
            if (file_put_contents($index_file, $index_content)) {
                echo "   ✓ تم إنشاء حماية إضافية: $index_file\n";
            } else {
                echo "   ✗ فشل في إنشاء: $index_file\n";
            }
        } else {
            echo "   - ملف حماية موجود: $index_file\n";
        }
    }
}

// 5. إنشاء ملف robots.txt
echo "\n5. إنشاء ملف robots.txt:\n";
$robots_content = "User-agent: *\nDisallow: /config/\nDisallow: /logs/\nDisallow: /temp/\nDisallow: /backups/\nDisallow: /includes/\nDisallow: /models/\n";

if (!file_exists('robots.txt')) {
    if (file_put_contents('robots.txt', $robots_content)) {
        echo "   ✓ تم إنشاء ملف robots.txt\n";
    } else {
        echo "   ✗ فشل في إنشاء robots.txt\n";
    }
} else {
    echo "   - ملف robots.txt موجود\n";
}

// 6. إنشاء ملف .gitignore
echo "\n6. إنشاء ملف .gitignore:\n";
$gitignore_content = "# Logs\nlogs/\n*.log\n\n# Uploads\nuploads/\n\n# Temporary files\ntemp/\n\n# Backups\nbackups/\n\n# Configuration (if contains sensitive data)\n# config/database.php\n\n# IDE files\n.vscode/\n.idea/\n*.swp\n*.swo\n\n# OS files\n.DS_Store\nThumbs.db\n";

if (!file_exists('.gitignore')) {
    if (file_put_contents('.gitignore', $gitignore_content)) {
        echo "   ✓ تم إنشاء ملف .gitignore\n";
    } else {
        echo "   ✗ فشل في إنشاء .gitignore\n";
    }
} else {
    echo "   - ملف .gitignore موجود\n";
}

// 7. التحقق من ملفات التكوين
echo "\n7. التحقق من ملفات التكوين:\n";
$config_files = array(
    'config/database.php' => 'إعدادات قاعدة البيانات',
    'config/init.php' => 'تهيئة النظام',
    'config/constants.php' => 'ثوابت النظام'
);

foreach ($config_files as $file => $description) {
    if (file_exists($file)) {
        echo "   ✓ $description: موجود\n";
        
        // التحقق من بناء الجملة
        $output = array();
        $return_var = 0;
        exec("php -l $file 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "     ✓ بناء الجملة صحيح\n";
        } else {
            echo "     ✗ خطأ في بناء الجملة: " . implode(' ', $output) . "\n";
        }
    } else {
        echo "   ✗ $description: غير موجود\n";
    }
}

// 8. إنشاء ملف اختبار قاعدة البيانات
echo "\n8. إنشاء ملف اختبار قاعدة البيانات:\n";
$db_test_content = '<?php
// اختبار الاتصال بقاعدة البيانات
require_once "config/database.php";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS
    );
    
    echo "✓ الاتصال بقاعدة البيانات نجح\n";
    
    // اختبار جدول المستخدمين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "✓ عدد المستخدمين: " . $result["count"] . "\n";
    
} catch (Exception $e) {
    echo "✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
}
?>';

if (file_put_contents('test_database.php', $db_test_content)) {
    echo "   ✓ تم إنشاء ملف اختبار قاعدة البيانات\n";
} else {
    echo "   ✗ فشل في إنشاء ملف اختبار قاعدة البيانات\n";
}

// 9. إنشاء ملف معلومات النظام
echo "\n9. إنشاء ملف معلومات النظام:\n";
$info_content = '<?php
// معلومات النظام
echo "<h2>معلومات النظام</h2>";
echo "<p><strong>إصدار PHP:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>خادم الويب:</strong> " . $_SERVER["SERVER_SOFTWARE"] . "</p>";
echo "<p><strong>نظام التشغيل:</strong> " . PHP_OS . "</p>";
echo "<p><strong>الذاكرة المتاحة:</strong> " . ini_get("memory_limit") . "</p>";
echo "<p><strong>حد رفع الملفات:</strong> " . ini_get("upload_max_filesize") . "</p>";
echo "<p><strong>وقت التنفيذ الأقصى:</strong> " . ini_get("max_execution_time") . " ثانية</p>";

echo "<h3>الامتدادات المثبتة:</h3>";
$extensions = array("pdo", "pdo_mysql", "json", "mbstring", "gd", "curl", "openssl");
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? "✓ مثبت" : "✗ غير مثبت";
    echo "<p><strong>$ext:</strong> $status</p>";
}

phpinfo();
?>';

if (file_put_contents('system_info.php', $info_content)) {
    echo "   ✓ تم إنشاء ملف معلومات النظام\n";
} else {
    echo "   ✗ فشل في إنشاء ملف معلومات النظام\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "تم الانتهاء من الإصلاح السريع!\n\n";

echo "الخطوات التالية:\n";
echo "1. قم بتشغيل: php test_system.php\n";
echo "2. اختبر الاتصال: php test_database.php\n";
echo "3. راجع معلومات النظام: system_info.php\n";
echo "4. اختبر النظام في المتصفح\n\n";

echo "في حالة استمرار المشاكل:\n";
echo "• تحقق من سجل الأخطاء: logs/error.log\n";
echo "• تأكد من إعدادات Apache\n";
echo "• راجع ملف README.md\n";

echo "\nتم بنجاح! ✓\n";
?>
