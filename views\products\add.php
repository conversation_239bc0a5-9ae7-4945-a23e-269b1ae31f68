<?php
/**
 * إضافة منتج جديد
 * Add New Product
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Product.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('products.create');

$product_model = new Product();
$error = '';
$success = '';

// معالجة إضافة المنتج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitize($_POST['name'] ?? '');
    $name_ar = sanitize($_POST['name_ar'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $category_id = $_POST['category_id'] ?? null;
    $kitchen_printer_id = $_POST['kitchen_printer_id'] ?? null;
    $base_price = floatval($_POST['base_price'] ?? 0);
    $cost_price = floatval($_POST['cost_price'] ?? 0);
    $is_recipe_based = isset($_POST['is_recipe_based']) ? 1 : 0;
    $is_stock_item = isset($_POST['is_stock_item']) ? 1 : 0;
    $preparation_time = intval($_POST['preparation_time'] ?? 0);
    $calories = intval($_POST['calories'] ?? 0);
    $sort_order = intval($_POST['sort_order'] ?? 0);
    
    // التحقق من البيانات المطلوبة
    if (empty($name_ar) || empty($category_id) || $base_price <= 0) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        // معالجة رفع الصورة
        $image_filename = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] == UPLOAD_ERR_OK) {
            $upload_result = uploadFile($_FILES['image'], ['jpg', 'jpeg', 'png', 'gif']);
            if ($upload_result['success']) {
                $image_filename = $upload_result['filename'];
            } else {
                $error = $upload_result['error'];
            }
        }
        
        if (!$error) {
            $data = [
                'name' => $name,
                'name_ar' => $name_ar,
                'description' => $description,
                'image' => $image_filename,
                'category_id' => $category_id,
                'kitchen_printer_id' => $kitchen_printer_id,
                'base_price' => $base_price,
                'cost_price' => $cost_price,
                'is_recipe_based' => $is_recipe_based,
                'is_stock_item' => $is_stock_item,
                'preparation_time' => $preparation_time,
                'calories' => $calories,
                'sort_order' => $sort_order
            ];
            
            $result = $product_model->create($data);
            
            if ($result['success']) {
                $_SESSION['success'] = $result['message'];
                redirect('list.php');
            } else {
                $error = $result['message'];
            }
        }
    }
}

// الحصول على الفئات وطابعات المطبخ
$categories = DB::select("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order");
$kitchen_printers = DB::select("SELECT * FROM kitchen_printers WHERE is_active = 1 ORDER BY name_ar");

$page_title = 'إضافة منتج جديد';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إضافة منتج جديد</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="list.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للقائمة
                    </a>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">معلومات المنتج</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name_ar" class="form-label">اسم المنتج بالعربية <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name_ar" name="name_ar" 
                                                   value="<?php echo htmlspecialchars($_POST['name_ar'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">اسم المنتج بالإنجليزية</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="category_id" class="form-label">الفئة <span class="text-danger">*</span></label>
                                            <select class="form-select" id="category_id" name="category_id" required>
                                                <option value="">اختر الفئة</option>
                                                <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>" 
                                                        <?php echo ($_POST['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                                    <?php echo $category['name_ar']; ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="kitchen_printer_id" class="form-label">طابعة المطبخ</label>
                                            <select class="form-select" id="kitchen_printer_id" name="kitchen_printer_id">
                                                <option value="">اختر الطابعة</option>
                                                <?php foreach ($kitchen_printers as $printer): ?>
                                                <option value="<?php echo $printer['id']; ?>" 
                                                        <?php echo ($_POST['kitchen_printer_id'] ?? '') == $printer['id'] ? 'selected' : ''; ?>>
                                                    <?php echo $printer['name_ar']; ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="base_price" class="form-label">السعر الأساسي (ر.س) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="base_price" name="base_price" 
                                                   step="0.01" min="0" value="<?php echo $_POST['base_price'] ?? ''; ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="cost_price" class="form-label">تكلفة التحضير (ر.س)</label>
                                            <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                                   step="0.01" min="0" value="<?php echo $_POST['cost_price'] ?? ''; ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="preparation_time" class="form-label">وقت التحضير (دقيقة)</label>
                                            <input type="number" class="form-control" id="preparation_time" name="preparation_time" 
                                                   min="0" value="<?php echo $_POST['preparation_time'] ?? ''; ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="calories" class="form-label">السعرات الحرارية</label>
                                            <input type="number" class="form-control" id="calories" name="calories" 
                                                   min="0" value="<?php echo $_POST['calories'] ?? ''; ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sort_order" class="form-label">ترتيب العرض</label>
                                            <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                                   min="0" value="<?php echo $_POST['sort_order'] ?? '0'; ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="image" class="form-label">صورة المنتج</label>
                                    <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                    <div class="form-text">الحد الأقصى: 5 ميجابايت. الأنواع المدعومة: JPG, PNG, GIF</div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_recipe_based" name="is_recipe_based" 
                                                   <?php echo isset($_POST['is_recipe_based']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="is_recipe_based">
                                                منتج مبني على وصفة
                                            </label>
                                            <div class="form-text">يتطلب مكونات من المخزون</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_stock_item" name="is_stock_item" 
                                                   <?php echo isset($_POST['is_stock_item']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="is_stock_item">
                                                عنصر مخزني
                                            </label>
                                            <div class="form-text">يتم خصمه من المخزون مباشرة</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="list.php" class="btn btn-secondary">إلغاء</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المنتج
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">نصائح</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    استخدم أسماء واضحة ومفهومة للمنتجات
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-image text-info me-2"></i>
                                    أضف صورة جذابة لزيادة المبيعات
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    حدد وقت التحضير بدقة لتحسين الخدمة
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-calculator text-success me-2"></i>
                                    احسب تكلفة التحضير لمعرفة الربح
                                </li>
                                <li>
                                    <i class="fas fa-list text-secondary me-2"></i>
                                    يمكنك إضافة الأحجام والإضافات لاحقاً
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">معاينة السعر</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <small class="text-muted">السعر الأساسي</small>
                                        <div class="h5 text-primary" id="price-preview">0.00 ر.س</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <small class="text-muted">الربح المتوقع</small>
                                        <div class="h5 text-success" id="profit-preview">0.00 ر.س</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
$additional_js = '
<script>
$(document).ready(function() {
    // تحديث معاينة السعر
    function updatePricePreview() {
        let basePrice = parseFloat($("#base_price").val()) || 0;
        let costPrice = parseFloat($("#cost_price").val()) || 0;
        let profit = basePrice - costPrice;
        
        $("#price-preview").text(formatCurrency(basePrice));
        $("#profit-preview").text(formatCurrency(profit));
        
        if (profit < 0) {
            $("#profit-preview").removeClass("text-success").addClass("text-danger");
        } else {
            $("#profit-preview").removeClass("text-danger").addClass("text-success");
        }
    }
    
    $("#base_price, #cost_price").on("input", updatePricePreview);
    updatePricePreview();
    
    // معاينة الصورة
    $("#image").on("change", function() {
        let file = this.files[0];
        if (file) {
            let reader = new FileReader();
            reader.onload = function(e) {
                // يمكن إضافة معاينة الصورة هنا
            };
            reader.readAsDataURL(file);
        }
    });
    
    // التحقق من النموذج
    $("form").on("submit", function(e) {
        let nameAr = $("#name_ar").val().trim();
        let categoryId = $("#category_id").val();
        let basePrice = parseFloat($("#base_price").val());
        
        if (!nameAr || !categoryId || !basePrice || basePrice <= 0) {
            e.preventDefault();
            Swal.fire({
                title: "خطأ",
                text: "يرجى ملء جميع الحقول المطلوبة",
                icon: "error"
            });
            return false;
        }
    });
});

function formatCurrency(amount) {
    return new Intl.NumberFormat("ar-SA", {
        style: "currency",
        currency: "SAR",
        minimumFractionDigits: 2
    }).format(amount);
}
</script>
';

include '../layouts/footer.php';
?>
