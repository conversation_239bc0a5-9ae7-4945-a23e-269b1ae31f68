<?php
/**
 * الحصول على تفاصيل الطاولة
 * Get Table Details
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('tables.view');

$table_id = isset($_POST['table_id']) ? $_POST['table_id'] : null;

if (!$table_id) {
    echo '<div class="alert alert-danger">معرف الطاولة مطلوب</div>';
    exit;
}

// الحصول على معلومات الطاولة
$table = DB::selectOne("SELECT * FROM tables WHERE id = ?", [$table_id]);

if (!$table) {
    echo '<div class="alert alert-danger">الطاولة غير موجودة</div>';
    exit;
}

// الحصول على الجلسة النشطة إن وجدت
$active_session = DB::selectOne("
    SELECT ts.*, 
           COUNT(o.id) as active_orders,
           SUM(o.total_amount) as session_total,
           c.name as customer_name,
           c.phone as customer_phone
    FROM table_sessions ts
    LEFT JOIN orders o ON ts.id = o.table_session_id AND o.status NOT IN ('completed', 'cancelled')
    LEFT JOIN customers c ON ts.customer_id = c.id
    WHERE ts.table_id = ? AND ts.status = 'active'
    GROUP BY ts.id
", [$table_id]);

// الحصول على آخر الجلسات
$recent_sessions = DB::select("
    SELECT ts.*, 
           c.name as customer_name,
           COUNT(o.id) as total_orders,
           SUM(o.total_amount) as session_total
    FROM table_sessions ts
    LEFT JOIN customers c ON ts.customer_id = c.id
    LEFT JOIN orders o ON ts.id = o.table_session_id
    WHERE ts.table_id = ? AND ts.status = 'completed'
    GROUP BY ts.id
    ORDER BY ts.end_time DESC
    LIMIT 5
", [$table_id]);

// إحصائيات الطاولة
$table_stats = DB::selectOne("
    SELECT 
        COUNT(DISTINCT ts.id) as total_sessions,
        COUNT(DISTINCT o.id) as total_orders,
        SUM(o.total_amount) as total_revenue,
        AVG(TIMESTAMPDIFF(MINUTE, ts.start_time, ts.end_time)) as avg_session_duration
    FROM table_sessions ts
    LEFT JOIN orders o ON ts.id = o.table_session_id AND o.status = 'completed'
    WHERE ts.table_id = ? AND ts.status = 'completed'
", [$table_id]);
?>

<div class="row">
    <div class="col-md-6">
        <h5>معلومات الطاولة</h5>
        <table class="table table-sm">
            <tr>
                <td><strong>رقم الطاولة:</strong></td>
                <td><?php echo $table['table_number']; ?></td>
            </tr>
            <tr>
                <td><strong>السعة:</strong></td>
                <td>
                    <i class="fas fa-users me-1"></i>
                    <?php echo $table['capacity']; ?> شخص
                </td>
            </tr>
            <tr>
                <td><strong>الحالة:</strong></td>
                <td>
                    <?php
                    $status_names = [
                        'available' => 'متاحة',
                        'occupied' => 'مشغولة',
                        'reserved' => 'محجوزة',
                        'maintenance' => 'صيانة'
                    ];
                    $status_classes = [
                        'available' => 'success',
                        'occupied' => 'danger',
                        'reserved' => 'warning',
                        'maintenance' => 'secondary'
                    ];
                    ?>
                    <span class="badge bg-<?php echo $status_classes[$table['status']]; ?>">
                        <?php echo $status_names[$table['status']]; ?>
                    </span>
                </td>
            </tr>
            <?php if ($table['location']): ?>
            <tr>
                <td><strong>الموقع:</strong></td>
                <td><?php echo $table['location']; ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($table['notes']): ?>
            <tr>
                <td><strong>ملاحظات:</strong></td>
                <td><?php echo $table['notes']; ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
    
    <div class="col-md-6">
        <h5>إحصائيات الطاولة</h5>
        <div class="row">
            <div class="col-6">
                <div class="card bg-primary text-white mb-2">
                    <div class="card-body text-center">
                        <h4><?php echo number_format($table_stats['total_sessions']); ?></h4>
                        <small>إجمالي الجلسات</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card bg-success text-white mb-2">
                    <div class="card-body text-center">
                        <h4><?php echo number_format($table_stats['total_orders']); ?></h4>
                        <small>إجمالي الطلبات</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card bg-info text-white mb-2">
                    <div class="card-body text-center">
                        <h4><?php echo formatCurrency($table_stats['total_revenue']); ?></h4>
                        <small>إجمالي المبيعات</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card bg-warning text-white mb-2">
                    <div class="card-body text-center">
                        <h4><?php echo round($table_stats['avg_session_duration']); ?></h4>
                        <small>متوسط مدة الجلسة (دقيقة)</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الجلسة النشطة -->
<?php if ($active_session): ?>
<hr>
<h6>الجلسة النشطة</h6>
<div class="card border-primary">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p class="mb-1"><strong>كود الجلسة:</strong> <?php echo $active_session['session_code']; ?></p>
                <p class="mb-1"><strong>وقت البداية:</strong> <?php echo formatDate($active_session['start_time'], 'd/m/Y H:i'); ?></p>
                <p class="mb-1"><strong>عدد العملاء:</strong> <?php echo $active_session['total_customers']; ?></p>
                <?php if ($active_session['customer_name']): ?>
                <p class="mb-1"><strong>العميل:</strong> <?php echo $active_session['customer_name']; ?></p>
                <?php endif; ?>
            </div>
            <div class="col-md-6">
                <p class="mb-1"><strong>الطلبات النشطة:</strong> <?php echo $active_session['active_orders']; ?></p>
                <p class="mb-1"><strong>إجمالي الجلسة:</strong> <?php echo formatCurrency($active_session['session_total']); ?></p>
                <p class="mb-1">
                    <strong>المدة المنقضية:</strong> 
                    <?php 
                    $elapsed = time() - strtotime($active_session['start_time']);
                    echo gmdate('H:i', $elapsed);
                    ?>
                </p>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="../orders/list.php?table_session_id=<?php echo $active_session['id']; ?>" 
               class="btn btn-primary btn-sm">
                <i class="fas fa-shopping-cart me-1"></i>
                عرض الطلبات
            </a>
            <a href="../pos/index.php?table_id=<?php echo $table_id; ?>" 
               class="btn btn-success btn-sm">
                <i class="fas fa-plus me-1"></i>
                طلب جديد
            </a>
            <button type="button" class="btn btn-warning btn-sm" 
                    onclick="endTableSession(<?php echo $active_session['id']; ?>)">
                <i class="fas fa-stop me-1"></i>
                إنهاء الجلسة
            </button>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- آخر الجلسات -->
<?php if (!empty($recent_sessions)): ?>
<hr>
<h6>آخر الجلسات</h6>
<div class="table-responsive">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>كود الجلسة</th>
                <th>العميل</th>
                <th>التاريخ</th>
                <th>المدة</th>
                <th>الطلبات</th>
                <th>الإجمالي</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($recent_sessions as $session): ?>
            <tr>
                <td><?php echo $session['session_code']; ?></td>
                <td><?php echo $session['customer_name'] ?: 'غير محدد'; ?></td>
                <td><?php echo formatDate($session['start_time'], 'd/m/Y'); ?></td>
                <td>
                    <?php 
                    if ($session['end_time']) {
                        $duration = strtotime($session['end_time']) - strtotime($session['start_time']);
                        echo gmdate('H:i', $duration);
                    } else {
                        echo 'غير محدد';
                    }
                    ?>
                </td>
                <td><span class="badge bg-info"><?php echo $session['total_orders']; ?></span></td>
                <td><?php echo formatCurrency($session['session_total']); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<?php endif; ?>

<!-- أزرار الإجراءات -->
<hr>
<div class="text-center">
    <?php if ($table['status'] === 'available'): ?>
    <button type="button" class="btn btn-success" onclick="startNewSession(<?php echo $table_id; ?>)">
        <i class="fas fa-play me-2"></i>
        بدء جلسة جديدة
    </button>
    <?php endif; ?>
    
    <?php if ($table['status'] === 'occupied' && $active_session): ?>
    <a href="../pos/index.php?table_id=<?php echo $table_id; ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        طلب جديد
    </a>
    <button type="button" class="btn btn-warning" onclick="endTableSession(<?php echo $active_session['id']; ?>)">
        <i class="fas fa-stop me-2"></i>
        إنهاء الجلسة
    </button>
    <?php endif; ?>
    
    <a href="../tables/edit.php?id=<?php echo $table_id; ?>" class="btn btn-outline-secondary">
        <i class="fas fa-edit me-2"></i>
        تعديل الطاولة
    </a>
</div>

<script>
function startNewSession(tableId) {
    Swal.fire({
        title: 'بدء جلسة جديدة',
        html: `
            <div class="mb-3">
                <label class="form-label">عدد العملاء</label>
                <input type="number" id="customerCount" class="form-control" min="1" value="1">
            </div>
            <div class="mb-3">
                <label class="form-label">العميل (اختياري)</label>
                <select id="customerId" class="form-select">
                    <option value="">اختر العميل</option>
                    <!-- يمكن تحميل قائمة العملاء هنا -->
                </select>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'بدء الجلسة',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            const customerCount = document.getElementById('customerCount').value;
            const customerId = document.getElementById('customerId').value;
            
            if (!customerCount || customerCount < 1) {
                Swal.showValidationMessage('يرجى إدخال عدد العملاء');
                return false;
            }
            
            return { customerCount, customerId };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب بدء الجلسة
            $.ajax({
                url: 'ajax/start_session.php',
                type: 'POST',
                data: {
                    table_id: tableId,
                    customer_count: result.value.customerCount,
                    customer_id: result.value.customerId
                },
                success: function(response) {
                    const data = JSON.parse(response);
                    if (data.success) {
                        Swal.fire('تم!', 'تم بدء الجلسة بنجاح', 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ!', data.message, 'error');
                    }
                }
            });
        }
    });
}

function endTableSession(sessionId) {
    Swal.fire({
        title: 'إنهاء الجلسة',
        text: 'هل أنت متأكد من إنهاء هذه الجلسة؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، أنهي الجلسة',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: 'ajax/end_session.php',
                type: 'POST',
                data: { session_id: sessionId },
                success: function(response) {
                    const data = JSON.parse(response);
                    if (data.success) {
                        Swal.fire('تم!', 'تم إنهاء الجلسة بنجاح', 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ!', data.message, 'error');
                    }
                }
            });
        }
    });
}
</script>
