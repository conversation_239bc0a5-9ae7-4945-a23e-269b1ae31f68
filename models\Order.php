<?php
/**
 * نموذج الطلبات
 * Order Model
 * Restaurant Management System
 */

class Order {
    private $table = 'orders';
    
    // الحصول على جميع الطلبات
    public function getAll($limit = null, $status = null) {
        $where = "";
        $params = array();
        
        if ($status) {
            $where = "WHERE o.status = ?";
            $params[] = $status;
        }
        
        $limit_clause = $limit ? "LIMIT ?" : "";
        if ($limit) {
            $params[] = $limit;
        }
        
        $sql = "SELECT o.*, c.name as customer_name, c.phone as customer_phone,
                       ts.session_code, t.table_number,
                       u.full_name as created_by_name
                FROM {$this->table} o 
                LEFT JOIN customers c ON o.customer_id = c.id 
                LEFT JOIN table_sessions ts ON o.table_session_id = ts.id 
                LEFT JOIN tables t ON ts.table_id = t.id 
                LEFT JOIN users u ON o.created_by = u.id 
                {$where}
                ORDER BY o.created_at DESC 
                {$limit_clause}";
        
        return DB::select($sql, $params);
    }
    
    // الحصول على طلب بالمعرف
    public function getById($id) {
        $sql = "SELECT o.*, c.name as customer_name, c.phone as customer_phone,
                       ts.session_code, t.table_number,
                       u.full_name as created_by_name
                FROM {$this->table} o 
                LEFT JOIN customers c ON o.customer_id = c.id 
                LEFT JOIN table_sessions ts ON o.table_session_id = ts.id 
                LEFT JOIN tables t ON ts.table_id = t.id 
                LEFT JOIN users u ON o.created_by = u.id 
                WHERE o.id = ?";
        
        return DB::selectOne($sql, array($id));
    }
    
    // الحصول على طلب برقم الطلب
    public function getByOrderNumber($order_number) {
        $sql = "SELECT o.*, c.name as customer_name, c.phone as customer_phone,
                       ts.session_code, t.table_number
                FROM {$this->table} o 
                LEFT JOIN customers c ON o.customer_id = c.id 
                LEFT JOIN table_sessions ts ON o.table_session_id = ts.id 
                LEFT JOIN tables t ON ts.table_id = t.id 
                WHERE o.order_number = ?";
        
        return DB::selectOne($sql, array($order_number));
    }
    
    // إنشاء طلب جديد
    public function create($data) {
        DB::beginTransaction();
        
        try {
            // إنشاء رقم الطلب
            $order_number = generateOrderNumber();
            
            $sql = "INSERT INTO {$this->table} 
                    (order_number, order_type, table_session_id, customer_id, 
                     customer_name, customer_phone, delivery_address, 
                     special_instructions, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = array(
                $order_number,
                $data['order_type'],
                $data['table_session_id'] ?? null,
                $data['customer_id'] ?? null,
                $data['customer_name'] ?? null,
                $data['customer_phone'] ?? null,
                $data['delivery_address'] ?? null,
                $data['special_instructions'] ?? null,
                $_SESSION['user_id']
            );
            
            $order_id = DB::insert($sql, $params);
            
            if (!$order_id) {
                throw new Exception('فشل في إنشاء الطلب');
            }
            
            DB::commit();
            logActivity('create_order', 'orders', $order_id, null, $data);
            
            return array(
                'success' => true, 
                'id' => $order_id, 
                'order_number' => $order_number,
                'message' => 'تم إنشاء الطلب بنجاح'
            );
            
        } catch (Exception $e) {
            DB::rollback();
            return array('success' => false, 'message' => $e->getMessage());
        }
    }
    
    // تحديث طلب
    public function update($id, $data) {
        $old_data = $this->getById($id);
        if (!$old_data) {
            return array('success' => false, 'message' => 'الطلب غير موجود');
        }
        
        $fields = array();
        $params = array();
        
        foreach ($data as $key => $value) {
            $fields[] = "{$key} = ?";
            $params[] = $value;
        }
        
        $params[] = $id;
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";
        
        if (DB::execute($sql, $params)) {
            logActivity('update_order', 'orders', $id, $old_data, $data);
            return array('success' => true, 'message' => 'تم تحديث الطلب بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في تحديث الطلب');
    }
    
    // تحديث حالة الطلب
    public function updateStatus($id, $status) {
        $order = $this->getById($id);
        if (!$order) {
            return array('success' => false, 'message' => 'الطلب غير موجود');
        }
        
        $sql = "UPDATE {$this->table} SET status = ? WHERE id = ?";
        
        if (DB::execute($sql, array($status, $id))) {
            logActivity('update_order_status', 'orders', $id, 
                       array('status' => $order['status']), 
                       array('status' => $status));
            
            return array('success' => true, 'message' => 'تم تحديث حالة الطلب بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في تحديث حالة الطلب');
    }
    
    // إضافة عنصر للطلب
    public function addItem($order_id, $product_id, $quantity, $size_id = null, $special_instructions = null) {
        // الحصول على معلومات المنتج
        $product = DB::selectOne("SELECT * FROM products WHERE id = ?", array($product_id));
        if (!$product) {
            return array('success' => false, 'message' => 'المنتج غير موجود');
        }
        
        $unit_price = $product['base_price'];
        
        // إضافة سعر الحجم إذا وجد
        if ($size_id) {
            $size = DB::selectOne("SELECT * FROM product_sizes WHERE id = ? AND product_id = ?", 
                                 array($size_id, $product_id));
            if ($size) {
                $unit_price += $size['price_modifier'];
            }
        }
        
        $total_price = $unit_price * $quantity;
        
        $sql = "INSERT INTO order_items 
                (order_id, product_id, product_size_id, quantity, unit_price, total_price, 
                 special_instructions, kitchen_printer_id) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = array(
            $order_id,
            $product_id,
            $size_id,
            $quantity,
            $unit_price,
            $total_price,
            $special_instructions,
            $product['kitchen_printer_id']
        );
        
        $item_id = DB::insert($sql, $params);
        
        if ($item_id) {
            // تحديث إجمالي الطلب
            $this->updateTotals($order_id);
            
            // إرسال إلى المطبخ
            $this->sendToKitchen($item_id);
            
            return array('success' => true, 'id' => $item_id, 'message' => 'تم إضافة العنصر بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في إضافة العنصر');
    }
    
    // إضافة إضافة لعنصر الطلب
    public function addItemAddon($item_id, $addon_id, $quantity = 1) {
        $addon = DB::selectOne("SELECT * FROM product_addons WHERE id = ?", array($addon_id));
        if (!$addon) {
            return array('success' => false, 'message' => 'الإضافة غير موجودة');
        }
        
        $total_price = $addon['price'] * $quantity;
        
        $sql = "INSERT INTO order_item_addons 
                (order_item_id, product_addon_id, quantity, unit_price, total_price) 
                VALUES (?, ?, ?, ?, ?)";
        
        $addon_item_id = DB::insert($sql, array($item_id, $addon_id, $quantity, $addon['price'], $total_price));
        
        if ($addon_item_id) {
            // تحديث إجمالي العنصر
            $this->updateItemTotal($item_id);
            
            // تحديث إجمالي الطلب
            $item = DB::selectOne("SELECT order_id FROM order_items WHERE id = ?", array($item_id));
            $this->updateTotals($item['order_id']);
            
            return array('success' => true, 'id' => $addon_item_id, 'message' => 'تم إضافة الإضافة بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في إضافة الإضافة');
    }
    
    // تحديث إجمالي عنصر الطلب
    private function updateItemTotal($item_id) {
        $sql = "UPDATE order_items oi 
                SET total_price = (
                    SELECT oi.unit_price * oi.quantity + COALESCE(SUM(oia.total_price), 0)
                    FROM order_items oi2 
                    LEFT JOIN order_item_addons oia ON oi2.id = oia.order_item_id 
                    WHERE oi2.id = ?
                    GROUP BY oi2.id
                )
                WHERE oi.id = ?";
        
        return DB::execute($sql, array($item_id, $item_id));
    }
    
    // تحديث إجماليات الطلب
    public function updateTotals($order_id) {
        // حساب المجموع الفرعي
        $sql = "SELECT SUM(total_price) as subtotal FROM order_items WHERE order_id = ?";
        $result = DB::selectOne($sql, array($order_id));
        $subtotal = $result['subtotal'] ?? 0;
        
        // حساب الضريبة ورسوم الخدمة
        $tax_rate = getSetting('tax_rate', DEFAULT_TAX_RATE);
        $service_charge_rate = getSetting('service_charge_rate', DEFAULT_SERVICE_CHARGE);
        
        $tax_amount = calculateTax($subtotal, $tax_rate);
        $service_charge = calculateServiceCharge($subtotal, $service_charge_rate);
        $total_amount = $subtotal + $tax_amount + $service_charge;
        
        // تحديث الطلب
        $sql = "UPDATE {$this->table} 
                SET subtotal = ?, tax_amount = ?, service_charge = ?, total_amount = ? 
                WHERE id = ?";
        
        return DB::execute($sql, array($subtotal, $tax_amount, $service_charge, $total_amount, $order_id));
    }
    
    // إرسال عنصر إلى المطبخ
    private function sendToKitchen($item_id) {
        $sql = "SELECT oi.*, p.name_ar as product_name, o.order_number, 
                       ts.session_code, t.table_number
                FROM order_items oi 
                JOIN products p ON oi.product_id = p.id 
                JOIN orders o ON oi.order_id = o.id 
                LEFT JOIN table_sessions ts ON o.table_session_id = ts.id 
                LEFT JOIN tables t ON ts.table_id = t.id 
                WHERE oi.id = ?";
        
        $item = DB::selectOne($sql, array($item_id));
        
        if ($item && $item['kitchen_printer_id']) {
            // جمع الإضافات
            $addons_sql = "SELECT pa.name_ar, oia.quantity 
                          FROM order_item_addons oia 
                          JOIN product_addons pa ON oia.product_addon_id = pa.id 
                          WHERE oia.order_item_id = ?";
            $addons = DB::select($addons_sql, array($item_id));
            
            $addons_text = '';
            foreach ($addons as $addon) {
                $addons_text .= $addon['name_ar'] . ' (' . $addon['quantity'] . '), ';
            }
            $addons_text = rtrim($addons_text, ', ');
            
            // إدراج في جدول طلبات المطبخ
            $kitchen_sql = "INSERT INTO kitchen_orders 
                           (order_item_id, kitchen_printer_id, order_number, table_number, 
                            product_name, product_name_ar, quantity, special_instructions, addons) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            DB::insert($kitchen_sql, array(
                $item_id,
                $item['kitchen_printer_id'],
                $item['order_number'],
                $item['table_number'] ?? '',
                $item['product_name'],
                $item['product_name'],
                $item['quantity'],
                $item['special_instructions'],
                $addons_text
            ));
        }
    }
    
    // الحصول على عناصر الطلب
    public function getItems($order_id) {
        $sql = "SELECT oi.*, p.name_ar as product_name, ps.name_ar as size_name
                FROM order_items oi 
                JOIN products p ON oi.product_id = p.id 
                LEFT JOIN product_sizes ps ON oi.product_size_id = ps.id 
                WHERE oi.order_id = ?
                ORDER BY oi.id";
        
        return DB::select($sql, array($order_id));
    }
    
    // الحصول على إضافات عنصر الطلب
    public function getItemAddons($item_id) {
        $sql = "SELECT oia.*, pa.name_ar as addon_name
                FROM order_item_addons oia 
                JOIN product_addons pa ON oia.product_addon_id = pa.id 
                WHERE oia.order_item_id = ?";
        
        return DB::select($sql, array($item_id));
    }
    
    // البحث في الطلبات
    public function search($term, $date_from = null, $date_to = null) {
        $where = "WHERE (o.order_number LIKE ? OR o.customer_name LIKE ? OR o.customer_phone LIKE ?)";
        $params = array("%{$term}%", "%{$term}%", "%{$term}%");
        
        if ($date_from) {
            $where .= " AND DATE(o.created_at) >= ?";
            $params[] = $date_from;
        }
        
        if ($date_to) {
            $where .= " AND DATE(o.created_at) <= ?";
            $params[] = $date_to;
        }
        
        $sql = "SELECT o.*, c.name as customer_name, c.phone as customer_phone,
                       ts.session_code, t.table_number
                FROM {$this->table} o 
                LEFT JOIN customers c ON o.customer_id = c.id 
                LEFT JOIN table_sessions ts ON o.table_session_id = ts.id 
                LEFT JOIN tables t ON ts.table_id = t.id 
                {$where}
                ORDER BY o.created_at DESC";
        
        return DB::select($sql, $params);
    }
    
    // إحصائيات الطلبات
    public function getStats($date_from = null, $date_to = null) {
        $where = "";
        $params = array();
        
        if ($date_from && $date_to) {
            $where = "WHERE DATE(created_at) BETWEEN ? AND ?";
            $params = array($date_from, $date_to);
        }
        
        $sql = "SELECT 
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
                    SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_revenue,
                    AVG(CASE WHEN status = 'completed' THEN total_amount ELSE NULL END) as average_order_value
                FROM {$this->table} 
                {$where}";
        
        return DB::selectOne($sql, $params);
    }
}
?>
