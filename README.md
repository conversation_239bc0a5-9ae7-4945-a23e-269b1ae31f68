# نظام إدارة المطعم الاحترافي
## Restaurant Management System

نظام شامل ومتكامل لإدارة المطاعم والوجبات السريعة مصمم خصيصاً للسوق العربي مع دعم كامل للغة العربية (RTL).

---

## 🌟 المميزات الرئيسية

### 🍽️ إدارة الطلبات
- **أنواع الطلبات**: طاولة، سفري، توصيل
- **جلسات الطاولات**: دعم عدة عملاء على نفس الطاولة
- **تغيير الطاولة**: إمكانية نقل الطلب بين الطاولات
- **حالات الطلب**: قيد الانتظار، قيد التحضير، جاهز، مكتمل، ملغي
- **تقسيم الفاتورة**: بالتساوي، حسب الأصناف، حسب النسبة المئوية

### 🛍️ إدارة المنتجات
- **فئات المنتجات**: تصنيف منظم للمنتجات
- **أحجام متعددة**: صغير، وسط، كبير مع أسعار مختلفة
- **إضافات مدفوعة ومجانية**: جبن إضافي، خضروات، إلخ
- **وصفات التحضير**: ربط المنتجات بالمكونات المخزنية
- **حساب التكلفة**: حساب تكلفة التحضير الفعلية

### 📦 إدارة المخزون
- **نظام FEFO**: أول من ينتهي يُستخدم أولاً
- **تنبيهات الصلاحية**: تنبيهات عند قرب انتهاء الصلاحية
- **إدارة الموردين**: معلومات كاملة عن الموردين
- **فواتير الشراء**: تتبع المشتريات والتكاليف
- **تقارير المخزون**: تقارير شاملة عن حالة المخزون

### 👥 إدارة العملاء
- **ملفات العملاء**: بيانات شاملة لكل عميل
- **نظام الولاء**: نقاط ولاء قابلة للاستبدال
- **سجل المشتريات**: تتبع جميع طلبات العميل
- **إدارة الديون**: تتبع المدفوعات والديون

### 📅 نظام الحجوزات
- **حجز الطاولات**: حجز مسبق بالاسم والوقت
- **تحويل الحجز**: تحويل الحجز إلى طلب نشط
- **إدارة الطاولات**: متابعة حالة جميع الطاولات

### 👨‍🍳 إدارة الطهاة
- **أنواع المحاسبة**: نسبة من المبيعات أو أجر يومي
- **حساب المستحقات**: حساب تلقائي للمستحقات الشهرية
- **سجل المدفوعات**: تتبع جميع المدفوعات

### 💵 نظام الورديات
- **فتح وإغلاق الورديات**: مع مبلغ افتتاحي وختامي
- **حساب الفروقات**: مقارنة المبلغ الفعلي بالمتوقع
- **تقارير الورديات**: تقارير مفصلة لكل وردية

### 🖥️ شاشة المطبخ (KDS)
- **عرض الطلبات**: عرض منظم للطلبات حسب المحطة
- **تحديث الحالة**: تحديث حالة الطلبات من قبل الطهاة
- **أولويات الطلبات**: ترتيب الطلبات حسب الأولوية والوقت

### 🎁 نظام العروض والخصومات
- **عروض زمنية**: عروض في أوقات محددة (Happy Hour)
- **عروض الكمية**: اشترِ 2 واحصل على 1
- **عروض العملاء**: عروض خاصة للعملاء المميزين
- **تطبيق تلقائي**: تطبيق العروض تلقائياً في نقاط البيع

### 🔐 الأمان والصلاحيات
- **أدوار المستخدمين**: مدير، كاشير، طباخ، نادل
- **صلاحيات مخصصة**: تحكم دقيق في الصلاحيات
- **سجل النشاطات**: تتبع جميع العمليات والتغييرات

### 📈 التقارير الشاملة
- **تقارير المبيعات**: يومية، أسبوعية، شهرية
- **تقارير المخزون**: حالة المخزون والمنتجات منتهية الصلاحية
- **تقارير العملاء**: أفضل العملاء ونقاط الولاء
- **تقارير الطهاة**: مستحقات الطهاة والورديات

---

## 🛠️ المتطلبات التقنية

### متطلبات الخادم
- **PHP**: 5.6.26 أو أحدث
- **MySQL**: 5.7 أو أحدث
- **Apache/Nginx**: خادم ويب
- **مساحة القرص**: 500 ميجابايت على الأقل

### المتطلبات الإضافية
- **PDO MySQL**: لاتصال قاعدة البيانات
- **GD Extension**: لمعالجة الصور
- **cURL**: للاتصالات الخارجية
- **JSON**: لمعالجة البيانات

---

## 📥 التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/restaurant-system.git
cd restaurant-system
```

### 2. إعداد قاعدة البيانات
1. إنشاء قاعدة بيانات جديدة في MySQL
2. استيراد ملف `db/database.sql`
3. تحديث إعدادات الاتصال في `config/database.php`

### 3. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 logs/
```

### 4. إعداد الخادم
- تأكد من تشغيل Apache/Nginx
- تأكد من تفعيل mod_rewrite (للـ Apache)
- ضبط المجلد الجذر على مجلد المشروع

### 5. الدخول للنظام
- **الرابط**: `http://your-domain/`
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `password`

---

## 🗂️ هيكل المشروع

```
restaurant-system/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── includes/
│   └── functions.php         # الدوال المساعدة
├── models/
│   ├── User.php             # نموذج المستخدمين
│   ├── Product.php          # نموذج المنتجات
│   ├── Order.php            # نموذج الطلبات
│   ├── Customer.php         # نموذج العملاء
│   └── Inventory.php        # نموذج المخزون
├── views/
│   ├── layouts/             # تخطيطات الصفحات
│   ├── pos/                 # نقاط البيع
│   ├── kitchen/             # شاشة المطبخ
│   ├── orders/              # إدارة الطلبات
│   ├── products/            # إدارة المنتجات
│   ├── customers/           # إدارة العملاء
│   ├── inventory/           # إدارة المخزون
│   ├── reports/             # التقارير
│   └── settings/            # الإعدادات
├── assets/
│   ├── css/                 # ملفات الأنماط
│   ├── js/                  # ملفات JavaScript
│   └── images/              # الصور
├── uploads/                 # ملفات المستخدمين
├── logs/                    # ملفات السجلات
├── db/
│   └── database.sql         # هيكل قاعدة البيانات
├── index.php               # الصفحة الرئيسية
├── login.php               # صفحة تسجيل الدخول
└── README.md               # هذا الملف
```

---

## 🎯 الاستخدام السريع

### بدء وردية جديدة
1. تسجيل الدخول للنظام
2. الانتقال إلى "بدء وردية"
3. إدخال المبلغ الافتتاحي
4. النقر على "بدء الوردية"

### إنشاء طلب جديد
1. الانتقال إلى "نقاط البيع"
2. اختيار نوع الطلب (طاولة/سفري/توصيل)
3. إضافة المنتجات للسلة
4. تأكيد الطلب وطباعة الفاتورة

### متابعة المطبخ
1. الانتقال إلى "شاشة المطبخ"
2. مراجعة الطلبات الواردة
3. تحديث حالة الطلبات (بدء التحضير/جاهز)

---

## 🔧 التخصيص والإعدادات

### إعدادات المطعم
- اسم المطعم وبيانات الاتصال
- نسبة الضريبة ورسوم الخدمة
- إعدادات نظام الولاء
- إعدادات الطباعة

### إضافة منتجات جديدة
1. الانتقال إلى "إدارة المنتجات"
2. النقر على "إضافة منتج جديد"
3. ملء بيانات المنتج
4. إضافة الأحجام والإضافات
5. ربط المنتج بوصفة التحضير

### إدارة المستخدمين
1. الانتقال إلى "إدارة المستخدمين"
2. إضافة مستخدم جديد
3. تحديد الدور والصلاحيات
4. تفعيل الحساب

---

## 📱 الدعم والمساعدة

### الأسئلة الشائعة
- **كيفية إعادة تعيين كلمة المرور؟** اتصل بالمدير لإعادة تعيين كلمة المرور
- **كيفية إضافة طابعة جديدة؟** من إعدادات النظام > طابعات المطبخ
- **كيفية عمل نسخة احتياطية؟** استخدم أدوات MySQL لعمل نسخة احتياطية من قاعدة البيانات

### الدعم التقني
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966501234567
- **الموقع**: www.restaurant-system.com

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

---

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل إرسال أي تحديثات.

---

## 📝 سجل التحديثات

### الإصدار 1.0.0
- إطلاق النسخة الأولى من النظام
- جميع الميزات الأساسية متوفرة
- دعم كامل للغة العربية

---

**تم تطوير هذا النظام بعناية فائقة ليلبي احتياجات المطاعم العربية ويوفر تجربة استخدام متميزة.**
