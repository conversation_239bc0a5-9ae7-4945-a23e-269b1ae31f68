<?php
/**
 * الحصول على حالة الطاولات
 * Get Tables Status
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('tables.view');

header('Content-Type: application/json');

try {
    // الحصول على الطاولات مع معلومات الجلسات النشطة
    $sql = "SELECT t.id, t.status,
                   ts.id as session_id, ts.start_time,
                   COUNT(o.id) as active_orders,
                   SUM(o.total_amount) as session_total
            FROM tables t 
            LEFT JOIN table_sessions ts ON t.id = ts.table_id AND ts.status = 'active'
            LEFT JOIN orders o ON ts.id = o.table_session_id AND o.status NOT IN ('completed', 'cancelled')
            GROUP BY t.id
            ORDER BY t.table_number";

    $tables = DB::select($sql);
    
    $result = [];
    
    foreach ($tables as $table) {
        $table_data = [
            'id' => $table['id'],
            'status' => $table['status'],
            'session_info' => null
        ];
        
        // إذا كانت الطاولة مشغولة وبها جلسة نشطة
        if ($table['status'] === 'occupied' && $table['session_id']) {
            $start_time = strtotime($table['start_time']);
            $elapsed = time() - $start_time;
            
            $table_data['session_info'] = [
                'elapsed_time' => gmdate('H:i', $elapsed),
                'active_orders' => $table['active_orders'],
                'total' => formatCurrency($table['session_total'])
            ];
        }
        
        $result[] = $table_data;
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    echo json_encode(['error' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
