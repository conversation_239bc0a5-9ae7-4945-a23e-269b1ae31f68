<?php
/**
 * قائمة الطلبات
 * Orders List
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Order.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('orders.view');

$order_model = new Order();

// معالجة الفلاتر
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-d');
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$search = $_GET['search'] ?? '';

// الحصول على الطلبات
if ($search) {
    $orders = $order_model->search($search, $date_from, $date_to);
} else {
    $where_conditions = [];
    $params = [];
    
    if ($status_filter) {
        $where_conditions[] = "o.status = ?";
        $params[] = $status_filter;
    }
    
    if ($date_from) {
        $where_conditions[] = "DATE(o.created_at) >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $where_conditions[] = "DATE(o.created_at) <= ?";
        $params[] = $date_to;
    }
    
    $where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";
    
    $sql = "SELECT o.*, c.name as customer_name, c.phone as customer_phone,
                   ts.session_code, t.table_number,
                   u.full_name as created_by_name
            FROM orders o 
            LEFT JOIN customers c ON o.customer_id = c.id 
            LEFT JOIN table_sessions ts ON o.table_session_id = ts.id 
            LEFT JOIN tables t ON ts.table_id = t.id 
            LEFT JOIN users u ON o.created_by = u.id 
            {$where_clause}
            ORDER BY o.created_at DESC";
    
    $orders = DB::select($sql, $params);
}

$page_title = 'إدارة الطلبات';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الطلبات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="../pos/index.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> طلب جديد
                        </a>
                    </div>
                </div>
            </div>

            <?php showSessionMessages(); ?>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="رقم الطلب أو اسم العميل">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>قيد الانتظار</option>
                                <option value="confirmed" <?php echo $status_filter == 'confirmed' ? 'selected' : ''; ?>>مؤكد</option>
                                <option value="preparing" <?php echo $status_filter == 'preparing' ? 'selected' : ''; ?>>قيد التحضير</option>
                                <option value="ready" <?php echo $status_filter == 'ready' ? 'selected' : ''; ?>>جاهز</option>
                                <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                <option value="cancelled" <?php echo $status_filter == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الطلبات -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="ordersTable">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>النوع</th>
                                    <th>العميل</th>
                                    <th>الطاولة</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الوقت</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo $order['order_number']; ?></strong>
                                    </td>
                                    <td>
                                        <?php
                                        $type_icons = [
                                            'dine_in' => 'fas fa-chair',
                                            'takeaway' => 'fas fa-shopping-bag',
                                            'delivery' => 'fas fa-truck'
                                        ];
                                        $type_names = [
                                            'dine_in' => 'طاولة',
                                            'takeaway' => 'سفري',
                                            'delivery' => 'توصيل'
                                        ];
                                        ?>
                                        <i class="<?php echo $type_icons[$order['order_type']]; ?> me-1"></i>
                                        <?php echo $type_names[$order['order_type']]; ?>
                                    </td>
                                    <td>
                                        <?php if ($order['customer_name']): ?>
                                            <div><?php echo $order['customer_name']; ?></div>
                                            <small class="text-muted"><?php echo $order['customer_phone']; ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($order['table_number']): ?>
                                            <span class="badge bg-info"><?php echo $order['table_number']; ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo formatCurrency($order['total_amount']); ?></strong>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'pending' => 'warning',
                                            'confirmed' => 'info',
                                            'preparing' => 'primary',
                                            'ready' => 'success',
                                            'completed' => 'dark',
                                            'cancelled' => 'danger'
                                        ];
                                        $status_names = [
                                            'pending' => 'قيد الانتظار',
                                            'confirmed' => 'مؤكد',
                                            'preparing' => 'قيد التحضير',
                                            'ready' => 'جاهز',
                                            'completed' => 'مكتمل',
                                            'cancelled' => 'ملغي'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $status_classes[$order['status']]; ?>">
                                            <?php echo $status_names[$order['status']]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div><?php echo formatDate($order['created_at'], 'd/m/Y'); ?></div>
                                        <small class="text-muted"><?php echo formatDate($order['created_at'], 'H:i'); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="viewOrder(<?php echo $order['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($order['status'] != 'completed' && $order['status'] != 'cancelled'): ?>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <?php if ($order['status'] == 'pending'): ?>
                                                    <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'confirmed')">
                                                        <i class="fas fa-check text-info"></i> تأكيد
                                                    </a></li>
                                                    <?php endif; ?>
                                                    <?php if (in_array($order['status'], ['confirmed', 'preparing'])): ?>
                                                    <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'ready')">
                                                        <i class="fas fa-check-circle text-success"></i> جاهز
                                                    </a></li>
                                                    <?php endif; ?>
                                                    <?php if ($order['status'] == 'ready'): ?>
                                                    <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'completed')">
                                                        <i class="fas fa-flag-checkered text-dark"></i> مكتمل
                                                    </a></li>
                                                    <?php endif; ?>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'cancelled')">
                                                        <i class="fas fa-times"></i> إلغاء
                                                    </a></li>
                                                </ul>
                                            </div>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-outline-success" 
                                                    onclick="printReceipt(<?php echo $order['id']; ?>)">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نافذة تفاصيل الطلب -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
        </div>
    </div>
</div>

<?php
$additional_js = '
<script>
$(document).ready(function() {
    $("#ordersTable").DataTable({
        order: [[6, "desc"]],
        pageLength: 25,
        responsive: true
    });
});

function viewOrder(orderId) {
    $.ajax({
        url: "ajax/get_order_details.php",
        type: "POST",
        data: { order_id: orderId },
        success: function(response) {
            $("#orderDetails").html(response);
            $("#orderModal").modal("show");
        }
    });
}

function updateOrderStatus(orderId, status) {
    let statusNames = {
        "confirmed": "تأكيد",
        "ready": "جاهز",
        "completed": "مكتمل",
        "cancelled": "إلغاء"
    };
    
    Swal.fire({
        title: "تحديث حالة الطلب",
        text: "هل تريد " + statusNames[status] + " هذا الطلب؟",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "نعم",
        cancelButtonText: "إلغاء"
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: "ajax/update_order_status.php",
                type: "POST",
                data: {
                    order_id: orderId,
                    status: status
                },
                dataType: "json",
                success: function(response) {
                    if (response.success) {
                        showSuccess("تم تحديث حالة الطلب بنجاح");
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showError("خطأ", response.message);
                    }
                }
            });
        }
    });
}

function printReceipt(orderId) {
    window.open("../print/receipt.php?id=" + orderId, "_blank", "width=300,height=600");
}
</script>
';

include '../layouts/footer.php';
?>
