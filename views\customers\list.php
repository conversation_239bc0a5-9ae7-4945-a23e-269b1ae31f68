<?php
/**
 * قائمة العملاء
 * Customers List
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Customer.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('customers.view');

$customer_model = new Customer();

// معالجة الحذف
if ($_POST['action'] ?? '' === 'delete' && hasPermission('customers.delete')) {
    $customer_id = $_POST['customer_id'] ?? null;
    if ($customer_id) {
        $result = $customer_model->delete($customer_id);
        if ($result['success']) {
            $_SESSION['success'] = $result['message'];
        } else {
            $_SESSION['error'] = $result['message'];
        }
        redirect('list.php');
    }
}

// الحصول على العملاء
$customers = $customer_model->getAll();

// إحصائيات العملاء
$stats = $customer_model->getStats();

$page_title = 'إدارة العملاء';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة العملاء</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <?php if (hasPermission('customers.create')): ?>
                        <a href="add.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> عميل جديد
                        </a>
                        <?php endif; ?>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="exportCustomers()">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                    </div>
                </div>
            </div>

            <?php showSessionMessages(); ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي العملاء
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['total_customers']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        العملاء النشطون
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['active_customers']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        العملاء المميزون
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['vip_customers']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-crown fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        متوسط الإنفاق
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo formatCurrency($stats['average_spent']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" id="searchInput" 
                                   placeholder="الاسم أو رقم الهاتف أو البريد الإلكتروني">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">نوع العميل</label>
                            <select class="form-select" id="customerTypeFilter">
                                <option value="">الكل</option>
                                <option value="vip">مميز</option>
                                <option value="regular">عادي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">ترتيب حسب</label>
                            <select class="form-select" id="sortBy">
                                <option value="total_spent">الإنفاق الإجمالي</option>
                                <option value="total_visits">عدد الزيارات</option>
                                <option value="last_visit">آخر زيارة</option>
                                <option value="created_at">تاريخ التسجيل</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="fas fa-times"></i> مسح الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول العملاء -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="customersTable">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>إجمالي الإنفاق</th>
                                    <th>عدد الزيارات</th>
                                    <th>نقاط الولاء</th>
                                    <th>آخر زيارة</th>
                                    <th>النوع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($customers as $customer): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-2">
                                                <?php echo mb_substr($customer['name'], 0, 1); ?>
                                            </div>
                                            <div>
                                                <strong><?php echo $customer['name']; ?></strong>
                                                <?php if ($customer['date_of_birth']): ?>
                                                <div>
                                                    <small class="text-muted">
                                                        <i class="fas fa-birthday-cake"></i>
                                                        <?php echo formatDate($customer['date_of_birth'], 'd/m'); ?>
                                                    </small>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="tel:<?php echo $customer['phone']; ?>" class="text-decoration-none">
                                            <?php echo $customer['phone']; ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if ($customer['email']): ?>
                                        <a href="mailto:<?php echo $customer['email']; ?>" class="text-decoration-none">
                                            <?php echo $customer['email']; ?>
                                        </a>
                                        <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong class="text-success"><?php echo formatCurrency($customer['total_spent']); ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $customer['total_visits']; ?></span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <strong><?php echo number_format($customer['loyalty_points']); ?></strong>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($customer['last_visit']): ?>
                                        <div><?php echo formatDate($customer['last_visit'], 'd/m/Y'); ?></div>
                                        <small class="text-muted"><?php echo formatDate($customer['last_visit'], 'H:i'); ?></small>
                                        <?php else: ?>
                                        <span class="text-muted">لم يزر بعد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($customer['is_vip']): ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-crown"></i> مميز
                                        </span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">عادي</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="viewCustomer(<?php echo $customer['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if (hasPermission('customers.edit')): ?>
                                            <a href="edit.php?id=<?php echo $customer['id']; ?>" 
                                               class="btn btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php endif; ?>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="orders.php?customer_id=<?php echo $customer['id']; ?>">
                                                        <i class="fas fa-shopping-cart"></i> الطلبات
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="loyalty.php?customer_id=<?php echo $customer['id']; ?>">
                                                        <i class="fas fa-star"></i> نقاط الولاء
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="transactions.php?customer_id=<?php echo $customer['id']; ?>">
                                                        <i class="fas fa-money-bill"></i> المعاملات
                                                    </a></li>
                                                    <?php if (hasPermission('customers.delete')): ?>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" 
                                                           onclick="deleteCustomer(<?php echo $customer['id']; ?>, '<?php echo addslashes($customer['name']); ?>')">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </a></li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نافذة تفاصيل العميل -->
<div class="modal fade" id="customerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="customerDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
        </div>
    </div>
</div>

<?php
$additional_css = '
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}
</style>
';

$additional_js = '
<script>
let table;

$(document).ready(function() {
    table = $("#customersTable").DataTable({
        order: [[3, "desc"]],
        pageLength: 25,
        responsive: true,
        columnDefs: [
            { orderable: false, targets: [8] }
        ]
    });
    
    // فلاتر البحث
    $("#searchInput").on("keyup", function() {
        table.search(this.value).draw();
    });
    
    $("#customerTypeFilter").on("change", function() {
        let type = this.value;
        if (type === "") {
            table.column(7).search("").draw();
        } else if (type === "vip") {
            table.column(7).search("مميز").draw();
        } else {
            table.column(7).search("عادي").draw();
        }
    });
    
    $("#sortBy").on("change", function() {
        let sortBy = this.value;
        let columnIndex;
        
        switch(sortBy) {
            case "total_spent":
                columnIndex = 3;
                break;
            case "total_visits":
                columnIndex = 4;
                break;
            case "last_visit":
                columnIndex = 6;
                break;
            default:
                columnIndex = 0;
        }
        
        table.order([columnIndex, "desc"]).draw();
    });
});

function clearFilters() {
    $("#searchInput").val("");
    $("#customerTypeFilter").val("");
    $("#sortBy").val("total_spent");
    table.search("").columns().search("").order([3, "desc"]).draw();
}

function viewCustomer(customerId) {
    $.ajax({
        url: "ajax/get_customer_details.php",
        type: "POST",
        data: { customer_id: customerId },
        success: function(response) {
            $("#customerDetails").html(response);
            $("#customerModal").modal("show");
        }
    });
}

function deleteCustomer(customerId, customerName) {
    Swal.fire({
        title: "حذف العميل",
        text: "هل أنت متأكد من حذف العميل: " + customerName + "؟",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "نعم، احذف",
        cancelButtonText: "إلغاء"
    }).then((result) => {
        if (result.isConfirmed) {
            let form = document.createElement("form");
            form.method = "POST";
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="customer_id" value="${customerId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function exportCustomers() {
    window.open("export.php?type=customers", "_blank");
}
</script>
';

include '../layouts/footer.php';
?>
