<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            right: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset 1px 0 0 rgba(0, 0, 0, .1);
            background-color: #fff;
        }
        
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.125rem 0.5rem;
            transition: all 0.15s ease-in-out;
        }
        
        .sidebar .nav-link:hover {
            color: #495057;
            background-color: #f8f9fa;
        }
        
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #0d6efd;
        }
        
        .sidebar .nav-link i {
            margin-left: 0.5rem;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            margin-right: 240px;
            padding-top: 48px;
        }
        
        .navbar {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: 600;
        }
        
        .btn {
            border-radius: 0.375rem;
            font-weight: 500;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa;
        }
        
        .badge {
            font-size: 0.75em;
            font-weight: 500;
        }
        
        .border-right-primary {
            border-right: 0.25rem solid #0d6efd !important;
        }
        
        .border-right-success {
            border-right: 0.25rem solid #198754 !important;
        }
        
        .border-right-info {
            border-right: 0.25rem solid #0dcaf0 !important;
        }
        
        .border-right-warning {
            border-right: 0.25rem solid #ffc107 !important;
        }
        
        .text-gray-800 {
            color: #5a5c69 !important;
        }
        
        .text-gray-300 {
            color: #dddfeb !important;
        }
        
        .pos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
        }
        
        .pos-item {
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            border: 2px solid #dee2e6;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }
        
        .pos-item:hover {
            border-color: #0d6efd;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .pos-item img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
        }
        
        .pos-item .name {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .pos-item .price {
            font-size: 0.75rem;
            color: #198754;
            font-weight: 600;
        }
        
        .kitchen-order {
            border: 2px solid #dee2e6;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            background: white;
            transition: all 0.2s ease;
        }
        
        .kitchen-order.pending {
            border-color: #ffc107;
            background-color: #fff3cd;
        }
        
        .kitchen-order.preparing {
            border-color: #0dcaf0;
            background-color: #cff4fc;
        }
        
        .kitchen-order.ready {
            border-color: #198754;
            background-color: #d1e7dd;
        }
        
        .order-timer {
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .table-status {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 3px solid;
        }
        
        .table-status.available {
            background-color: #d1e7dd;
            border-color: #198754;
            color: #198754;
        }
        
        .table-status.occupied {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #dc3545;
        }
        
        .table-status.reserved {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #ffc107;
        }
        
        .table-status:hover {
            transform: scale(1.05);
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
                padding: 0;
            }
            
            .main-content {
                margin-right: 0;
                padding-top: 0;
            }
            
            .pos-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }
        }
        
        /* تحسينات للطباعة */
        @media print {
            .sidebar,
            .navbar,
            .btn,
            .no-print {
                display: none !important;
            }
            
            .main-content {
                margin-right: 0;
                padding: 0;
            }
            
            body {
                background: white;
            }
            
            .card {
                box-shadow: none;
                border: 1px solid #000;
            }
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 576px) {
            .container-fluid {
                padding-right: 0.5rem;
                padding-left: 0.5rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .btn-group .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
            }
        }
    </style>
    
    <?php if (isset($additional_css)): ?>
        <?php echo $additional_css; ?>
    <?php endif; ?>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                <i class="fas fa-utensils me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <div class="d-flex align-items-center">
                <!-- معلومات الوردية -->
                <?php if (isLoggedIn()): ?>
                    <?php $shift = getActiveShift(); ?>
                    <?php if ($shift): ?>
                        <span class="badge bg-success me-3">
                            <i class="fas fa-clock me-1"></i>
                            وردية نشطة منذ <?php echo formatDate($shift['start_time'], 'H:i'); ?>
                        </span>
                    <?php endif; ?>
                    
                    <!-- قائمة المستخدم -->
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" 
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i>
                            <?php echo $_SESSION['full_name']; ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="views/profile/index.php">
                                    <i class="fas fa-user-cog me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="views/settings/index.php">
                                    <i class="fas fa-cog me-2"></i>
                                    الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <?php if ($shift): ?>
                                <li>
                                    <a class="dropdown-item" href="views/shifts/end.php">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        إنهاء الوردية
                                    </a>
                                </li>
                            <?php endif; ?>
                            <li>
                                <a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </nav>
