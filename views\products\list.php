<?php
/**
 * قائمة المنتجات
 * Products List
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Product.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('products.view');

$product_model = new Product();

// معالجة الحذف
if ($_POST['action'] ?? '' === 'delete' && hasPermission('products.delete')) {
    $product_id = $_POST['product_id'] ?? null;
    if ($product_id) {
        $result = $product_model->delete($product_id);
        if ($result['success']) {
            $_SESSION['success'] = $result['message'];
        } else {
            $_SESSION['error'] = $result['message'];
        }
        redirect('list.php');
    }
}

// الحصول على المنتجات
$products = $product_model->getAll();

// الحصول على الفئات للفلتر
$categories = DB::select("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order");

$page_title = 'إدارة المنتجات';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المنتجات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <?php if (hasPermission('products.create')): ?>
                        <a href="add.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> منتج جديد
                        </a>
                        <?php endif; ?>
                        <a href="../categories/list.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-tags"></i> الفئات
                        </a>
                    </div>
                </div>
            </div>

            <?php showSessionMessages(); ?>

            <!-- فلاتر -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="اسم المنتج...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الفئة</label>
                            <select class="form-select" id="categoryFilter">
                                <option value="">جميع الفئات</option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo $category['name_ar']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">الكل</option>
                                <option value="1">نشط</option>
                                <option value="0">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="fas fa-times"></i> مسح الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المنتجات -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="productsTable">
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>اسم المنتج</th>
                                    <th>الفئة</th>
                                    <th>السعر الأساسي</th>
                                    <th>تكلفة التحضير</th>
                                    <th>وقت التحضير</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                <tr>
                                    <td>
                                        <?php if ($product['image']): ?>
                                        <img src="../../uploads/<?php echo $product['image']; ?>" 
                                             alt="<?php echo $product['name_ar']; ?>" 
                                             class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px; border-radius: 0.375rem;">
                                            <i class="fas fa-utensils text-muted"></i>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo $product['name_ar']; ?></strong>
                                        </div>
                                        <small class="text-muted"><?php echo $product['name']; ?></small>
                                        <?php if ($product['description']): ?>
                                        <div>
                                            <small class="text-muted"><?php echo substr($product['description'], 0, 50); ?>...</small>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo $product['category_name']; ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo formatCurrency($product['base_price']); ?></strong>
                                    </td>
                                    <td>
                                        <?php if ($product['cost_price'] > 0): ?>
                                        <span class="text-muted"><?php echo formatCurrency($product['cost_price']); ?></span>
                                        <div>
                                            <small class="text-success">
                                                ربح: <?php echo formatCurrency($product['base_price'] - $product['cost_price']); ?>
                                            </small>
                                        </div>
                                        <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($product['preparation_time'] > 0): ?>
                                        <span class="badge bg-info"><?php echo $product['preparation_time']; ?> دقيقة</span>
                                        <?php else: ?>
                                        <span class="text-muted">فوري</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($product['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="viewProduct(<?php echo $product['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if (hasPermission('products.edit')): ?>
                                            <a href="edit.php?id=<?php echo $product['id']; ?>" 
                                               class="btn btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php endif; ?>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="sizes.php?id=<?php echo $product['id']; ?>">
                                                        <i class="fas fa-expand-arrows-alt"></i> الأحجام
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="addons.php?id=<?php echo $product['id']; ?>">
                                                        <i class="fas fa-plus-circle"></i> الإضافات
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="recipe.php?id=<?php echo $product['id']; ?>">
                                                        <i class="fas fa-list"></i> الوصفة
                                                    </a></li>
                                                    <?php if (hasPermission('products.delete')): ?>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" 
                                                           onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo addslashes($product['name_ar']); ?>')">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </a></li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نافذة تفاصيل المنتج -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="productDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
        </div>
    </div>
</div>

<?php
$additional_js = '
<script>
let table;

$(document).ready(function() {
    table = $("#productsTable").DataTable({
        order: [[1, "asc"]],
        pageLength: 25,
        responsive: true,
        columnDefs: [
            { orderable: false, targets: [0, 7] }
        ]
    });
    
    // فلاتر البحث
    $("#searchInput").on("keyup", function() {
        table.search(this.value).draw();
    });
    
    $("#categoryFilter").on("change", function() {
        table.column(2).search(this.value).draw();
    });
    
    $("#statusFilter").on("change", function() {
        let status = this.value;
        if (status === "") {
            table.column(6).search("").draw();
        } else {
            let statusText = status === "1" ? "نشط" : "غير نشط";
            table.column(6).search(statusText).draw();
        }
    });
});

function clearFilters() {
    $("#searchInput").val("");
    $("#categoryFilter").val("");
    $("#statusFilter").val("");
    table.search("").columns().search("").draw();
}

function viewProduct(productId) {
    $.ajax({
        url: "ajax/get_product_details.php",
        type: "POST",
        data: { product_id: productId },
        success: function(response) {
            $("#productDetails").html(response);
            $("#productModal").modal("show");
        }
    });
}

function deleteProduct(productId, productName) {
    Swal.fire({
        title: "حذف المنتج",
        text: "هل أنت متأكد من حذف المنتج: " + productName + "؟",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "نعم، احذف",
        cancelButtonText: "إلغاء"
    }).then((result) => {
        if (result.isConfirmed) {
            let form = document.createElement("form");
            form.method = "POST";
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="product_id" value="${productId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
';

include '../layouts/footer.php';
?>
