<?php
/**
 * نموذج المخزون
 * Inventory Model
 * Restaurant Management System
 */

class Inventory {
    
    // الحصول على جميع المكونات
    public function getAllIngredients($active_only = true) {
        $where = $active_only ? "WHERE is_active = 1" : "";
        
        $sql = "SELECT * FROM ingredients 
                {$where}
                ORDER BY name_ar";
        
        return DB::select($sql);
    }
    
    // الحصول على مكون بالمعرف
    public function getIngredientById($id) {
        $sql = "SELECT * FROM ingredients WHERE id = ?";
        return DB::selectOne($sql, array($id));
    }
    
    // إنشاء مكون جديد
    public function createIngredient($data) {
        $sql = "INSERT INTO ingredients (name, name_ar, unit, unit_ar, minimum_stock) 
                VALUES (?, ?, ?, ?, ?)";
        
        $params = array(
            $data['name'],
            $data['name_ar'],
            $data['unit'],
            $data['unit_ar'],
            $data['minimum_stock'] ?? 0
        );
        
        $ingredient_id = DB::insert($sql, $params);
        
        if ($ingredient_id) {
            logActivity('create_ingredient', 'ingredients', $ingredient_id, null, $data);
            return array('success' => true, 'id' => $ingredient_id, 'message' => 'تم إنشاء المكون بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في إنشاء المكون');
    }
    
    // تحديث مكون
    public function updateIngredient($id, $data) {
        $old_data = $this->getIngredientById($id);
        if (!$old_data) {
            return array('success' => false, 'message' => 'المكون غير موجود');
        }
        
        $fields = array();
        $params = array();
        
        foreach ($data as $key => $value) {
            $fields[] = "{$key} = ?";
            $params[] = $value;
        }
        
        $params[] = $id;
        
        $sql = "UPDATE ingredients SET " . implode(', ', $fields) . " WHERE id = ?";
        
        if (DB::execute($sql, $params)) {
            logActivity('update_ingredient', 'ingredients', $id, $old_data, $data);
            return array('success' => true, 'message' => 'تم تحديث المكون بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في تحديث المكون');
    }
    
    // حذف مكون
    public function deleteIngredient($id) {
        $ingredient = $this->getIngredientById($id);
        if (!$ingredient) {
            return array('success' => false, 'message' => 'المكون غير موجود');
        }
        
        $sql = "UPDATE ingredients SET is_active = 0 WHERE id = ?";
        
        if (DB::execute($sql, array($id))) {
            logActivity('delete_ingredient', 'ingredients', $id, $ingredient);
            return array('success' => true, 'message' => 'تم حذف المكون بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في حذف المكون');
    }
    
    // إضافة مخزون (شراء)
    public function addStock($ingredient_id, $quantity, $unit_cost, $expiry_date = null, $batch_number = null, $purchase_item_id = null) {
        DB::beginTransaction();
        
        try {
            // إنشاء دفعة جديدة
            $sql = "INSERT INTO stock_batches 
                    (ingredient_id, purchase_item_id, quantity_received, quantity_remaining, 
                     unit_cost, expiry_date, batch_number, received_date) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURDATE())";
            
            $batch_id = DB::insert($sql, array(
                $ingredient_id, $purchase_item_id, $quantity, $quantity, 
                $unit_cost, $expiry_date, $batch_number
            ));
            
            if (!$batch_id) {
                throw new Exception('فشل في إنشاء الدفعة');
            }
            
            // تحديث المخزون الحالي
            $this->updateCurrentStock($ingredient_id);
            
            // تسجيل حركة المخزون
            $this->recordStockMovement($ingredient_id, 'in', $quantity, $unit_cost, 
                                     'purchase', $purchase_item_id, $batch_id, 'إضافة مخزون');
            
            DB::commit();
            return array('success' => true, 'batch_id' => $batch_id, 'message' => 'تم إضافة المخزون بنجاح');
            
        } catch (Exception $e) {
            DB::rollback();
            return array('success' => false, 'message' => $e->getMessage());
        }
    }
    
    // استهلاك مخزون (FEFO - First Expired First Out)
    public function consumeStock($ingredient_id, $quantity, $reason = 'استهلاك', $reference_type = 'order', $reference_id = null) {
        DB::beginTransaction();
        
        try {
            $remaining_quantity = $quantity;
            
            // الحصول على الدفعات مرتبة حسب تاريخ الانتهاء (FEFO)
            $sql = "SELECT * FROM stock_batches 
                    WHERE ingredient_id = ? AND quantity_remaining > 0 
                    ORDER BY expiry_date ASC, received_date ASC";
            
            $batches = DB::select($sql, array($ingredient_id));
            
            foreach ($batches as $batch) {
                if ($remaining_quantity <= 0) break;
                
                $consume_from_batch = min($remaining_quantity, $batch['quantity_remaining']);
                
                // تحديث الدفعة
                $sql = "UPDATE stock_batches 
                        SET quantity_remaining = quantity_remaining - ? 
                        WHERE id = ?";
                
                DB::execute($sql, array($consume_from_batch, $batch['id']));
                
                // تسجيل حركة المخزون
                $this->recordStockMovement($ingredient_id, 'out', $consume_from_batch, 
                                         $batch['unit_cost'], $reference_type, $reference_id, 
                                         $batch['id'], $reason);
                
                $remaining_quantity -= $consume_from_batch;
            }
            
            if ($remaining_quantity > 0) {
                throw new Exception('المخزون غير كافي. نقص: ' . $remaining_quantity);
            }
            
            // تحديث المخزون الحالي
            $this->updateCurrentStock($ingredient_id);
            
            DB::commit();
            return array('success' => true, 'message' => 'تم استهلاك المخزون بنجاح');
            
        } catch (Exception $e) {
            DB::rollback();
            return array('success' => false, 'message' => $e->getMessage());
        }
    }
    
    // تحديث المخزون الحالي
    private function updateCurrentStock($ingredient_id) {
        $sql = "UPDATE ingredients 
                SET current_stock = (
                    SELECT COALESCE(SUM(quantity_remaining), 0) 
                    FROM stock_batches 
                    WHERE ingredient_id = ?
                ),
                average_cost = (
                    SELECT COALESCE(
                        SUM(quantity_remaining * unit_cost) / NULLIF(SUM(quantity_remaining), 0), 
                        0
                    ) 
                    FROM stock_batches 
                    WHERE ingredient_id = ? AND quantity_remaining > 0
                )
                WHERE id = ?";
        
        return DB::execute($sql, array($ingredient_id, $ingredient_id, $ingredient_id));
    }
    
    // تسجيل حركة المخزون
    private function recordStockMovement($ingredient_id, $movement_type, $quantity, $unit_cost, 
                                       $reference_type, $reference_id, $batch_id, $reason) {
        // الحصول على الرصيد الحالي
        $ingredient = $this->getIngredientById($ingredient_id);
        $balance_after = $ingredient['current_stock'];
        
        if ($movement_type == 'out') {
            $balance_after -= $quantity;
        } else {
            $balance_after += $quantity;
        }
        
        $total_cost = $quantity * $unit_cost;
        
        $sql = "INSERT INTO stock_movements 
                (ingredient_id, movement_type, quantity, unit_cost, total_cost, 
                 reference_type, reference_id, batch_id, reason, balance_after, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        return DB::insert($sql, array(
            $ingredient_id, $movement_type, $quantity, $unit_cost, $total_cost,
            $reference_type, $reference_id, $batch_id, $reason, $balance_after,
            $_SESSION['user_id']
        ));
    }
    
    // الحصول على حركات المخزون
    public function getStockMovements($ingredient_id = null, $limit = 100) {
        $where = $ingredient_id ? "WHERE sm.ingredient_id = ?" : "";
        $params = $ingredient_id ? array($ingredient_id, $limit) : array($limit);
        
        $sql = "SELECT sm.*, i.name_ar as ingredient_name, u.full_name as created_by_name
                FROM stock_movements sm 
                JOIN ingredients i ON sm.ingredient_id = i.id 
                LEFT JOIN users u ON sm.created_by = u.id 
                {$where}
                ORDER BY sm.created_at DESC 
                LIMIT ?";
        
        return DB::select($sql, $params);
    }
    
    // الحصول على المكونات منخفضة المخزون
    public function getLowStockIngredients() {
        $sql = "SELECT * FROM ingredients 
                WHERE current_stock <= minimum_stock AND is_active = 1 
                ORDER BY (current_stock / NULLIF(minimum_stock, 0)) ASC";
        
        return DB::select($sql);
    }
    
    // الحصول على المكونات منتهية الصلاحية أو قريبة الانتهاء
    public function getExpiringIngredients($days = 7) {
        $sql = "SELECT i.name_ar as ingredient_name, sb.*, 
                       DATEDIFF(sb.expiry_date, CURDATE()) as days_to_expiry
                FROM stock_batches sb 
                JOIN ingredients i ON sb.ingredient_id = i.id 
                WHERE sb.quantity_remaining > 0 
                AND sb.expiry_date IS NOT NULL 
                AND sb.expiry_date <= DATE_ADD(CURDATE(), INTERVAL ? DAY)
                ORDER BY sb.expiry_date ASC";
        
        return DB::select($sql, array($days));
    }
    
    // تحديث حالة الدفعات المنتهية الصلاحية
    public function updateExpiredBatches() {
        $sql = "UPDATE stock_batches 
                SET is_expired = 1 
                WHERE expiry_date < CURDATE() AND is_expired = 0";
        
        return DB::execute($sql);
    }
    
    // جرد المخزون
    public function stockTake($adjustments) {
        DB::beginTransaction();
        
        try {
            foreach ($adjustments as $ingredient_id => $actual_quantity) {
                $ingredient = $this->getIngredientById($ingredient_id);
                $current_stock = $ingredient['current_stock'];
                $difference = $actual_quantity - $current_stock;
                
                if ($difference != 0) {
                    $movement_type = $difference > 0 ? 'in' : 'out';
                    $quantity = abs($difference);
                    
                    // تسجيل حركة التسوية
                    $this->recordStockMovement($ingredient_id, $movement_type, $quantity, 
                                             $ingredient['average_cost'], 'adjustment', null, 
                                             null, 'تسوية جرد');
                    
                    // تحديث المخزون الحالي
                    $sql = "UPDATE ingredients SET current_stock = ? WHERE id = ?";
                    DB::execute($sql, array($actual_quantity, $ingredient_id));
                }
            }
            
            DB::commit();
            logActivity('stock_take', 'inventory', null, null, $adjustments);
            return array('success' => true, 'message' => 'تم تحديث الجرد بنجاح');
            
        } catch (Exception $e) {
            DB::rollback();
            return array('success' => false, 'message' => 'فشل في تحديث الجرد');
        }
    }
    
    // تقرير حالة المخزون
    public function getStockReport() {
        $sql = "SELECT i.*, 
                       CASE 
                           WHEN i.current_stock <= i.minimum_stock THEN 'منخفض'
                           WHEN i.current_stock <= i.minimum_stock * 2 THEN 'متوسط'
                           ELSE 'جيد'
                       END as stock_status,
                       (i.current_stock * i.average_cost) as stock_value
                FROM ingredients i 
                WHERE i.is_active = 1 
                ORDER BY i.name_ar";
        
        return DB::select($sql);
    }
    
    // إحصائيات المخزون
    public function getStats() {
        $sql = "SELECT 
                    COUNT(*) as total_ingredients,
                    SUM(CASE WHEN current_stock <= minimum_stock THEN 1 ELSE 0 END) as low_stock_count,
                    SUM(current_stock * average_cost) as total_stock_value,
                    AVG(current_stock) as average_stock_level
                FROM ingredients 
                WHERE is_active = 1";
        
        return DB::selectOne($sql);
    }
    
    // البحث في المكونات
    public function searchIngredients($term) {
        $sql = "SELECT * FROM ingredients 
                WHERE (name LIKE ? OR name_ar LIKE ?) AND is_active = 1 
                ORDER BY name_ar";
        
        $search_term = "%{$term}%";
        return DB::select($sql, array($search_term, $search_term));
    }
}
?>
