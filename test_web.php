<?php
/**
 * اختبار النظام عبر المتصفح
 * Web-based System Test
 * Restaurant Management System
 */

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "<title>اختبار النظام</title>\n";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n";
echo ".test-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n";
echo ".test-item { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }\n";
echo ".success { border-color: #28a745; background: #d4edda; color: #155724; }\n";
echo ".error { border-color: #dc3545; background: #f8d7da; color: #721c24; }\n";
echo ".warning { border-color: #ffc107; background: #fff3cd; color: #856404; }\n";
echo ".info { border-color: #17a2b8; background: #d1ecf1; color: #0c5460; }\n";
echo "h1, h2 { color: #333; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<div class='test-container'>\n";
echo "<h1>🧪 اختبار شامل لنظام إدارة المطعم</h1>\n";

$tests_passed = 0;
$tests_failed = 0;

function test_result($test_name, $result, $message = '') {
    global $tests_passed, $tests_failed;
    
    if ($result) {
        $tests_passed++;
        echo "<div class='test-item success'>✅ $test_name: نجح</div>\n";
    } else {
        $tests_failed++;
        echo "<div class='test-item error'>❌ $test_name: فشل" . ($message ? " - $message" : "") . "</div>\n";
    }
}

function test_warning($test_name, $message) {
    echo "<div class='test-item warning'>⚠️ $test_name: $message</div>\n";
}

function test_info($test_name, $message) {
    echo "<div class='test-item info'>ℹ️ $test_name: $message</div>\n";
}

// اختبار إصدار PHP
echo "<h2>🔧 اختبارات البيئة</h2>\n";
test_result("إصدار PHP", version_compare(PHP_VERSION, '5.6.0', '>='), "الإصدار الحالي: " . PHP_VERSION);

// اختبار الامتدادات المطلوبة
$required_extensions = array('pdo', 'pdo_mysql', 'json', 'mbstring', 'gd');
foreach ($required_extensions as $ext) {
    test_result("امتداد $ext", extension_loaded($ext));
}

// اختبار الملفات الأساسية
echo "<h2>📁 اختبارات الملفات</h2>\n";
$required_files = array(
    'config/database.php',
    'config/init.php',
    'config/constants.php',
    'includes/functions.php',
    'error.php',
    'index.php',
    'login.php'
);

foreach ($required_files as $file) {
    test_result("ملف $file", file_exists($file));
}

// اختبار المجلدات المطلوبة
$required_dirs = array('logs', 'uploads', 'temp', 'backups');
foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        test_warning("مجلد $dir", "غير موجود - سيتم إنشاؤه");
    } else {
        test_result("مجلد $dir", is_writable($dir), is_writable($dir) ? "قابل للكتابة" : "غير قابل للكتابة");
    }
}

// اختبار قاعدة البيانات
echo "<h2>🗄️ اختبارات قاعدة البيانات</h2>\n";
try {
    require_once 'config/database.php';
    
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";charset=utf8",
        DB_USER,
        DB_PASS
    );
    test_result("الاتصال بخادم MySQL", true);
    
    // اختبار قاعدة البيانات
    $pdo->exec("USE " . DB_NAME);
    test_result("قاعدة البيانات " . DB_NAME, true);
    
    // اختبار الجداول الأساسية
    $required_tables = array('users', 'settings', 'customers', 'products', 'orders', 'tables', 'roles');
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        test_result("جدول $table", $stmt->rowCount() > 0);
    }
    
} catch (Exception $e) {
    test_result("قاعدة البيانات", false, $e->getMessage());
}

// اختبار تحميل الملفات الأساسية
echo "<h2>🔄 اختبارات التحميل</h2>\n";

// اختبار config/init.php
try {
    ob_start();
    include_once 'config/init.php';
    ob_end_clean();
    test_result("تحميل config/init.php", true);
    
    // اختبار فئة DB
    test_result("فئة DB", class_exists('DB'));
    
    // اختبار الدوال
    $functions_to_test = array('getSetting', 'setSetting', 'logError', 'sanitize', 'formatCurrency');
    foreach ($functions_to_test as $func) {
        test_result("دالة $func", function_exists($func));
    }
    
} catch (Exception $e) {
    test_result("تحميل config/init.php", false, $e->getMessage());
}

// اختبار الصفحات الرئيسية
echo "<h2>🌐 اختبارات الصفحات</h2>\n";

$pages_to_test = array(
    'index.php' => 'الصفحة الرئيسية',
    'login.php' => 'صفحة تسجيل الدخول',
    'views/dashboard/index.php' => 'لوحة التحكم',
    'views/tables/index.php' => 'صفحة الطاولات الرئيسية',
    'views/tables/list.php' => 'قائمة الطاولات',
    'views/products/add.php' => 'إضافة منتج',
    'views/customers/list.php' => 'قائمة العملاء',
    'views/users/list.php' => 'قائمة المستخدمين',
    'views/users/add.php' => 'إضافة مستخدم',
    'views/roles/list.php' => 'إدارة الأدوار'
);

foreach ($pages_to_test as $page => $name) {
    test_result($name, file_exists($page));
}

// اختبار ملفات AJAX
$ajax_files = array(
    'views/tables/ajax/get_table_details.php',
    'views/tables/ajax/get_tables_status.php',
    'views/customers/ajax/get_customer_details.php',
    'views/users/ajax/get_user_details.php',
    'views/products/ajax/get_product_details.php',
    'views/categories/ajax/get_category.php'
);

foreach ($ajax_files as $file) {
    test_result("ملف AJAX: " . basename($file), file_exists($file));
}

// اختبار التوافق مع PHP 5.6
echo "<h2>🔄 اختبارات التوافق مع PHP 5.6</h2>\n";

// فحص الملفات للتأكد من عدم وجود syntax غير متوافق
$files_to_check = array(
    'config/init.php',
    'config/constants.php',
    'config/database.php',
    'error.php'
);

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // فحص Null Coalescing Operator
        $has_null_coalescing = preg_match('/\?\?/', $content);
        test_result("$file - خالي من ??", !$has_null_coalescing);
        
        // فحص Array Syntax الجديد
        $has_short_array = preg_match('/=\s*\[/', $content);
        test_result("$file - خالي من [] syntax", !$has_short_array);
    }
}

// إحصائيات النتائج
echo "<h2>📊 ملخص النتائج</h2>\n";
$total_tests = $tests_passed + $tests_failed;
$success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;

echo "<div class='test-item info'>\n";
echo "إجمالي الاختبارات: $total_tests<br>\n";
echo "نجح: $tests_passed<br>\n";
echo "فشل: $tests_failed<br>\n";
echo "معدل النجاح: $success_rate%\n";
echo "</div>\n";

if ($tests_failed === 0) {
    echo "<div class='test-item success'>\n";
    echo "<h3>🎉 تهانينا! جميع الاختبارات نجحت</h3>\n";
    echo "النظام جاهز للاستخدام. يمكنك الآن:<br>\n";
    echo "• الوصول للنظام عبر: <a href='index.php' class='btn btn-success btn-sm'>index.php</a><br>\n";
    echo "• تسجيل الدخول بـ: admin / admin123<br>\n";
    echo "• استكشاف جميع الميزات\n";
    echo "</div>\n";
} else {
    echo "<div class='test-item error'>\n";
    echo "<h3>⚠️ يوجد مشاكل تحتاج إصلاح</h3>\n";
    echo "يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل استخدام النظام.<br>\n";
    echo "<a href='quick_fix.php' class='btn btn-warning btn-sm'>إصلاح سريع</a>\n";
    echo "</div>\n";
}

// نصائح إضافية
echo "<h2>💡 نصائح مهمة</h2>\n";
echo "<div class='test-item info'>\n";
echo "• تأكد من تشغيل Apache و MySQL<br>\n";
echo "• راجع ملف logs/error.log للأخطاء<br>\n";
echo "• تأكد من صلاحيات الكتابة على المجلدات<br>\n";
echo "• في حالة مشاكل قاعدة البيانات، استورد ملف database.sql<br>\n";
echo "• للدعم الفني، راجع ملف README.md\n";
echo "</div>\n";

// روابط مفيدة
echo "<h2>🔗 روابط مفيدة</h2>\n";
echo "<div class='test-item info'>\n";
echo "<div class='row'>\n";
echo "<div class='col-md-6'>\n";
echo "<h5>صفحات النظام:</h5>\n";
echo "<ul>\n";
echo "<li><a href='start_system.php'>تشغيل النظام</a></li>\n";
echo "<li><a href='login.php'>تسجيل الدخول</a></li>\n";
echo "<li><a href='views/dashboard/index.php'>لوحة التحكم</a></li>\n";
echo "<li><a href='views/tables/index.php'>خريطة الطاولات</a></li>\n";
echo "</ul>\n";
echo "</div>\n";
echo "<div class='col-md-6'>\n";
echo "<h5>أدوات الإصلاح:</h5>\n";
echo "<ul>\n";
echo "<li><a href='quick_fix.php'>إصلاح سريع</a></li>\n";
echo "<li><a href='test_web.php'>اختبار شامل</a></li>\n";
echo "<li><a href='system_info.php'>معلومات النظام</a></li>\n";
echo "</ul>\n";
echo "</div>\n";
echo "</div>\n";
echo "</div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
