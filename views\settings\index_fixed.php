<?php
/**
 * إعدادات النظام
 * System Settings
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('settings.view');

$error = '';
$success = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST' && hasPermission('settings.edit')) {
    $settings = isset($_POST['settings']) ? $_POST['settings'] : array();
    
    try {
        DB::beginTransaction();
        
        foreach ($settings as $key => $value) {
            $sql = "INSERT INTO settings (setting_key, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
            DB::execute($sql, array($key, $value));
        }
        
        DB::commit();
        $success = 'تم حفظ الإعدادات بنجاح';
        
        // تسجيل النشاط
        logActivity('update_settings', 'settings', null, null, $settings);
        
    } catch (Exception $e) {
        DB::rollback();
        $error = 'فشل في حفظ الإعدادات: ' . $e->getMessage();
    }
}

// الحصول على الإعدادات الحالية
$current_settings = array();
$settings_result = DB::select("SELECT setting_key, setting_value FROM settings");
foreach ($settings_result as $setting) {
    $current_settings[$setting['setting_key']] = $setting['setting_value'];
}

// دالة للحصول على قيمة الإعداد
function getSettingLocal($key, $default = '') {
    global $current_settings;
    return isset($current_settings[$key]) ? $current_settings[$key] : $default;
}

$page_title = 'إعدادات النظام';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إعدادات النظام</h1>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="row">
                    <!-- إعدادات المطعم -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-store me-2"></i>
                                    معلومات المطعم
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">اسم المطعم</label>
                                    <input type="text" class="form-control" name="settings[restaurant_name]" 
                                           value="<?php echo htmlspecialchars(getSettingLocal('restaurant_name', 'مطعم الذواقة')); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">اسم المطعم بالإنجليزية</label>
                                    <input type="text" class="form-control" name="settings[restaurant_name_en]" 
                                           value="<?php echo htmlspecialchars(getSettingLocal('restaurant_name_en', 'Gourmet Restaurant')); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="settings[restaurant_address]" rows="3"><?php echo htmlspecialchars(getSettingLocal('restaurant_address')); ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <input type="text" class="form-control" name="settings[restaurant_phone]" 
                                                   value="<?php echo htmlspecialchars(getSettingLocal('restaurant_phone')); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" name="settings[restaurant_email]" 
                                                   value="<?php echo htmlspecialchars(getSettingLocal('restaurant_email')); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" name="settings[tax_number]" 
                                                   value="<?php echo htmlspecialchars(getSettingLocal('tax_number')); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم السجل التجاري</label>
                                            <input type="text" class="form-control" name="settings[commercial_register]" 
                                                   value="<?php echo htmlspecialchars(getSettingLocal('commercial_register')); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإعدادات المالية -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-calculator me-2"></i>
                                    الإعدادات المالية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نسبة الضريبة (%)</label>
                                            <input type="number" class="form-control" name="settings[tax_rate]" 
                                                   step="0.01" min="0" max="100"
                                                   value="<?php echo getSettingLocal('tax_rate', '15'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رسوم الخدمة (%)</label>
                                            <input type="number" class="form-control" name="settings[service_charge_rate]" 
                                                   step="0.01" min="0" max="100"
                                                   value="<?php echo getSettingLocal('service_charge_rate', '10'); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">العملة</label>
                                    <select class="form-select" name="settings[currency]">
                                        <option value="SAR" <?php echo getSettingLocal('currency', 'SAR') == 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                                        <option value="AED" <?php echo getSettingLocal('currency') == 'AED' ? 'selected' : ''; ?>>درهم إماراتي (AED)</option>
                                        <option value="USD" <?php echo getSettingLocal('currency') == 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                        <option value="EUR" <?php echo getSettingLocal('currency') == 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">رمز العملة</label>
                                    <input type="text" class="form-control" name="settings[currency_symbol]" 
                                           value="<?php echo htmlspecialchars(getSettingLocal('currency_symbol', 'ر.س')); ?>">
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[auto_apply_tax]" 
                                           value="1" <?php echo getSettingLocal('auto_apply_tax', '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        تطبيق الضريبة تلقائياً
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[auto_apply_service_charge]" 
                                           value="1" <?php echo getSettingLocal('auto_apply_service_charge', '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        تطبيق رسوم الخدمة تلقائياً
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (hasPermission('settings.edit')): ?>
                <div class="text-center mb-4">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>
                        حفظ الإعدادات
                    </button>
                </div>
                <?php endif; ?>
            </form>
        </main>
    </div>
</div>

<?php
$additional_js = '
<script>
$(document).ready(function() {
    // تأكيد حفظ الإعدادات
    $("form").on("submit", function(e) {
        e.preventDefault();
        
        Swal.fire({
            title: "حفظ الإعدادات",
            text: "هل أنت متأكد من حفظ هذه الإعدادات؟",
            icon: "question",
            showCancelButton: true,
            confirmButtonText: "نعم، احفظ",
            cancelButtonText: "إلغاء"
        }).then((result) => {
            if (result.isConfirmed) {
                this.submit();
            }
        });
    });
});
</script>
';

include '../layouts/footer.php';
?>
