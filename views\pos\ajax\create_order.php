<?php
/**
 * إنشاء طلب جديد
 * Create New Order
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';
require_once '../../../models/Order.php';
require_once '../../../models/Customer.php';
require_once '../../../models/Inventory.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('orders.create');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// قراءة البيانات من JSON
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

$order_type = $data['order_type'] ?? '';
$customer_id = $data['customer_id'] ?? null;
$customer_name = $data['customer_name'] ?? null;
$customer_phone = $data['customer_phone'] ?? null;
$table_session_id = $data['table_session_id'] ?? null;
$items = $data['items'] ?? [];

if (empty($items)) {
    echo json_encode(['success' => false, 'message' => 'لا توجد عناصر في الطلب']);
    exit;
}

if (!in_array($order_type, ['dine_in', 'takeaway', 'delivery'])) {
    echo json_encode(['success' => false, 'message' => 'نوع الطلب غير صحيح']);
    exit;
}

if ($order_type === 'dine_in' && !$table_session_id) {
    echo json_encode(['success' => false, 'message' => 'جلسة الطاولة مطلوبة للطلبات الداخلية']);
    exit;
}

try {
    $order_model = new Order();
    $customer_model = new Customer();
    $inventory_model = new Inventory();
    
    DB::beginTransaction();
    
    // إنشاء الطلب
    $order_data = [
        'order_type' => $order_type,
        'table_session_id' => $table_session_id,
        'customer_id' => $customer_id,
        'customer_name' => $customer_name,
        'customer_phone' => $customer_phone
    ];
    
    $order_result = $order_model->create($order_data);
    
    if (!$order_result['success']) {
        throw new Exception($order_result['message']);
    }
    
    $order_id = $order_result['id'];
    $order_number = $order_result['order_number'];
    
    // إضافة عناصر الطلب
    foreach ($items as $item) {
        $product_id = $item['product_id'];
        $quantity = $item['quantity'];
        $unit_price = $item['unit_price'];
        $total_price = $item['total_price'];
        
        // إضافة العنصر
        $item_result = $order_model->addItem($order_id, $product_id, $quantity);
        
        if (!$item_result['success']) {
            throw new Exception('فشل في إضافة العنصر: ' . $item_result['message']);
        }
        
        $item_id = $item_result['id'];
        
        // إضافة الإضافات إذا وجدت
        if (isset($item['addons']) && !empty($item['addons'])) {
            foreach ($item['addons'] as $addon) {
                $addon_result = $order_model->addItemAddon($item_id, $addon['addon_id'], $addon['quantity']);
                
                if (!$addon_result['success']) {
                    throw new Exception('فشل في إضافة الإضافة: ' . $addon_result['message']);
                }
            }
        }
        
        // استهلاك المخزون للمنتجات المبنية على وصفات
        $product = DB::selectOne("SELECT * FROM products WHERE id = ?", [$product_id]);
        
        if ($product['is_recipe_based']) {
            $recipe = DB::select("SELECT * FROM product_recipes WHERE product_id = ?", [$product_id]);
            
            foreach ($recipe as $ingredient) {
                $consume_quantity = $ingredient['quantity'] * $quantity;
                
                $consume_result = $inventory_model->consumeStock(
                    $ingredient['ingredient_id'],
                    $consume_quantity,
                    'استهلاك - طلب رقم ' . $order_number,
                    'order',
                    $order_id
                );
                
                if (!$consume_result['success']) {
                    throw new Exception('نفد المخزون: ' . $consume_result['message']);
                }
            }
        }
    }
    
    // تحديث إجماليات الطلب
    $order_model->updateTotals($order_id);
    
    // إضافة نقاط الولاء للعميل
    if ($customer_id) {
        $order = $order_model->getById($order_id);
        $loyalty_points = calculateLoyaltyPoints($order['total_amount']);
        
        if ($loyalty_points > 0) {
            $customer_model->addLoyaltyPoints(
                $customer_id,
                $loyalty_points,
                'نقاط من طلب رقم ' . $order_number,
                'order',
                $order_id
            );
            
            // تحديث نقاط الولاء في الطلب
            DB::execute("UPDATE orders SET loyalty_points_earned = ? WHERE id = ?", 
                       [$loyalty_points, $order_id]);
        }
        
        // تحديث إحصائيات العميل
        $customer_model->updateStats($customer_id);
    }
    
    DB::commit();
    
    logActivity('create_order', 'orders', $order_id, null, $order_data);
    
    echo json_encode([
        'success' => true,
        'order_id' => $order_id,
        'order_number' => $order_number,
        'message' => 'تم إنشاء الطلب بنجاح'
    ]);
    
} catch (Exception $e) {
    DB::rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
