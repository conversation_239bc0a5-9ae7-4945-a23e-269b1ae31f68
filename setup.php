<?php
/**
 * معالج الإعداد السريع لنظام إدارة المطعم
 * Quick Setup Handler for Restaurant Management System
 */

// التحقق من وجود ملف الإعداد
if (file_exists('config/database.php')) {
    $config_exists = true;
    require_once 'config/database.php';
} else {
    $config_exists = false;
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// معالجة خطوات الإعداد
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    switch ($step) {
        case 1:
            // إعداد قاعدة البيانات
            $db_host = $_POST['db_host'] ?? 'localhost';
            $db_name = $_POST['db_name'] ?? 'restaurant_system';
            $db_user = $_POST['db_user'] ?? 'root';
            $db_pass = $_POST['db_pass'] ?? '';
            
            // اختبار الاتصال
            try {
                $dsn = "mysql:host={$db_host};charset=utf8";
                $pdo = new PDO($dsn, $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8 COLLATE utf8_general_ci");
                
                // حفظ إعدادات قاعدة البيانات
                $config_content = "<?php\n";
                $config_content .= "// إعدادات قاعدة البيانات\n";
                $config_content .= "define('DB_HOST', '{$db_host}');\n";
                $config_content .= "define('DB_NAME', '{$db_name}');\n";
                $config_content .= "define('DB_USER', '{$db_user}');\n";
                $config_content .= "define('DB_PASS', '{$db_pass}');\n";
                $config_content .= "define('DB_CHARSET', 'utf8');\n\n";
                $config_content .= "// إعدادات النظام\n";
                $config_content .= "define('SITE_URL', 'http://' . \$_SERVER['HTTP_HOST'] . dirname(\$_SERVER['SCRIPT_NAME']));\n";
                $config_content .= "define('SITE_NAME', 'نظام إدارة المطعم');\n";
                $config_content .= "define('SITE_NAME_EN', 'Restaurant Management System');\n";
                $config_content .= "?>";
                
                if (!is_dir('config')) {
                    mkdir('config', 0755, true);
                }
                
                file_put_contents('config/database.php', $config_content);
                
                header('Location: setup.php?step=2');
                exit;
                
            } catch (PDOException $e) {
                $error = 'فشل في الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 2:
            // استيراد قاعدة البيانات
            if (file_exists('db/database.sql')) {
                try {
                    require_once 'config/database.php';
                    
                    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
                    $pdo = new PDO($dsn, DB_USER, DB_PASS);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    $sql = file_get_contents('db/database.sql');
                    $pdo->exec($sql);
                    
                    header('Location: setup.php?step=3');
                    exit;
                    
                } catch (PDOException $e) {
                    $error = 'فشل في استيراد قاعدة البيانات: ' . $e->getMessage();
                }
            } else {
                $error = 'ملف قاعدة البيانات غير موجود';
            }
            break;
            
        case 3:
            // إنشاء المجلدات المطلوبة
            $directories = ['uploads', 'logs', 'assets/images'];
            
            foreach ($directories as $dir) {
                if (!is_dir($dir)) {
                    mkdir($dir, 0755, true);
                }
            }
            
            // إنشاء ملفات الحماية
            file_put_contents('uploads/.htaccess', "Options -Indexes\n");
            file_put_contents('logs/.htaccess', "Order Allow,Deny\nDeny from all\n");
            
            header('Location: setup.php?step=4');
            exit;
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام إدارة المطعم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .setup-container {
            max-width: 600px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
        }
        
        .step.active {
            background: #007bff;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .setup-content {
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <i class="fas fa-utensils fa-3x mb-3"></i>
                <h2>إعداد نظام إدارة المطعم</h2>
                <p>مرحباً بك في معالج الإعداد السريع</p>
            </div>
            
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step == 1 ? 'active' : 'completed') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step == 2 ? 'active' : 'completed') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step == 3 ? 'active' : 'completed') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? ($step == 4 ? 'active' : 'completed') : ''; ?>">4</div>
            </div>
            
            <div class="setup-content">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($step == 1): ?>
                    <!-- الخطوة 1: إعداد قاعدة البيانات -->
                    <h4><i class="fas fa-database me-2"></i>إعداد قاعدة البيانات</h4>
                    <p class="text-muted">يرجى إدخال بيانات الاتصال بقاعدة البيانات</p>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">خادم قاعدة البيانات</label>
                            <input type="text" class="form-control" name="db_host" value="localhost" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">اسم قاعدة البيانات</label>
                            <input type="text" class="form-control" name="db_name" value="restaurant_system" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" name="db_user" value="root" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" name="db_pass">
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-arrow-left me-2"></i>
                            التالي
                        </button>
                    </form>
                    
                <?php elseif ($step == 2): ?>
                    <!-- الخطوة 2: استيراد قاعدة البيانات -->
                    <h4><i class="fas fa-download me-2"></i>استيراد قاعدة البيانات</h4>
                    <p class="text-muted">سيتم الآن استيراد هيكل قاعدة البيانات والبيانات الأولية</p>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذه العملية قد تستغرق بضع ثوان
                    </div>
                    
                    <form method="POST">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-download me-2"></i>
                            استيراد قاعدة البيانات
                        </button>
                    </form>
                    
                <?php elseif ($step == 3): ?>
                    <!-- الخطوة 3: إنشاء المجلدات -->
                    <h4><i class="fas fa-folder me-2"></i>إنشاء المجلدات المطلوبة</h4>
                    <p class="text-muted">سيتم إنشاء المجلدات المطلوبة لعمل النظام</p>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إنشاء مجلدات: uploads, logs, assets/images
                    </div>
                    
                    <form method="POST">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-folder-plus me-2"></i>
                            إنشاء المجلدات
                        </button>
                    </form>
                    
                <?php elseif ($step == 4): ?>
                    <!-- الخطوة 4: اكتمال الإعداد -->
                    <div class="text-center">
                        <i class="fas fa-check-circle fa-5x text-success mb-3"></i>
                        <h4>تم إكمال الإعداد بنجاح!</h4>
                        <p class="text-muted">يمكنك الآن استخدام النظام</p>
                        
                        <div class="alert alert-success">
                            <h6>بيانات تسجيل الدخول الافتراضية:</h6>
                            <p class="mb-1"><strong>اسم المستخدم:</strong> admin</p>
                            <p class="mb-0"><strong>كلمة المرور:</strong> password</p>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول
                        </div>
                        
                        <a href="login.php" class="btn btn-success btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول للنظام
                        </a>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                يمكنك حذف ملف setup.php الآن لأسباب أمنية
                            </small>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
