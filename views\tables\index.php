<?php
/**
 * خريطة الطاولات - الصفحة الرئيسية
 * Tables Map - Main Page
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('tables.view');

// الحصول على الطاولات مع معلومات الجلسات النشطة
$sql = "SELECT t.*, 
               ts.id as session_id, 
               ts.start_time, 
               ts.total_customers,
               COUNT(o.id) as active_orders,
               SUM(o.total_amount) as session_total,
               c.name as customer_name
        FROM tables t 
        LEFT JOIN table_sessions ts ON t.id = ts.table_id AND ts.status = 'active'
        LEFT JOIN orders o ON ts.id = o.table_session_id AND o.status NOT IN ('completed', 'cancelled')
        LEFT JOIN customers c ON ts.customer_id = c.id
        WHERE t.is_active = 1
        GROUP BY t.id
        ORDER BY t.table_number";

$tables = DB::select($sql);

$page_title = 'خريطة الطاولات';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">خريطة الطاولات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="list.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-list"></i> قائمة الطاولات
                        </a>
                        <?php if (hasPermission('tables.create')): ?>
                        <a href="add.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> طاولة جديدة
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <?php
                $stats = array(
                    'available' => 0,
                    'occupied' => 0,
                    'reserved' => 0,
                    'maintenance' => 0
                );
                
                foreach ($tables as $table) {
                    $stats[$table['status']]++;
                }
                ?>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3><?php echo $stats['available']; ?></h3>
                            <p class="mb-0">طاولات متاحة</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3><?php echo $stats['occupied']; ?></h3>
                            <p class="mb-0">طاولات مشغولة</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3><?php echo $stats['reserved']; ?></h3>
                            <p class="mb-0">طاولات محجوزة</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3><?php echo $stats['maintenance']; ?></h3>
                            <p class="mb-0">طاولات صيانة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- خريطة الطاولات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">خريطة الطاولات</h5>
                    <small class="text-muted">انقر على الطاولة لعرض التفاصيل أو إدارة الجلسة</small>
                </div>
                <div class="card-body">
                    <div class="tables-grid">
                        <?php foreach ($tables as $table): ?>
                        <div class="table-card <?php echo $table['status']; ?>" 
                             data-table-id="<?php echo $table['id']; ?>"
                             onclick="showTableDetails(<?php echo $table['id']; ?>)">
                            
                            <div class="table-number"><?php echo $table['table_number']; ?></div>
                            
                            <div class="table-capacity">
                                <i class="fas fa-users"></i>
                                <?php echo $table['capacity']; ?> شخص
                            </div>
                            
                            <?php if ($table['status'] === 'occupied' && $table['session_id']): ?>
                            <div class="table-session-info">
                                <div class="session-time" id="session-time-<?php echo $table['id']; ?>">
                                    <?php 
                                    $start_time = strtotime($table['start_time']);
                                    $elapsed = time() - $start_time;
                                    echo gmdate('H:i', $elapsed);
                                    ?>
                                </div>
                                <?php if ($table['active_orders'] > 0): ?>
                                <div class="session-orders"><?php echo $table['active_orders']; ?> طلب</div>
                                <?php endif; ?>
                                <?php if ($table['session_total'] > 0): ?>
                                <div class="session-total"><?php echo formatCurrency($table['session_total']); ?></div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                            
                            <div class="table-status-badge">
                                <?php
                                $status_names = array(
                                    'available' => 'متاحة',
                                    'occupied' => 'مشغولة',
                                    'reserved' => 'محجوزة',
                                    'maintenance' => 'صيانة'
                                );
                                echo $status_names[$table['status']];
                                ?>
                            </div>
                            
                            <?php if ($table['location']): ?>
                            <div class="table-location">
                                <small><?php echo $table['location']; ?></small>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نافذة تفاصيل الطاولة -->
<div class="modal fade" id="tableDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الطاولة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="tableDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<?php
$additional_css = '
<style>
.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.table-card {
    aspect-ratio: 1;
    border: 3px solid;
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: white;
    min-height: 150px;
}

.table-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.table-card.available {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.table-card.occupied {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.table-card.reserved {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.table-card.maintenance {
    border-color: #6c757d;
    background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
}

.table-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.table-capacity {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.table-session-info {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem;
    border-radius: 5px;
    font-size: 0.7rem;
    text-align: center;
    min-width: 60px;
}

.table-status-badge {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
}

.table-location {
    position: absolute;
    bottom: 5px;
    right: 5px;
    font-size: 0.7rem;
    color: #666;
}

@media (max-width: 768px) {
    .tables-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.5rem;
        padding: 0.5rem;
    }
    
    .table-card {
        padding: 0.5rem;
        min-height: 120px;
    }
    
    .table-number {
        font-size: 1.5rem;
    }
}
</style>
';

$additional_js = '
<script>
function showTableDetails(tableId) {
    $.ajax({
        url: "ajax/get_table_details.php",
        type: "POST",
        data: { table_id: tableId },
        success: function(response) {
            $("#tableDetailsContent").html(response);
            $("#tableDetailsModal").modal("show");
        },
        error: function() {
            alert("حدث خطأ في تحميل تفاصيل الطاولة");
        }
    });
}

// تحديث الوقت المنقضي كل دقيقة
setInterval(function() {
    $(".session-time").each(function() {
        var tableId = $(this).attr("id").split("-")[2];
        // يمكن إضافة تحديث الوقت هنا
    });
}, 60000);

// تحديث حالة الطاولات كل 30 ثانية
setInterval(function() {
    if (!document.hidden) {
        updateTablesStatus();
    }
}, 30000);

function updateTablesStatus() {
    $.ajax({
        url: "ajax/get_tables_status.php",
        type: "GET",
        dataType: "json",
        success: function(tables) {
            tables.forEach(function(table) {
                var tableCard = $("[data-table-id=\"" + table.id + "\"]");
                
                if (tableCard.length) {
                    // تحديث الحالة
                    tableCard.removeClass("available occupied reserved maintenance")
                             .addClass(table.status);
                    
                    // تحديث معلومات الجلسة
                    if (table.status === "occupied" && table.session_info) {
                        var sessionInfo = "<div class=\"session-time\">" + table.session_info.elapsed_time + "</div>";
                        if (table.session_info.active_orders > 0) {
                            sessionInfo += "<div class=\"session-orders\">" + table.session_info.active_orders + " طلب</div>";
                        }
                        if (table.session_info.total && table.session_info.total !== "0.00 ر.س") {
                            sessionInfo += "<div class=\"session-total\">" + table.session_info.total + "</div>";
                        }
                        
                        tableCard.find(".table-session-info").html(sessionInfo);
                    } else {
                        tableCard.find(".table-session-info").empty();
                    }
                }
            });
        }
    });
}

$(document).ready(function() {
    // تحديث أولي لحالة الطاولات
    updateTablesStatus();
});
</script>
';

include '../layouts/footer.php';
?>
