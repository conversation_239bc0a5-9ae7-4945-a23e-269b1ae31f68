<?php
/**
 * قائمة المستخدمين
 * Users List
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/User.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('users.view');

$user_model = new User();
$error = '';
$success = '';

// معالجة الحذف
if (isset($_POST['action']) && $_POST['action'] === 'delete' && hasPermission('users.delete')) {
    $user_id = isset($_POST['user_id']) ? $_POST['user_id'] : null;
    if ($user_id && $user_id != $_SESSION['user_id']) { // منع حذف المستخدم الحالي
        if ($user_model->delete($user_id)) {
            $success = 'تم حذف المستخدم بنجاح';
            logActivity('delete_user', 'users', $user_id);
        } else {
            $error = 'فشل في حذف المستخدم';
        }
    } else {
        $error = 'لا يمكن حذف المستخدم الحالي';
    }
}

// معالجة تغيير الحالة
if (isset($_POST['action']) && $_POST['action'] === 'toggle_status' && hasPermission('users.edit')) {
    $user_id = isset($_POST['user_id']) ? $_POST['user_id'] : null;
    $status = isset($_POST['status']) ? $_POST['status'] : 0;
    
    if ($user_id && $user_id != $_SESSION['user_id']) {
        $sql = "UPDATE users SET is_active = ? WHERE id = ?";
        if (DB::execute($sql, array($status, $user_id))) {
            $success = $status ? 'تم تفعيل المستخدم' : 'تم إلغاء تفعيل المستخدم';
            logActivity('toggle_user_status', 'users', $user_id, null, array('status' => $status));
        } else {
            $error = 'فشل في تغيير حالة المستخدم';
        }
    }
}

// الحصول على قائمة المستخدمين
$search = isset($_GET['search']) ? $_GET['search'] : '';
$role_filter = isset($_GET['role']) ? $_GET['role'] : '';

$sql = "SELECT u.*, r.name_ar as role_name 
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        WHERE 1=1";
$params = array();

if ($search) {
    $sql .= " AND (u.full_name LIKE ? OR u.username LIKE ? OR u.email LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($role_filter) {
    $sql .= " AND u.role_id = ?";
    $params[] = $role_filter;
}

$sql .= " ORDER BY u.created_at DESC";
$users = DB::select($sql, $params);

// الحصول على الأدوار
$roles = DB::select("SELECT * FROM roles ORDER BY name_ar");

$page_title = 'إدارة المستخدمين';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المستخدمين</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <?php if (hasPermission('users.create')): ?>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مستخدم جديد
                    </a>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="اسم المستخدم، الاسم الكامل، أو البريد الإلكتروني">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الدور</label>
                            <select class="form-select" name="role">
                                <option value="">جميع الأدوار</option>
                                <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role['id']; ?>" 
                                        <?php echo $role_filter == $role['id'] ? 'selected' : ''; ?>>
                                    <?php echo $role['name_ar']; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <a href="list.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> إلغاء الفلتر
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة المستخدمين -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة المستخدمين (<?php echo count($users); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($users)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مستخدمين</h5>
                            <p class="text-muted">لم يتم العثور على أي مستخدمين مطابقين للبحث</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم المستخدم</th>
                                        <th>الاسم الكامل</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>آخر نشاط</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <div class="user-avatar">
                                                <?php if ($user['avatar']): ?>
                                                    <img src="../../uploads/avatars/<?php echo $user['avatar']; ?>" 
                                                         alt="صورة المستخدم" class="rounded-circle" width="40" height="40">
                                                <?php else: ?>
                                                    <div class="avatar-placeholder">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                            <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                <span class="badge bg-primary">أنت</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                        <td>
                                            <?php if ($user['email']): ?>
                                                <a href="mailto:<?php echo $user['email']; ?>">
                                                    <?php echo htmlspecialchars($user['email']); ?>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $user['role_name'] ? $user['role_name'] : 'غير محدد'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($user['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['last_activity']): ?>
                                                <small class="text-muted">
                                                    <?php echo formatDateTime($user['last_activity']); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">لم يسجل دخول</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo formatDateTime($user['created_at']); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if (hasPermission('users.view')): ?>
                                                <button type="button" class="btn btn-outline-info" 
                                                        onclick="viewUser(<?php echo $user['id']; ?>)" 
                                                        title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php endif; ?>
                                                
                                                <?php if (hasPermission('users.edit')): ?>
                                                <a href="edit.php?id=<?php echo $user['id']; ?>" 
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php endif; ?>
                                                
                                                <?php if (hasPermission('users.edit') && $user['id'] != $_SESSION['user_id']): ?>
                                                <button type="button" class="btn btn-outline-warning" 
                                                        onclick="toggleUserStatus(<?php echo $user['id']; ?>, <?php echo $user['is_active'] ? 0 : 1; ?>)" 
                                                        title="<?php echo $user['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                    <i class="fas fa-<?php echo $user['is_active'] ? 'ban' : 'check'; ?>"></i>
                                                </button>
                                                <?php endif; ?>
                                                
                                                <?php if (hasPermission('users.delete') && $user['id'] != $_SESSION['user_id']): ?>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نافذة تفاصيل المستخدم -->
<div class="modal fade" id="userDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<?php
$additional_css = '
<style>
.user-avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.table td {
    vertical-align: middle;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}
</style>
';

$additional_js = '
<script>
function viewUser(userId) {
    $.ajax({
        url: "ajax/get_user_details.php",
        type: "POST",
        data: { user_id: userId },
        success: function(response) {
            $("#userDetailsContent").html(response);
            $("#userDetailsModal").modal("show");
        },
        error: function() {
            alert("حدث خطأ في تحميل تفاصيل المستخدم");
        }
    });
}

function toggleUserStatus(userId, status) {
    const action = status ? "تفعيل" : "إلغاء تفعيل";
    
    Swal.fire({
        title: action + " المستخدم",
        text: "هل أنت متأكد من " + action + " هذا المستخدم؟",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "نعم، " + action,
        cancelButtonText: "إلغاء"
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement("form");
            form.method = "POST";
            form.innerHTML = `
                <input type="hidden" name="action" value="toggle_status">
                <input type="hidden" name="user_id" value="${userId}">
                <input type="hidden" name="status" value="${status}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function deleteUser(userId, username) {
    Swal.fire({
        title: "حذف المستخدم",
        text: `هل أنت متأكد من حذف المستخدم "${username}"؟ هذا الإجراء لا يمكن التراجع عنه.`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "نعم، احذف",
        cancelButtonText: "إلغاء",
        confirmButtonColor: "#dc3545"
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement("form");
            form.method = "POST";
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="user_id" value="${userId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
';

include '../layouts/footer.php';
?>
