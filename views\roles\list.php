<?php
/**
 * إدارة الأدوار والصلاحيات
 * Roles and Permissions Management
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('roles.view');

$error = '';
$success = '';

// معالجة إضافة دور جديد
if (isset($_POST['action']) && $_POST['action'] === 'add' && hasPermission('roles.create')) {
    $name = sanitize(isset($_POST['name']) ? $_POST['name'] : '');
    $name_ar = sanitize(isset($_POST['name_ar']) ? $_POST['name_ar'] : '');
    $description = sanitize(isset($_POST['description']) ? $_POST['description'] : '');
    $permissions = isset($_POST['permissions']) ? $_POST['permissions'] : array();
    
    if (empty($name_ar)) {
        $error = 'اسم الدور مطلوب';
    } else {
        // التحقق من عدم وجود الدور مسبقاً
        $existing = DB::selectOne("SELECT id FROM roles WHERE name_ar = ?", array($name_ar));
        if ($existing) {
            $error = 'اسم الدور موجود مسبقاً';
        } else {
            $sql = "INSERT INTO roles (name, name_ar, description, permissions, is_active) VALUES (?, ?, ?, ?, 1)";
            $params = array($name, $name_ar, $description, json_encode($permissions));
            
            if (DB::execute($sql, $params)) {
                $success = 'تم إضافة الدور بنجاح';
                logActivity('create_role', 'roles', DB::lastInsertId());
                // إعادة تعيين النموذج
                $_POST = array();
            } else {
                $error = 'فشل في إضافة الدور';
            }
        }
    }
}

// معالجة الحذف
if (isset($_POST['action']) && $_POST['action'] === 'delete' && hasPermission('roles.delete')) {
    $role_id = isset($_POST['role_id']) ? $_POST['role_id'] : null;
    if ($role_id) {
        // التحقق من عدم وجود مستخدمين بهذا الدور
        $users_count = DB::selectOne("SELECT COUNT(*) as count FROM users WHERE role_id = ?", array($role_id))['count'];
        if ($users_count > 0) {
            $error = "لا يمكن حذف الدور لأنه مرتبط بـ $users_count مستخدم";
        } else {
            if (DB::execute("DELETE FROM roles WHERE id = ?", array($role_id))) {
                $success = 'تم حذف الدور بنجاح';
                logActivity('delete_role', 'roles', $role_id);
            } else {
                $error = 'فشل في حذف الدور';
            }
        }
    }
}

// الحصول على قائمة الأدوار
$roles = DB::select("SELECT r.*, COUNT(u.id) as users_count 
                     FROM roles r 
                     LEFT JOIN users u ON r.id = u.role_id 
                     GROUP BY r.id 
                     ORDER BY r.created_at DESC");

// قائمة الصلاحيات المتاحة
$available_permissions = array(
    'dashboard' => array(
        'name' => 'لوحة التحكم',
        'permissions' => array(
            'dashboard.view' => 'عرض لوحة التحكم'
        )
    ),
    'users' => array(
        'name' => 'إدارة المستخدمين',
        'permissions' => array(
            'users.view' => 'عرض المستخدمين',
            'users.create' => 'إضافة مستخدم',
            'users.edit' => 'تعديل المستخدم',
            'users.delete' => 'حذف المستخدم'
        )
    ),
    'roles' => array(
        'name' => 'إدارة الأدوار',
        'permissions' => array(
            'roles.view' => 'عرض الأدوار',
            'roles.create' => 'إضافة دور',
            'roles.edit' => 'تعديل الدور',
            'roles.delete' => 'حذف الدور'
        )
    ),
    'customers' => array(
        'name' => 'إدارة العملاء',
        'permissions' => array(
            'customers.view' => 'عرض العملاء',
            'customers.create' => 'إضافة عميل',
            'customers.edit' => 'تعديل العميل',
            'customers.delete' => 'حذف العميل'
        )
    ),
    'products' => array(
        'name' => 'إدارة المنتجات',
        'permissions' => array(
            'products.view' => 'عرض المنتجات',
            'products.create' => 'إضافة منتج',
            'products.edit' => 'تعديل المنتج',
            'products.delete' => 'حذف المنتج'
        )
    ),
    'orders' => array(
        'name' => 'إدارة الطلبات',
        'permissions' => array(
            'orders.view' => 'عرض الطلبات',
            'orders.create' => 'إنشاء طلب',
            'orders.edit' => 'تعديل الطلب',
            'orders.delete' => 'حذف الطلب'
        )
    ),
    'tables' => array(
        'name' => 'إدارة الطاولات',
        'permissions' => array(
            'tables.view' => 'عرض الطاولات',
            'tables.create' => 'إضافة طاولة',
            'tables.edit' => 'تعديل الطاولة',
            'tables.delete' => 'حذف الطاولة'
        )
    ),
    'reports' => array(
        'name' => 'التقارير',
        'permissions' => array(
            'reports.sales' => 'تقارير المبيعات',
            'reports.inventory' => 'تقارير المخزون',
            'reports.customers' => 'تقارير العملاء'
        )
    ),
    'settings' => array(
        'name' => 'الإعدادات',
        'permissions' => array(
            'settings.view' => 'عرض الإعدادات',
            'settings.edit' => 'تعديل الإعدادات'
        )
    )
);

$page_title = 'إدارة الأدوار والصلاحيات';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الأدوار والصلاحيات</h1>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- قائمة الأدوار -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">الأدوار الحالية</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($roles)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-user-shield fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد أدوار</h5>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>اسم الدور</th>
                                                <th>الوصف</th>
                                                <th>عدد المستخدمين</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($roles as $role): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($role['name_ar']); ?></strong>
                                                    <?php if ($role['name']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($role['name']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($role['description']): ?>
                                                        <?php echo htmlspecialchars($role['description']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">لا يوجد وصف</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $role['users_count']; ?> مستخدم</span>
                                                </td>
                                                <td>
                                                    <?php if ($role['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-info" 
                                                                onclick="viewRolePermissions(<?php echo $role['id']; ?>)" 
                                                                title="عرض الصلاحيات">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        
                                                        <?php if (hasPermission('roles.edit')): ?>
                                                        <button type="button" class="btn btn-outline-primary" 
                                                                onclick="editRole(<?php echo $role['id']; ?>)" 
                                                                title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <?php endif; ?>
                                                        
                                                        <?php if (hasPermission('roles.delete') && $role['users_count'] == 0): ?>
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                onclick="deleteRole(<?php echo $role['id']; ?>, '<?php echo htmlspecialchars($role['name_ar']); ?>')" 
                                                                title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- إضافة دور جديد -->
                <?php if (hasPermission('roles.create')): ?>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">إضافة دور جديد</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="addRoleForm">
                                <input type="hidden" name="action" value="add">
                                
                                <div class="mb-3">
                                    <label for="name_ar" class="form-label">اسم الدور <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name_ar" name="name_ar" 
                                           value="<?php echo htmlspecialchars(isset($_POST['name_ar']) ? $_POST['name_ar'] : ''); ?>" 
                                           required maxlength="100">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">الاسم بالإنجليزية</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars(isset($_POST['name']) ? $_POST['name'] : ''); ?>" 
                                           maxlength="100">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars(isset($_POST['description']) ? $_POST['description'] : ''); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الصلاحيات</label>
                                    <div class="permissions-container" style="max-height: 300px; overflow-y: auto;">
                                        <?php foreach ($available_permissions as $group_key => $group): ?>
                                        <div class="permission-group mb-3">
                                            <h6 class="text-primary"><?php echo $group['name']; ?></h6>
                                            <?php foreach ($group['permissions'] as $perm_key => $perm_name): ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="permissions[]" value="<?php echo $perm_key; ?>" 
                                                       id="perm_<?php echo $perm_key; ?>"
                                                       <?php echo (isset($_POST['permissions']) && in_array($perm_key, $_POST['permissions'])) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="perm_<?php echo $perm_key; ?>">
                                                    <?php echo $perm_name; ?>
                                                </label>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> إضافة الدور
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>

<!-- نافذة عرض الصلاحيات -->
<div class="modal fade" id="permissionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صلاحيات الدور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="permissionsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<?php
$additional_css = '
<style>
.permission-group {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background: #f8f9fa;
}

.permission-group h6 {
    margin-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.25rem;
}

.form-check {
    margin-bottom: 0.25rem;
}

.permissions-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
}
</style>
';

$additional_js = '
<script>
function viewRolePermissions(roleId) {
    $.ajax({
        url: "ajax/get_role_permissions.php",
        type: "POST",
        data: { role_id: roleId },
        success: function(response) {
            $("#permissionsContent").html(response);
            $("#permissionsModal").modal("show");
        },
        error: function() {
            alert("حدث خطأ في تحميل صلاحيات الدور");
        }
    });
}

function editRole(roleId) {
    // يمكن إضافة نافذة تعديل أو توجيه لصفحة تعديل
    window.location.href = "edit.php?id=" + roleId;
}

function deleteRole(roleId, roleName) {
    Swal.fire({
        title: "حذف الدور",
        text: `هل أنت متأكد من حذف الدور "${roleName}"؟ هذا الإجراء لا يمكن التراجع عنه.`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "نعم، احذف",
        cancelButtonText: "إلغاء",
        confirmButtonColor: "#dc3545"
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement("form");
            form.method = "POST";
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="role_id" value="${roleId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// تحديد/إلغاء تحديد جميع الصلاحيات في مجموعة
document.querySelectorAll(".permission-group h6").forEach(function(header) {
    header.style.cursor = "pointer";
    header.addEventListener("click", function() {
        const group = this.parentElement;
        const checkboxes = group.querySelectorAll("input[type=checkbox]");
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        
        checkboxes.forEach(function(checkbox) {
            checkbox.checked = !allChecked;
        });
    });
});
</script>
';

include '../layouts/footer.php';
?>
