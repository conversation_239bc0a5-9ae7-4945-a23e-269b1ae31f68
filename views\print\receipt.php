<?php
/**
 * طباعة الفاتورة
 * Print Receipt
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Order.php';

// التحقق من تسجيل الدخول
requireLogin();

$order_id = $_GET['id'] ?? null;

if (!$order_id) {
    die('معرف الطلب مطلوب');
}

$order_model = new Order();
$order = $order_model->getById($order_id);

if (!$order) {
    die('الطلب غير موجود');
}

// الحصول على عناصر الطلب
$items = $order_model->getItems($order_id);

// الحصول على إعدادات المطعم
$restaurant_name = getSetting('restaurant_name', 'مطعم الذواقة');
$restaurant_address = getSetting('restaurant_address', '');
$restaurant_phone = getSetting('restaurant_phone', '');
$tax_number = getSetting('tax_number', '');
$receipt_footer = getSetting('receipt_footer', 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى');
$receipt_width = getSetting('receipt_width', '80');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم <?php echo $order['order_number']; ?></title>
    
    <style>
        @media print {
            @page {
                size: <?php echo $receipt_width; ?>mm auto;
                margin: 0;
            }
            
            body {
                margin: 0;
                padding: 5mm;
            }
            
            .no-print {
                display: none;
            }
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 10px;
            width: <?php echo $receipt_width; ?>mm;
            background: white;
        }
        
        .receipt-header {
            text-align: center;
            border-bottom: 2px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .restaurant-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .restaurant-info {
            font-size: 10px;
            margin-bottom: 2px;
        }
        
        .order-info {
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        
        .order-info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .items-table {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .item-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding-bottom: 3px;
            border-bottom: 1px dotted #ccc;
        }
        
        .item-name {
            flex: 1;
            font-weight: bold;
        }
        
        .item-details {
            font-size: 10px;
            color: #666;
            margin-right: 10px;
        }
        
        .item-qty {
            width: 30px;
            text-align: center;
        }
        
        .item-price {
            width: 60px;
            text-align: left;
        }
        
        .totals {
            border-top: 2px dashed #000;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .grand-total {
            font-size: 14px;
            font-weight: bold;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            padding: 5px 0;
            margin: 5px 0;
        }
        
        .receipt-footer {
            text-align: center;
            border-top: 2px dashed #000;
            padding-top: 10px;
            margin-top: 10px;
            font-size: 10px;
        }
        
        .qr-code {
            text-align: center;
            margin: 10px 0;
        }
        
        .print-button {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> طباعة
    </button>
    
    <div class="receipt">
        <!-- رأس الفاتورة -->
        <div class="receipt-header">
            <div class="restaurant-name"><?php echo $restaurant_name; ?></div>
            <?php if ($restaurant_address): ?>
            <div class="restaurant-info"><?php echo $restaurant_address; ?></div>
            <?php endif; ?>
            <?php if ($restaurant_phone): ?>
            <div class="restaurant-info">هاتف: <?php echo $restaurant_phone; ?></div>
            <?php endif; ?>
            <?php if ($tax_number): ?>
            <div class="restaurant-info">الرقم الضريبي: <?php echo $tax_number; ?></div>
            <?php endif; ?>
        </div>
        
        <!-- معلومات الطلب -->
        <div class="order-info">
            <div class="order-info-row">
                <span>رقم الطلب:</span>
                <span><strong><?php echo $order['order_number']; ?></strong></span>
            </div>
            <div class="order-info-row">
                <span>التاريخ:</span>
                <span><?php echo formatDate($order['created_at'], 'd/m/Y'); ?></span>
            </div>
            <div class="order-info-row">
                <span>الوقت:</span>
                <span><?php echo formatDate($order['created_at'], 'H:i'); ?></span>
            </div>
            <div class="order-info-row">
                <span>النوع:</span>
                <span>
                    <?php
                    $type_names = [
                        'dine_in' => 'طاولة',
                        'takeaway' => 'سفري',
                        'delivery' => 'توصيل'
                    ];
                    echo $type_names[$order['order_type']];
                    ?>
                </span>
            </div>
            <?php if ($order['table_number']): ?>
            <div class="order-info-row">
                <span>الطاولة:</span>
                <span><?php echo $order['table_number']; ?></span>
            </div>
            <?php endif; ?>
            <?php if ($order['customer_name']): ?>
            <div class="order-info-row">
                <span>العميل:</span>
                <span><?php echo $order['customer_name']; ?></span>
            </div>
            <?php endif; ?>
            <?php if ($order['customer_phone']): ?>
            <div class="order-info-row">
                <span>الهاتف:</span>
                <span><?php echo $order['customer_phone']; ?></span>
            </div>
            <?php endif; ?>
            <div class="order-info-row">
                <span>الكاشير:</span>
                <span><?php echo $order['created_by_name']; ?></span>
            </div>
        </div>
        
        <!-- عناصر الطلب -->
        <div class="items-table">
            <?php foreach ($items as $item): ?>
            <div class="item-row">
                <div style="flex: 1;">
                    <div class="item-name"><?php echo $item['product_name']; ?></div>
                    <?php if ($item['size_name']): ?>
                    <div class="item-details">الحجم: <?php echo $item['size_name']; ?></div>
                    <?php endif; ?>
                    
                    <?php
                    // الحصول على الإضافات
                    $addons = $order_model->getItemAddons($item['id']);
                    if ($addons):
                    ?>
                    <div class="item-details">
                        الإضافات: 
                        <?php
                        $addon_names = [];
                        foreach ($addons as $addon) {
                            $addon_names[] = $addon['addon_name'] . ($addon['quantity'] > 1 ? ' (' . $addon['quantity'] . ')' : '');
                        }
                        echo implode(', ', $addon_names);
                        ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($item['special_instructions']): ?>
                    <div class="item-details">ملاحظة: <?php echo $item['special_instructions']; ?></div>
                    <?php endif; ?>
                </div>
                <div class="item-qty"><?php echo $item['quantity']; ?></div>
                <div class="item-price"><?php echo formatCurrency($item['total_price']); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- الإجماليات -->
        <div class="totals">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span><?php echo formatCurrency($order['subtotal']); ?></span>
            </div>
            
            <?php if ($order['discount_amount'] > 0): ?>
            <div class="total-row">
                <span>الخصم:</span>
                <span>-<?php echo formatCurrency($order['discount_amount']); ?></span>
            </div>
            <?php endif; ?>
            
            <?php if ($order['tax_amount'] > 0): ?>
            <div class="total-row">
                <span>الضريبة (15%):</span>
                <span><?php echo formatCurrency($order['tax_amount']); ?></span>
            </div>
            <?php endif; ?>
            
            <?php if ($order['service_charge'] > 0): ?>
            <div class="total-row">
                <span>رسوم الخدمة (10%):</span>
                <span><?php echo formatCurrency($order['service_charge']); ?></span>
            </div>
            <?php endif; ?>
            
            <div class="total-row grand-total">
                <span>الإجمالي:</span>
                <span><?php echo formatCurrency($order['total_amount']); ?></span>
            </div>
            
            <?php if ($order['loyalty_points_earned'] > 0): ?>
            <div class="total-row">
                <span>نقاط الولاء المكتسبة:</span>
                <span><?php echo $order['loyalty_points_earned']; ?> نقطة</span>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- رمز QR (اختياري) -->
        <?php if (getSetting('enable_qr_code')): ?>
        <div class="qr-code">
            <!-- يمكن إضافة رمز QR هنا -->
            <div style="border: 1px solid #000; width: 80px; height: 80px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                QR CODE
            </div>
        </div>
        <?php endif; ?>
        
        <!-- تذييل الفاتورة -->
        <div class="receipt-footer">
            <?php if ($receipt_footer): ?>
            <div style="margin-bottom: 10px;"><?php echo nl2br($receipt_footer); ?></div>
            <?php endif; ?>
            
            <div style="font-size: 9px; color: #666;">
                تم الطباعة في: <?php echo formatDate(date('Y-m-d H:i:s'), 'd/m/Y H:i'); ?>
            </div>
            
            <?php if (getSetting('show_website')): ?>
            <div style="margin-top: 5px; font-size: 9px;">
                <?php echo getSetting('website_url', 'www.restaurant.com'); ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // طباعة تلقائية عند التحميل
        window.onload = function() {
            <?php if (getSetting('auto_print_receipt', '1')): ?>
            setTimeout(function() {
                window.print();
            }, 500);
            <?php endif; ?>
        };
        
        // إغلاق النافذة بعد الطباعة
        window.onafterprint = function() {
            setTimeout(function() {
                window.close();
            }, 1000);
        };
    </script>
</body>
</html>
