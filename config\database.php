<?php
/**
 * إعدادات قاعدة البيانات
 * Database Configuration
 * Restaurant Management System
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'restaurant_system');
define('DB_USER', 'root');
define('DB_PASS', '19951997');
define('DB_CHARSET', 'utf8');

// إعدادات النظام
define('SITE_URL', 'http://127.0.0.1/3');
define('SITE_NAME', 'نظام إدارة المطعم');
define('SITE_NAME_EN', 'Restaurant Management System');

// إعدادات الأمان
define('HASH_ALGO', 'sha256');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB
define('ALLOWED_EXTENSIONS', array('jpg', 'jpeg', 'png', 'gif', 'pdf'));

// إعدادات التاريخ والوقت
date_default_timezone_set('Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات العملة
define('CURRENCY', 'SAR');
define('CURRENCY_SYMBOL', 'ر.س');
define('DECIMAL_PLACES', 2);

// إعدادات الطباعة
define('RECEIPT_WIDTH', 48); // عدد الأحرف
define('KITCHEN_RECEIPT_WIDTH', 32);

// إعدادات نظام الولاء
define('LOYALTY_POINTS_PER_SAR', 1);
define('LOYALTY_POINT_VALUE', 0.1); // قيمة النقطة الواحدة بالريال

// إعدادات الضرائب والرسوم
define('DEFAULT_TAX_RATE', 15); // 15%
define('DEFAULT_SERVICE_CHARGE', 10); // 10%

// أوضاع التشغيل
define('DEBUG_MODE', true);
define('MAINTENANCE_MODE', false);

// إعدادات الجلسات
ini_set('session.gc_maxlifetime', SESSION_TIMEOUT);
ini_set('session.cookie_lifetime', SESSION_TIMEOUT);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// فئة الاتصال بقاعدة البيانات
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = array(
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            );
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
            } else {
                die("خطأ في الاتصال بقاعدة البيانات");
            }
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // منع النسخ
    private function __clone() {}
    
    // منع إلغاء التسلسل
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// فئة مساعدة للاستعلامات
class DB {
    private static $db = null;
    
    private static function getDB() {
        if (self::$db === null) {
            self::$db = Database::getInstance()->getConnection();
        }
        return self::$db;
    }
    
    // تنفيذ استعلام SELECT
    public static function select($sql, $params = array()) {
        try {
            $stmt = self::getDB()->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            self::logError($e, $sql, $params);
            return false;
        }
    }
    
    // تنفيذ استعلام SELECT لسجل واحد
    public static function selectOne($sql, $params = array()) {
        try {
            $stmt = self::getDB()->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            self::logError($e, $sql, $params);
            return false;
        }
    }
    
    // تنفيذ استعلام INSERT/UPDATE/DELETE
    public static function execute($sql, $params = array()) {
        try {
            $stmt = self::getDB()->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            self::logError($e, $sql, $params);
            return false;
        }
    }
    
    // إدراج سجل جديد وإرجاع ID
    public static function insert($sql, $params = array()) {
        try {
            $stmt = self::getDB()->prepare($sql);
            if ($stmt->execute($params)) {
                return self::getDB()->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            self::logError($e, $sql, $params);
            return false;
        }
    }
    
    // بدء معاملة
    public static function beginTransaction() {
        return self::getDB()->beginTransaction();
    }
    
    // تأكيد المعاملة
    public static function commit() {
        return self::getDB()->commit();
    }
    
    // إلغاء المعاملة
    public static function rollback() {
        return self::getDB()->rollback();
    }
    
    // تسجيل الأخطاء
    private static function logError($exception, $sql, $params) {
        $error = date('Y-m-d H:i:s') . " - " . $exception->getMessage() . "\n";
        $error .= "SQL: " . $sql . "\n";
        $error .= "Params: " . print_r($params, true) . "\n\n";
        
        if (DEBUG_MODE) {
            echo "<pre>Database Error: " . htmlspecialchars($error) . "</pre>";
        }
        
        // كتابة الخطأ في ملف السجل
        error_log($error, 3, 'logs/database_errors.log');
    }
}

// دوال مساعدة عامة
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

function formatCurrency($amount) {
    return number_format($amount, DECIMAL_PLACES) . ' ' . CURRENCY_SYMBOL;
}

function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
    if (empty($date) || $date == '0000-00-00' || $date == '0000-00-00 00:00:00') {
        return '';
    }
    return date($format, strtotime($date));
}

function generateOrderNumber() {
    $prefix = getSetting('order_number_prefix', 'ORD');
    $number = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    return $prefix . date('ymd') . $number;
}

function generateBillNumber() {
    $prefix = getSetting('bill_number_prefix', 'BILL');
    $number = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    return $prefix . date('ymd') . $number;
}

// تم نقل دالة getSetting إلى config/init.php لتجنب التعريف المزدوج

// تم نقل دالة setSetting إلى config/init.php لتجنب التعريف المزدوج

// التحقق من وضع الصيانة
if (MAINTENANCE_MODE && !isset($_SESSION['user_id'])) {
    die('النظام تحت الصيانة حالياً. يرجى المحاولة لاحقاً.');
}
?>
