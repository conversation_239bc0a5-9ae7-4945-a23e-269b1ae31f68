<?php
/**
 * الدوال المساعدة العامة
 * General Helper Functions
 * Restaurant Management System
 */

// التحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// التحقق من الصلاحية
function hasPermission($permission) {
    if (!isLoggedIn()) {
        return false;
    }
    
    if (!isset($_SESSION['permissions'])) {
        loadUserPermissions();
    }
    
    return in_array($permission, $_SESSION['permissions']);
}

// تحميل صلاحيات المستخدم
function loadUserPermissions() {
    if (!isLoggedIn()) {
        return;
    }
    
    $sql = "SELECT p.name 
            FROM permissions p 
            JOIN role_permissions rp ON p.id = rp.permission_id 
            JOIN users u ON u.role_id = rp.role_id 
            WHERE u.id = ?";
    
    $permissions = DB::select($sql, array($_SESSION['user_id']));
    $_SESSION['permissions'] = array_column($permissions, 'name');
}

// إعادة توجيه المستخدم
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// التحقق من الصلاحية وإعادة التوجيه
function requirePermission($permission, $redirect_url = 'index.php') {
    if (!hasPermission($permission)) {
        $_SESSION['error'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
        redirect($redirect_url);
    }
}

// التحقق من تسجيل الدخول وإعادة التوجيه
function requireLogin($redirect_url = 'login.php') {
    if (!isLoggedIn()) {
        redirect($redirect_url);
    }
}

// تم نقل دالة logActivity إلى config/init.php لتجنب التعريف المزدوج

// عرض الرسائل
function showMessage($type, $message) {
    $class = '';
    $icon = '';
    
    switch ($type) {
        case 'success':
            $class = 'alert-success';
            $icon = 'fa-check-circle';
            break;
        case 'error':
            $class = 'alert-danger';
            $icon = 'fa-exclamation-circle';
            break;
        case 'warning':
            $class = 'alert-warning';
            $icon = 'fa-exclamation-triangle';
            break;
        case 'info':
            $class = 'alert-info';
            $icon = 'fa-info-circle';
            break;
        default:
            $class = 'alert-info';
            $icon = 'fa-info-circle';
    }
    
    echo '<div class="alert ' . $class . ' alert-dismissible fade show" role="alert">';
    echo '<i class="fas ' . $icon . ' me-2"></i>';
    echo $message;
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
}

// عرض رسائل الجلسة
function showSessionMessages() {
    $types = array('success', 'error', 'warning', 'info');
    
    foreach ($types as $type) {
        if (isset($_SESSION[$type])) {
            showMessage($type, $_SESSION[$type]);
            unset($_SESSION[$type]);
        }
    }
}

// التحقق من صحة البريد الإلكتروني
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// التحقق من صحة رقم الهاتف السعودي
function isValidSaudiPhone($phone) {
    $pattern = '/^((\+966)|0)?5[0-9]{8}$/';
    return preg_match($pattern, $phone);
}

// تنسيق رقم الهاتف
function formatPhone($phone) {
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    if (strpos($phone, '+966') === 0) {
        return $phone;
    } elseif (strpos($phone, '966') === 0) {
        return '+' . $phone;
    } elseif (strpos($phone, '05') === 0) {
        return '+966' . substr($phone, 1);
    } elseif (strpos($phone, '5') === 0 && strlen($phone) == 9) {
        return '+966' . $phone;
    }
    
    return $phone;
}

// حساب الضريبة
function calculateTax($amount, $rate = null) {
    if ($rate === null) {
        $rate = getSetting('tax_rate', DEFAULT_TAX_RATE);
    }
    return round($amount * ($rate / 100), DECIMAL_PLACES);
}

// حساب رسوم الخدمة
function calculateServiceCharge($amount, $rate = null) {
    if ($rate === null) {
        $rate = getSetting('service_charge_rate', DEFAULT_SERVICE_CHARGE);
    }
    return round($amount * ($rate / 100), DECIMAL_PLACES);
}

// حساب نقاط الولاء
function calculateLoyaltyPoints($amount) {
    $rate = getSetting('loyalty_points_rate', LOYALTY_POINTS_PER_SAR);
    return floor($amount * $rate);
}

// حساب قيمة نقاط الولاء
function calculateLoyaltyValue($points) {
    $value = getSetting('loyalty_point_value', LOYALTY_POINT_VALUE);
    return round($points * $value, DECIMAL_PLACES);
}

// التحقق من الوردية النشطة
function getActiveShift($user_id = null) {
    if ($user_id === null && isLoggedIn()) {
        $user_id = $_SESSION['user_id'];
    }
    
    if (!$user_id) {
        return false;
    }
    
    $sql = "SELECT * FROM shifts WHERE user_id = ? AND status = 'active' ORDER BY id DESC LIMIT 1";
    return DB::selectOne($sql, array($user_id));
}

// التحقق من وجود وردية نشطة
function hasActiveShift($user_id = null) {
    return getActiveShift($user_id) !== false;
}

// بدء وردية جديدة
function startShift($opening_cash, $notes = '') {
    if (!isLoggedIn()) {
        return false;
    }
    
    // التحقق من عدم وجود وردية نشطة
    if (hasActiveShift()) {
        return false;
    }
    
    $sql = "INSERT INTO shifts (user_id, shift_date, opening_cash, notes) 
            VALUES (?, CURDATE(), ?, ?)";
    
    $shift_id = DB::insert($sql, array($_SESSION['user_id'], $opening_cash, $notes));
    
    if ($shift_id) {
        logActivity('start_shift', 'shifts', $shift_id);
        return $shift_id;
    }
    
    return false;
}

// إنهاء الوردية
function endShift($closing_cash, $notes = '') {
    if (!isLoggedIn()) {
        return false;
    }
    
    $shift = getActiveShift();
    if (!$shift) {
        return false;
    }
    
    // حساب المبيعات المتوقعة
    $sql = "SELECT COALESCE(SUM(total_amount), 0) as total_sales,
                   COUNT(*) as total_orders
            FROM orders 
            WHERE created_by = ? 
            AND DATE(created_at) = ? 
            AND status = 'completed'";
    
    $sales = DB::selectOne($sql, array($_SESSION['user_id'], $shift['shift_date']));
    
    $expected_cash = $shift['opening_cash'] + $sales['total_sales'];
    $cash_difference = $closing_cash - $expected_cash;
    
    $sql = "UPDATE shifts SET 
            end_time = NOW(),
            closing_cash = ?,
            expected_cash = ?,
            cash_difference = ?,
            total_sales = ?,
            total_orders = ?,
            notes = CONCAT(COALESCE(notes, ''), ?, ?),
            status = 'closed',
            closed_by = ?
            WHERE id = ?";
    
    $params = array(
        $closing_cash,
        $expected_cash,
        $cash_difference,
        $sales['total_sales'],
        $sales['total_orders'],
        $notes ? "\n" . $notes : '',
        $cash_difference != 0 ? "\nفرق النقدية: " . formatCurrency($cash_difference) : '',
        $_SESSION['user_id'],
        $shift['id']
    );
    
    if (DB::execute($sql, $params)) {
        logActivity('end_shift', 'shifts', $shift['id']);
        return true;
    }
    
    return false;
}

// الحصول على معلومات المستخدم الحالي
function getCurrentUser() {
    if (!isLoggedIn()) {
        return false;
    }
    
    $sql = "SELECT u.*, r.name as role_name, r.name_ar as role_name_ar 
            FROM users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = ?";
    
    return DB::selectOne($sql, array($_SESSION['user_id']));
}

// التحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// تشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// إنشاء رمز عشوائي
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// تنظيف اسم الملف
function sanitizeFilename($filename) {
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    return substr($filename, 0, 100);
}

// رفع الملف
function uploadFile($file, $allowed_types = null, $max_size = null) {
    if ($allowed_types === null) {
        $allowed_types = ALLOWED_EXTENSIONS;
    }
    
    if ($max_size === null) {
        $max_size = MAX_FILE_SIZE;
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return array('success' => false, 'error' => 'خطأ في رفع الملف');
    }
    
    if ($file['size'] > $max_size) {
        return array('success' => false, 'error' => 'حجم الملف كبير جداً');
    }
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowed_types)) {
        return array('success' => false, 'error' => 'نوع الملف غير مسموح');
    }
    
    $filename = uniqid() . '.' . $extension;
    $filepath = UPLOAD_PATH . $filename;
    
    if (!is_dir(UPLOAD_PATH)) {
        mkdir(UPLOAD_PATH, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return array('success' => true, 'filename' => $filename, 'filepath' => $filepath);
    }
    
    return array('success' => false, 'error' => 'فشل في حفظ الملف');
}

// حذف الملف
function deleteFile($filename) {
    $filepath = UPLOAD_PATH . $filename;
    if (file_exists($filepath)) {
        return unlink($filepath);
    }
    return true;
}
?>
