<?php
/**
 * إنشاء جلسة طاولة جديدة
 * Create New Table Session
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('orders.create');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

$table_id = $_POST['table_id'] ?? null;
$customer_id = $_POST['customer_id'] ?? null;

if (!$table_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الطاولة مطلوب']);
    exit;
}

try {
    DB::beginTransaction();
    
    // التحقق من حالة الطاولة
    $table = DB::selectOne("SELECT * FROM tables WHERE id = ?", [$table_id]);
    
    if (!$table) {
        throw new Exception('الطاولة غير موجودة');
    }
    
    if ($table['status'] !== 'available') {
        throw new Exception('الطاولة غير متاحة');
    }
    
    // إنشاء رمز الجلسة
    $session_code = 'S' . date('ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // إنشاء جلسة جديدة
    $sql = "INSERT INTO table_sessions (table_id, session_code, total_customers, created_by) 
            VALUES (?, ?, 1, ?)";
    
    $session_id = DB::insert($sql, [$table_id, $session_code, $_SESSION['user_id']]);
    
    if (!$session_id) {
        throw new Exception('فشل في إنشاء الجلسة');
    }
    
    // إضافة العميل للجلسة إذا كان موجوداً
    if ($customer_id) {
        $sql = "INSERT INTO table_session_customers (session_id, customer_id, seat_number) 
                VALUES (?, ?, 1)";
        DB::execute($sql, [$session_id, $customer_id]);
    }
    
    // تحديث حالة الطاولة
    $sql = "UPDATE tables SET status = 'occupied' WHERE id = ?";
    DB::execute($sql, [$table_id]);
    
    DB::commit();
    
    logActivity('create_table_session', 'table_sessions', $session_id);
    
    echo json_encode([
        'success' => true,
        'session_id' => $session_id,
        'session_code' => $session_code,
        'message' => 'تم إنشاء جلسة الطاولة بنجاح'
    ]);
    
} catch (Exception $e) {
    DB::rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
