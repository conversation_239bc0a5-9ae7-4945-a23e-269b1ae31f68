<?php
/**
 * الحصول على تفاصيل العميل
 * Get Customer Details
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';
require_once '../../../models/Customer.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('customers.view');

$customer_id = isset($_POST['customer_id']) ? $_POST['customer_id'] : null;

if (!$customer_id) {
    echo '<div class="alert alert-danger">معرف العميل مطلوب</div>';
    exit;
}

$customer_model = new Customer();
$customer = $customer_model->getById($customer_id);

if (!$customer) {
    echo '<div class="alert alert-danger">العميل غير موجود</div>';
    exit;
}

// الحصول على آخر الطلبات
$recent_orders = DB::select("
    SELECT o.*, 
           CASE 
               WHEN o.order_type = 'dine_in' THEN 'طاولة'
               WHEN o.order_type = 'takeaway' THEN 'سفري'
               WHEN o.order_type = 'delivery' THEN 'توصيل'
           END as order_type_name,
           CASE 
               WHEN o.status = 'pending' THEN 'قيد الانتظار'
               WHEN o.status = 'confirmed' THEN 'مؤكد'
               WHEN o.status = 'preparing' THEN 'قيد التحضير'
               WHEN o.status = 'ready' THEN 'جاهز'
               WHEN o.status = 'completed' THEN 'مكتمل'
               WHEN o.status = 'cancelled' THEN 'ملغي'
           END as status_name
    FROM orders o 
    WHERE o.customer_id = ? 
    ORDER BY o.created_at DESC 
    LIMIT 5
", [$customer_id]);

// الحصول على المنتجات المفضلة
$favorite_products = DB::select("
    SELECT p.name_ar, 
           COUNT(oi.id) as order_count,
           SUM(oi.quantity) as total_quantity,
           SUM(oi.total_price) as total_spent
    FROM order_items oi
    JOIN products p ON oi.product_id = p.id
    JOIN orders o ON oi.order_id = o.id
    WHERE o.customer_id = ? AND o.status = 'completed'
    GROUP BY p.id
    ORDER BY order_count DESC
    LIMIT 5
", [$customer_id]);

// إحصائيات الطلبات حسب النوع
$order_types_stats = DB::select("
    SELECT order_type,
           COUNT(*) as count,
           SUM(total_amount) as total_amount
    FROM orders 
    WHERE customer_id = ? AND status = 'completed'
    GROUP BY order_type
", [$customer_id]);
?>

<div class="row">
    <div class="col-md-6">
        <h5>معلومات العميل</h5>
        <table class="table table-sm">
            <tr>
                <td><strong>الاسم:</strong></td>
                <td><?php echo $customer['name']; ?></td>
            </tr>
            <tr>
                <td><strong>رقم الهاتف:</strong></td>
                <td>
                    <a href="tel:<?php echo $customer['phone']; ?>" class="text-decoration-none">
                        <?php echo $customer['phone']; ?>
                    </a>
                </td>
            </tr>
            <?php if ($customer['email']): ?>
            <tr>
                <td><strong>البريد الإلكتروني:</strong></td>
                <td>
                    <a href="mailto:<?php echo $customer['email']; ?>" class="text-decoration-none">
                        <?php echo $customer['email']; ?>
                    </a>
                </td>
            </tr>
            <?php endif; ?>
            <?php if ($customer['date_of_birth']): ?>
            <tr>
                <td><strong>تاريخ الميلاد:</strong></td>
                <td><?php echo formatDate($customer['date_of_birth'], 'd/m/Y'); ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($customer['address']): ?>
            <tr>
                <td><strong>العنوان:</strong></td>
                <td><?php echo $customer['address']; ?></td>
            </tr>
            <?php endif; ?>
            <tr>
                <td><strong>تاريخ التسجيل:</strong></td>
                <td><?php echo formatDate($customer['created_at'], 'd/m/Y'); ?></td>
            </tr>
            <tr>
                <td><strong>النوع:</strong></td>
                <td>
                    <?php if ($customer['is_vip']): ?>
                    <span class="badge bg-warning">
                        <i class="fas fa-crown"></i> مميز
                    </span>
                    <?php else: ?>
                    <span class="badge bg-secondary">عادي</span>
                    <?php endif; ?>
                </td>
            </tr>
        </table>
    </div>
    
    <div class="col-md-6">
        <h5>إحصائيات العميل</h5>
        <div class="row">
            <div class="col-6">
                <div class="card bg-primary text-white mb-2">
                    <div class="card-body text-center">
                        <h4><?php echo number_format($customer['total_visits']); ?></h4>
                        <small>إجمالي الزيارات</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card bg-success text-white mb-2">
                    <div class="card-body text-center">
                        <h4><?php echo formatCurrency($customer['total_spent']); ?></h4>
                        <small>إجمالي الإنفاق</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card bg-warning text-white mb-2">
                    <div class="card-body text-center">
                        <h4><?php echo number_format($customer['loyalty_points']); ?></h4>
                        <small>نقاط الولاء</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card bg-info text-white mb-2">
                    <div class="card-body text-center">
                        <h4><?php echo formatCurrency($customer['average_order_value']); ?></h4>
                        <small>متوسط قيمة الطلب</small>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if ($customer['last_visit']): ?>
        <div class="alert alert-info">
            <i class="fas fa-clock me-2"></i>
            آخر زيارة: <?php echo formatDate($customer['last_visit'], 'd/m/Y H:i'); ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- إحصائيات الطلبات حسب النوع -->
<?php if (!empty($order_types_stats)): ?>
<hr>
<h6>إحصائيات الطلبات حسب النوع</h6>
<div class="row">
    <?php foreach ($order_types_stats as $stat): ?>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h5>
                    <?php
                    $type_names = [
                        'dine_in' => 'طاولة',
                        'takeaway' => 'سفري',
                        'delivery' => 'توصيل'
                    ];
                    echo $type_names[$stat['order_type']];
                    ?>
                </h5>
                <p class="mb-1"><strong><?php echo $stat['count']; ?></strong> طلب</p>
                <p class="mb-0"><?php echo formatCurrency($stat['total_amount']); ?></p>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>

<!-- المنتجات المفضلة -->
<?php if (!empty($favorite_products)): ?>
<hr>
<h6>المنتجات المفضلة</h6>
<div class="table-responsive">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>المنتج</th>
                <th>عدد الطلبات</th>
                <th>الكمية الإجمالية</th>
                <th>الإنفاق الإجمالي</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($favorite_products as $product): ?>
            <tr>
                <td><?php echo $product['name_ar']; ?></td>
                <td><span class="badge bg-primary"><?php echo $product['order_count']; ?></span></td>
                <td><?php echo $product['total_quantity']; ?></td>
                <td><?php echo formatCurrency($product['total_spent']); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<?php endif; ?>

<!-- آخر الطلبات -->
<?php if (!empty($recent_orders)): ?>
<hr>
<h6>آخر الطلبات</h6>
<div class="table-responsive">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>رقم الطلب</th>
                <th>التاريخ</th>
                <th>النوع</th>
                <th>المبلغ</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($recent_orders as $order): ?>
            <tr>
                <td>
                    <a href="../orders/view.php?id=<?php echo $order['id']; ?>" class="text-decoration-none">
                        <?php echo $order['order_number']; ?>
                    </a>
                </td>
                <td><?php echo formatDate($order['created_at'], 'd/m/Y H:i'); ?></td>
                <td><?php echo $order['order_type_name']; ?></td>
                <td><?php echo formatCurrency($order['total_amount']); ?></td>
                <td>
                    <?php
                    $status_classes = [
                        'pending' => 'warning',
                        'confirmed' => 'info',
                        'preparing' => 'primary',
                        'ready' => 'success',
                        'completed' => 'dark',
                        'cancelled' => 'danger'
                    ];
                    ?>
                    <span class="badge bg-<?php echo $status_classes[$order['status']]; ?>">
                        <?php echo $order['status_name']; ?>
                    </span>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<?php endif; ?>

<!-- أزرار الإجراءات -->
<hr>
<div class="text-center">
    <a href="../customers/edit.php?id=<?php echo $customer['id']; ?>" class="btn btn-primary">
        <i class="fas fa-edit me-2"></i>
        تعديل العميل
    </a>
    <a href="../orders/list.php?customer_id=<?php echo $customer['id']; ?>" class="btn btn-outline-secondary">
        <i class="fas fa-shopping-cart me-2"></i>
        عرض جميع الطلبات
    </a>
    <a href="../pos/index.php?customer_id=<?php echo $customer['id']; ?>" class="btn btn-outline-success">
        <i class="fas fa-plus me-2"></i>
        طلب جديد
    </a>
</div>
