<?php
/**
 * إنشاء عميل جديد
 * Create New Customer
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';
require_once '../../../models/Customer.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('customers.manage');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

$name = sanitize($_POST['name'] ?? '');
$phone = sanitize($_POST['phone'] ?? '');

if (empty($name) || empty($phone)) {
    echo json_encode(['success' => false, 'message' => 'الاسم ورقم الهاتف مطلوبان']);
    exit;
}

// التحقق من صحة رقم الهاتف
if (!isValidSaudiPhone($phone)) {
    echo json_encode(['success' => false, 'message' => 'رقم الهاتف غير صحيح']);
    exit;
}

try {
    $customer_model = new Customer();
    
    $data = [
        'name' => $name,
        'phone' => formatPhone($phone)
    ];
    
    $result = $customer_model->create($data);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'id' => $result['id'],
            'message' => $result['message']
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => $result['message']]);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
