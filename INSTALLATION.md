# دليل التثبيت والتشغيل - Installation Guide

## متطلبات النظام - System Requirements

### الخادم - Server
- **PHP**: 5.6.26 أو أحدث
- **MySQL**: 5.7 أو أحدث  
- **Apache**: 2.4 أو أحدث
- **الذاكرة**: 512 MB RAM كحد أدنى
- **التخزين**: 1 GB مساحة فارغة

### المتصفح - Browser
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 16+

## خطوات التثبيت - Installation Steps

### 1. تحضير البيئة - Environment Setup
```bash
# تأكد من تشغيل Apache و MySQL
# في XAMPP أو AppServ أو WAMP
```

### 2. رفع الملفات - Upload Files
```bash
# انسخ جميع الملفات إلى مجلد الويب
# مثال: C:\AppServ\www\restaurant
# أو: /var/www/html/restaurant
```

### 3. <PERSON><PERSON><PERSON><PERSON> قاعدة البيانات - Database Setup
```sql
-- أنشئ قاعدة بيانات جديدة
CREATE DATABASE restaurant_db CHARACTER SET utf8 COLLATE utf8_general_ci;

-- استورد ملف قاعدة البيانات
-- استخدم phpMyAdmin أو أي أداة إدارة MySQL
-- استورد ملف: database/restaurant_db.sql
```

### 4. تكوين الاتصال - Database Configuration
```php
// عدل ملف config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'restaurant_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8');
```

### 5. إعداد الصلاحيات - Permissions Setup
```bash
# تأكد من صلاحيات الكتابة على المجلدات التالية:
chmod 755 uploads/
chmod 755 logs/
chmod 755 temp/

# في Windows، تأكد من أن المجلدات قابلة للكتابة
```

### 6. إعداد الرابط الأساسي - Base URL Setup
```php
// عدل ملف config/config.php
define('SITE_URL', 'http://localhost/restaurant');
// أو
define('SITE_URL', 'http://yourdomain.com');
```

## التحقق من التثبيت - Installation Verification

### 1. اختبار الاتصال
- افتح المتصفح واذهب إلى: `http://localhost/restaurant`
- يجب أن تظهر صفحة تسجيل الدخول

### 2. تسجيل الدخول الأول
- **المستخدم**: admin
- **كلمة المرور**: admin123
- **يُنصح بتغيير كلمة المرور فوراً**

### 3. اختبار الوظائف الأساسية
- تصفح لوحة التحكم
- إضافة منتج جديد
- إنشاء طلب تجريبي

## الإعدادات الأولية - Initial Configuration

### 1. معلومات المطعم
```
الإعدادات > الإعدادات العامة
- اسم المطعم
- العنوان
- رقم الهاتف
- الرقم الضريبي
```

### 2. الإعدادات المالية
```
- نسبة الضريبة (افتراضي: 15%)
- رسوم الخدمة (افتراضي: 10%)
- العملة (افتراضي: ريال سعودي)
```

### 3. إعداد الطابعات
```
الإعدادات > طابعات المطبخ
- إضافة طابعات المطبخ
- تحديد عنوان IP لكل طابعة
```

## استكشاف الأخطاء - Troubleshooting

### خطأ في الاتصال بقاعدة البيانات
```
الحل:
1. تحقق من إعدادات config/database.php
2. تأكد من تشغيل MySQL
3. تحقق من اسم قاعدة البيانات واسم المستخدم
```

### مشاكل الصلاحيات
```
الحل:
1. تحقق من صلاحيات مجلد uploads
2. في Linux: chmod -R 755 uploads/
3. في Windows: خصائص المجلد > الأمان
```

### بطء في الأداء
```
الحل:
1. زيادة ذاكرة PHP في php.ini
2. تحسين إعدادات MySQL
3. إضافة فهارس لقاعدة البيانات
```

### مشاكل الترميز العربي
```
الحل:
1. تأكد من ترميز UTF-8 في قاعدة البيانات
2. تحقق من إعدادات PHP
3. تأكد من ترميز الملفات
```

## التحديثات - Updates

### نسخ احتياطي قبل التحديث
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u root -p restaurant_db > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz uploads/ config/
```

### تطبيق التحديث
```bash
1. رفع الملفات الجديدة
2. تشغيل سكريبت التحديث (إن وجد)
3. مسح ذاكرة التخزين المؤقت
4. اختبار النظام
```

## الأمان - Security

### تأمين النظام
```
1. تغيير كلمة مرور المدير
2. إنشاء مستخدمين بصلاحيات محدودة
3. تحديث PHP و MySQL بانتظام
4. استخدام HTTPS في الإنتاج
```

### النسخ الاحتياطي
```
1. نسخ احتياطي يومي لقاعدة البيانات
2. نسخ احتياطي أسبوعي للملفات
3. اختبار استعادة النسخ الاحتياطية
```

## الدعم الفني - Technical Support

### المشاكل الشائعة
- راجع ملف logs/error.log للأخطاء
- تحقق من إعدادات PHP
- تأكد من تحديث المتصفح

### طلب المساعدة
عند طلب المساعدة، يرجى تقديم:
- إصدار PHP و MySQL
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة
- لقطة شاشة للخطأ

---

**ملاحظة**: هذا النظام مصمم للعمل مع PHP 5.6.26 كما هو مطلوب. للحصول على أفضل أداء، يُنصح باستخدام خادم مخصص أو VPS.
