    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // إعدادات عامة لـ DataTables
        $.extend(true, $.fn.dataTable.defaults, {
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            },
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            order: [[0, 'desc']]
        });
        
        // إعدادات عامة لـ Select2
        $.fn.select2.defaults.set('theme', 'bootstrap-5');
        $.fn.select2.defaults.set('language', 'ar');
        $.fn.select2.defaults.set('dir', 'rtl');
        
        // تطبيق Select2 على جميع عناصر select
        $(document).ready(function() {
            $('.select2').select2();
        });
        
        // دالة عرض رسائل التأكيد
        function confirmAction(title, text, confirmButtonText, cancelButtonText) {
            return Swal.fire({
                title: title || 'هل أنت متأكد؟',
                text: text || 'لن تتمكن من التراجع عن هذا الإجراء!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: confirmButtonText || 'نعم، احذف!',
                cancelButtonText: cancelButtonText || 'إلغاء',
                reverseButtons: true
            });
        }
        
        // دالة عرض رسائل النجاح
        function showSuccess(title, text) {
            Swal.fire({
                title: title || 'تم بنجاح!',
                text: text || 'تم تنفيذ العملية بنجاح',
                icon: 'success',
                confirmButtonText: 'موافق'
            });
        }
        
        // دالة عرض رسائل الخطأ
        function showError(title, text) {
            Swal.fire({
                title: title || 'خطأ!',
                text: text || 'حدث خطأ أثناء تنفيذ العملية',
                icon: 'error',
                confirmButtonText: 'موافق'
            });
        }
        
        // دالة تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR',
                minimumFractionDigits: 2
            }).format(amount);
        }
        
        // دالة تنسيق التاريخ
        function formatDate(date) {
            return new Intl.DateTimeFormat('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }).format(new Date(date));
        }
        
        // دالة تنسيق الوقت
        function formatTime(time) {
            return new Intl.DateTimeFormat('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            }).format(new Date(time));
        }
        
        // دالة AJAX عامة
        function ajaxRequest(url, data, method, successCallback, errorCallback) {
            $.ajax({
                url: url,
                type: method || 'POST',
                data: data,
                dataType: 'json',
                beforeSend: function() {
                    // إظهار مؤشر التحميل
                    Swal.fire({
                        title: 'جاري التحميل...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                },
                success: function(response) {
                    Swal.close();
                    if (response.success) {
                        if (successCallback) {
                            successCallback(response);
                        } else {
                            showSuccess('تم بنجاح!', response.message);
                        }
                    } else {
                        showError('خطأ!', response.message || 'حدث خطأ غير متوقع');
                    }
                },
                error: function(xhr, status, error) {
                    Swal.close();
                    if (errorCallback) {
                        errorCallback(xhr, status, error);
                    } else {
                        showError('خطأ في الاتصال!', 'تعذر الاتصال بالخادم. يرجى المحاولة مرة أخرى.');
                    }
                }
            });
        }
        
        // دالة طباعة الفاتورة
        function printReceipt(orderId) {
            const printWindow = window.open('print_receipt.php?id=' + orderId, '_blank', 'width=300,height=600');
            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };
        }
        
        // دالة طباعة تذكرة المطبخ
        function printKitchenTicket(orderId) {
            const printWindow = window.open('print_kitchen.php?id=' + orderId, '_blank', 'width=300,height=400');
            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };
        }
        
        // تحديث تلقائي للصفحات التي تحتاج ذلك
        function autoRefresh(interval) {
            setInterval(function() {
                location.reload();
            }, interval || 30000); // 30 ثانية افتراضياً
        }
        
        // دالة حساب المجموع
        function calculateTotal(subtotal, taxRate, serviceChargeRate, discountAmount) {
            const tax = subtotal * (taxRate / 100);
            const serviceCharge = subtotal * (serviceChargeRate / 100);
            return subtotal + tax + serviceCharge - discountAmount;
        }
        
        // دالة التحقق من صحة البيانات
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;
            
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            return isValid;
        }
        
        // دالة تنظيف النموذج
        function clearForm(formId) {
            const form = document.getElementById(formId);
            if (form) {
                form.reset();
                form.querySelectorAll('.is-invalid').forEach(function(field) {
                    field.classList.remove('is-invalid');
                });
                form.querySelectorAll('.select2').forEach(function(select) {
                    $(select).val(null).trigger('change');
                });
            }
        }
        
        // دالة تحديث عداد الوقت
        function updateTimer(elementId, startTime) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            setInterval(function() {
                const now = new Date();
                const start = new Date(startTime);
                const diff = Math.floor((now - start) / 1000);
                
                const hours = Math.floor(diff / 3600);
                const minutes = Math.floor((diff % 3600) / 60);
                const seconds = diff % 60;
                
                element.textContent = 
                    String(hours).padStart(2, '0') + ':' +
                    String(minutes).padStart(2, '0') + ':' +
                    String(seconds).padStart(2, '0');
            }, 1000);
        }
        
        // دالة إرسال إشعار
        function sendNotification(title, message, type) {
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification(title, {
                    body: message,
                    icon: '/assets/images/logo.png'
                });
            }
        }
        
        // طلب إذن الإشعارات
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
        
        // معالج الأخطاء العام
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
            if (typeof showError === 'function') {
                showError('خطأ في النظام', 'حدث خطأ غير متوقع. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
            }
        });
        
        // تطبيق التوقيتات المحلية
        $(document).ready(function() {
            $('.datetime-local').each(function() {
                const utcTime = $(this).data('utc');
                if (utcTime) {
                    const localTime = new Date(utcTime).toLocaleString('ar-SA');
                    $(this).text(localTime);
                }
            });
        });
        
        // حفظ حالة النموذج تلقائياً
        function autoSaveForm(formId, interval) {
            const form = document.getElementById(formId);
            if (!form) return;
            
            setInterval(function() {
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);
                localStorage.setItem('autosave_' + formId, JSON.stringify(data));
            }, interval || 30000); // 30 ثانية
        }
        
        // استرجاع حالة النموذج المحفوظة
        function restoreForm(formId) {
            const savedData = localStorage.getItem('autosave_' + formId);
            if (savedData) {
                const data = JSON.parse(savedData);
                const form = document.getElementById(formId);
                
                Object.keys(data).forEach(function(key) {
                    const field = form.querySelector('[name="' + key + '"]');
                    if (field) {
                        field.value = data[key];
                    }
                });
            }
        }
    </script>
    
    <?php if (isset($additional_js)): ?>
        <?php echo $additional_js; ?>
    <?php endif; ?>
    
    <!-- تذييل الصفحة -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. 
                جميع الحقوق محفوظة.
                <span class="mx-2">|</span>
                <small>الإصدار 1.0</small>
            </p>
        </div>
    </footer>
</body>
</html>
