<?php
/**
 * البحث عن العملاء
 * Search Customers
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';
require_once '../../../models/Customer.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('orders.create');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

$term = $_POST['term'] ?? '';

if (strlen($term) < 2) {
    echo json_encode(['success' => false, 'message' => 'يجب إدخال حرفين على الأقل']);
    exit;
}

try {
    $customer_model = new Customer();
    $customers = $customer_model->search($term);
    
    echo json_encode([
        'success' => true,
        'customers' => $customers
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
