<?php
/**
 * نموذج المستخدمين
 * User Model
 * Restaurant Management System
 */

class User {
    private $table = 'users';
    
    // الحصول على جميع المستخدمين
    public function getAll($active_only = true) {
        $where = $active_only ? "WHERE u.is_active = 1" : "";
        
        $sql = "SELECT u.*, r.name as role_name, r.name_ar as role_name_ar 
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id 
                {$where}
                ORDER BY u.created_at DESC";
        
        return DB::select($sql);
    }
    
    // الحصول على مستخدم بالمعرف
    public function getById($id) {
        $sql = "SELECT u.*, r.name as role_name, r.name_ar as role_name_ar 
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id 
                WHERE u.id = ?";
        
        return DB::selectOne($sql, array($id));
    }
    
    // الحصول على مستخدم باسم المستخدم
    public function getByUsername($username) {
        $sql = "SELECT u.*, r.name as role_name, r.name_ar as role_name_ar 
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id 
                WHERE u.username = ?";
        
        return DB::selectOne($sql, array($username));
    }
    
    // إنشاء مستخدم جديد
    public function create($data) {
        // التحقق من عدم تكرار اسم المستخدم
        if ($this->usernameExists($data['username'])) {
            return array('success' => false, 'message' => 'اسم المستخدم موجود مسبقاً');
        }
        
        // تشفير كلمة المرور
        $data['password'] = hashPassword($data['password']);
        
        $sql = "INSERT INTO {$this->table} (username, password, full_name, email, phone, role_id) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $params = array(
            $data['username'],
            $data['password'],
            $data['full_name'],
            $data['email'],
            $data['phone'],
            $data['role_id']
        );
        
        $user_id = DB::insert($sql, $params);
        
        if ($user_id) {
            logActivity('create_user', 'users', $user_id, null, $data);
            return array('success' => true, 'id' => $user_id, 'message' => 'تم إنشاء المستخدم بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في إنشاء المستخدم');
    }
    
    // تحديث مستخدم
    public function update($id, $data) {
        $old_data = $this->getById($id);
        if (!$old_data) {
            return array('success' => false, 'message' => 'المستخدم غير موجود');
        }
        
        // التحقق من عدم تكرار اسم المستخدم
        if (isset($data['username']) && $data['username'] != $old_data['username']) {
            if ($this->usernameExists($data['username'])) {
                return array('success' => false, 'message' => 'اسم المستخدم موجود مسبقاً');
            }
        }
        
        // تشفير كلمة المرور إذا تم تغييرها
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = hashPassword($data['password']);
        } else {
            unset($data['password']);
        }
        
        $fields = array();
        $params = array();
        
        foreach ($data as $key => $value) {
            $fields[] = "{$key} = ?";
            $params[] = $value;
        }
        
        $params[] = $id;
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";
        
        if (DB::execute($sql, $params)) {
            logActivity('update_user', 'users', $id, $old_data, $data);
            return array('success' => true, 'message' => 'تم تحديث المستخدم بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في تحديث المستخدم');
    }
    
    // حذف مستخدم (إلغاء تفعيل)
    public function delete($id) {
        $user = $this->getById($id);
        if (!$user) {
            return array('success' => false, 'message' => 'المستخدم غير موجود');
        }
        
        $sql = "UPDATE {$this->table} SET is_active = 0 WHERE id = ?";
        
        if (DB::execute($sql, array($id))) {
            logActivity('delete_user', 'users', $id, $user);
            return array('success' => true, 'message' => 'تم حذف المستخدم بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في حذف المستخدم');
    }
    
    // التحقق من وجود اسم المستخدم
    public function usernameExists($username, $exclude_id = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE username = ?";
        $params = array($username);
        
        if ($exclude_id) {
            $sql .= " AND id != ?";
            $params[] = $exclude_id;
        }
        
        $result = DB::selectOne($sql, $params);
        return $result['count'] > 0;
    }
    
    // تحديث آخر تسجيل دخول
    public function updateLastLogin($id) {
        $sql = "UPDATE {$this->table} SET last_login = NOW() WHERE id = ?";
        return DB::execute($sql, array($id));
    }
    
    // تغيير كلمة المرور
    public function changePassword($id, $old_password, $new_password) {
        $user = $this->getById($id);
        if (!$user) {
            return array('success' => false, 'message' => 'المستخدم غير موجود');
        }
        
        if (!verifyPassword($old_password, $user['password'])) {
            return array('success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة');
        }
        
        $sql = "UPDATE {$this->table} SET password = ? WHERE id = ?";
        $hashed_password = hashPassword($new_password);
        
        if (DB::execute($sql, array($hashed_password, $id))) {
            logActivity('change_password', 'users', $id);
            return array('success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في تغيير كلمة المرور');
    }
    
    // الحصول على صلاحيات المستخدم
    public function getPermissions($user_id) {
        $sql = "SELECT p.name 
                FROM permissions p 
                JOIN role_permissions rp ON p.id = rp.permission_id 
                JOIN users u ON u.role_id = rp.role_id 
                WHERE u.id = ?";
        
        $permissions = DB::select($sql, array($user_id));
        return array_column($permissions, 'name');
    }
    
    // الحصول على المستخدمين حسب الدور
    public function getByRole($role_name) {
        $sql = "SELECT u.*, r.name as role_name, r.name_ar as role_name_ar 
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id 
                WHERE r.name = ? AND u.is_active = 1
                ORDER BY u.full_name";
        
        return DB::select($sql, array($role_name));
    }
    
    // إحصائيات المستخدمين
    public function getStats() {
        $sql = "SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_logins
                FROM {$this->table}";
        
        return DB::selectOne($sql);
    }
    
    // البحث في المستخدمين
    public function search($term) {
        $sql = "SELECT u.*, r.name as role_name, r.name_ar as role_name_ar 
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id 
                WHERE (u.username LIKE ? OR u.full_name LIKE ? OR u.email LIKE ?) 
                AND u.is_active = 1
                ORDER BY u.full_name";
        
        $search_term = "%{$term}%";
        return DB::select($sql, array($search_term, $search_term, $search_term));
    }
}
?>
