<?php
/**
 * إصلاح التوافق مع PHP 5.6
 * PHP 5.6 Compatibility Fix
 * Restaurant Management System
 */

echo "بدء إصلاح التوافق مع PHP 5.6...\n";

// قائمة الملفات التي تحتاج إصلاح
$files_to_fix = array(
    'views/customers/list.php',
    'views/products/add.php',
    'views/categories/list.php',
    'views/categories/ajax/get_category.php',
    'views/products/ajax/get_product_details.php',
    'views/reports/sales.php',
    'views/settings/index.php',
    'views/print/receipt.php',
    'views/tables/list.php',
    'views/tables/ajax/get_table_details.php',
    'views/tables/ajax/get_tables_status.php',
    'error.php',
    'config/init.php',
    'config/constants.php'
);

$fixes_applied = 0;

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        echo "إصلاح ملف: $file\n";
        
        $content = file_get_contents($file);
        $original_content = $content;
        
        // إصلاح Null Coalescing Operator (??)
        $content = preg_replace('/\$([a-zA-Z_][a-zA-Z0-9_]*)\s*\?\?\s*([^;]+);/', 'isset($\\1) ? $\\1 : \\2;', $content);
        $content = preg_replace('/\$_([A-Z]+)\[\'([^\']+)\'\]\s*\?\?\s*([^;,)]+)/', 'isset($_\\1[\'\\2\']) ? $_\\1[\'\\2\'] : \\3', $content);
        $content = preg_replace('/\$_([A-Z]+)\[\"([^\"]+)\"\]\s*\?\?\s*([^;,)]+)/', 'isset($_\\1["\\2"]) ? $_\\1["\\2"] : \\3', $content);
        
        // إصلاح Array Syntax الجديد []
        $content = preg_replace('/=\s*\[\s*\]/', '= array()', $content);
        $content = preg_replace('/=\s*\[([^\]]+)\]/', '= array(\\1)', $content);
        
        // إصلاح function parameters
        $content = preg_replace('/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\$[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*\[\s*\]/', 'function \\1(\\2 = array()', $content);
        
        if ($content !== $original_content) {
            file_put_contents($file, $content);
            $fixes_applied++;
            echo "  ✓ تم إصلاح الملف\n";
        } else {
            echo "  - لا يحتاج إصلاح\n";
        }
    } else {
        echo "  ✗ الملف غير موجود: $file\n";
    }
}

echo "\nتم الانتهاء من الإصلاح!\n";
echo "عدد الملفات المُصلحة: $fixes_applied\n";

// إنشاء ملف اختبار للتحقق من التوافق
echo "\nإنشاء ملف اختبار التوافق...\n";

$test_content = '<?php
/**
 * اختبار التوافق مع PHP 5.6
 * PHP 5.6 Compatibility Test
 */

echo "اختبار التوافق مع PHP 5.6...\n";
echo "إصدار PHP: " . PHP_VERSION . "\n";

// اختبار المصفوفات
$test_array = array("test1", "test2", "test3");
echo "اختبار المصفوفات: " . (count($test_array) === 3 ? "✓ نجح" : "✗ فشل") . "\n";

// اختبار isset
$test_var = "test_value";
$result = isset($test_var) ? $test_var : "default";
echo "اختبار isset: " . ($result === "test_value" ? "✓ نجح" : "✗ فشل") . "\n";

// اختبار قاعدة البيانات
try {
    require_once "config/database.php";
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS
    );
    echo "اختبار قاعدة البيانات: ✓ نجح\n";
} catch (Exception $e) {
    echo "اختبار قاعدة البيانات: ✗ فشل - " . $e->getMessage() . "\n";
}

echo "\nانتهى الاختبار.\n";
?>';

file_put_contents('test_php56_compatibility.php', $test_content);
echo "تم إنشاء ملف الاختبار: test_php56_compatibility.php\n";

echo "\nتعليمات:\n";
echo "1. قم بتشغيل: php test_php56_compatibility.php\n";
echo "2. تأكد من عدم وجود أخطاء في السجلات\n";
echo "3. اختبر الصفحات في المتصفح\n";
echo "4. في حالة وجود مشاكل، راجع ملف logs/error.log\n";

echo "\nملاحظات مهمة:\n";
echo "- تأكد من أن PHP 5.6.26 أو أحدث مثبت\n";
echo "- تأكد من تفعيل امتدادات MySQL و PDO\n";
echo "- تأكد من صلاحيات الكتابة على مجلدات uploads و logs\n";
echo "- في حالة استمرار المشاكل، تحقق من إعدادات Apache\n";

echo "\nتم الانتهاء بنجاح! ✓\n";
?>
