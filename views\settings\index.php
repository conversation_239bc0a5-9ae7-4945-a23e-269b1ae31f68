<?php
/**
 * إعدادات النظام
 * System Settings
 * Restaurant Management System
 */

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('settings.view');

$error = '';
$success = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST' && hasPermission('settings.edit')) {
    $settings = isset($_POST['settings']) ? $_POST['settings'] : array();
    
    try {
        DB::beginTransaction();
        
        foreach ($settings as $key => $value) {
            $sql = "INSERT INTO settings (setting_key, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
            DB::execute($sql, [$key, $value]);
        }
        
        DB::commit();
        $success = 'تم حفظ الإعدادات بنجاح';
        
        // تسجيل النشاط
        logActivity('update_settings', 'settings', null, null, $settings);
        
    } catch (Exception $e) {
        DB::rollback();
        $error = 'فشل في حفظ الإعدادات: ' . $e->getMessage();
    }
}

// الحصول على الإعدادات الحالية
$current_settings = [];
$settings_result = DB::select("SELECT setting_key, setting_value FROM settings");
foreach ($settings_result as $setting) {
    $current_settings[$setting['setting_key']] = $setting['setting_value'];
}

// دالة للحصول على قيمة الإعداد
function getSettingLocal($key, $default = '') {
    global $current_settings;
    return isset($current_settings[$key]) ? $current_settings[$key] : $default;
}

$page_title = 'إعدادات النظام';
include '../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../layouts/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إعدادات النظام</h1>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="row">
                    <!-- إعدادات المطعم -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-store me-2"></i>
                                    معلومات المطعم
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">اسم المطعم</label>
                                    <input type="text" class="form-control" name="settings[restaurant_name]" 
                                           value="<?php echo htmlspecialchars(getSetting('restaurant_name', 'مطعم الذواقة')); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">اسم المطعم بالإنجليزية</label>
                                    <input type="text" class="form-control" name="settings[restaurant_name_en]" 
                                           value="<?php echo htmlspecialchars(getSetting('restaurant_name_en', 'Gourmet Restaurant')); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="settings[restaurant_address]" rows="3"><?php echo htmlspecialchars(getSetting('restaurant_address')); ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <input type="text" class="form-control" name="settings[restaurant_phone]" 
                                                   value="<?php echo htmlspecialchars(getSetting('restaurant_phone')); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" name="settings[restaurant_email]" 
                                                   value="<?php echo htmlspecialchars(getSetting('restaurant_email')); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" name="settings[tax_number]" 
                                                   value="<?php echo htmlspecialchars(getSetting('tax_number')); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم السجل التجاري</label>
                                            <input type="text" class="form-control" name="settings[commercial_register]" 
                                                   value="<?php echo htmlspecialchars(getSetting('commercial_register')); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإعدادات المالية -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-calculator me-2"></i>
                                    الإعدادات المالية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نسبة الضريبة (%)</label>
                                            <input type="number" class="form-control" name="settings[tax_rate]" 
                                                   step="0.01" min="0" max="100"
                                                   value="<?php echo getSetting('tax_rate', '15'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رسوم الخدمة (%)</label>
                                            <input type="number" class="form-control" name="settings[service_charge_rate]" 
                                                   step="0.01" min="0" max="100"
                                                   value="<?php echo getSetting('service_charge_rate', '10'); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">العملة</label>
                                    <select class="form-select" name="settings[currency]">
                                        <option value="SAR" <?php echo getSetting('currency', 'SAR') == 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                                        <option value="AED" <?php echo getSetting('currency') == 'AED' ? 'selected' : ''; ?>>درهم إماراتي (AED)</option>
                                        <option value="USD" <?php echo getSetting('currency') == 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                        <option value="EUR" <?php echo getSetting('currency') == 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">رمز العملة</label>
                                    <input type="text" class="form-control" name="settings[currency_symbol]" 
                                           value="<?php echo htmlspecialchars(getSetting('currency_symbol', 'ر.س')); ?>">
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[auto_apply_tax]" 
                                           value="1" <?php echo getSetting('auto_apply_tax', '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        تطبيق الضريبة تلقائياً
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[auto_apply_service_charge]" 
                                           value="1" <?php echo getSetting('auto_apply_service_charge', '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        تطبيق رسوم الخدمة تلقائياً
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- نظام الولاء -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-star me-2"></i>
                                    نظام الولاء
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[loyalty_enabled]" 
                                           value="1" <?php echo getSetting('loyalty_enabled', '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        تفعيل نظام الولاء
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">نقاط لكل ريال</label>
                                    <input type="number" class="form-control" name="settings[loyalty_points_per_sar]" 
                                           step="0.01" min="0"
                                           value="<?php echo getSetting('loyalty_points_per_sar', '1'); ?>">
                                    <div class="form-text">عدد النقاط التي يحصل عليها العميل لكل ريال ينفقه</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">قيمة النقطة (ر.س)</label>
                                    <input type="number" class="form-control" name="settings[loyalty_point_value]" 
                                           step="0.01" min="0"
                                           value="<?php echo getSetting('loyalty_point_value', '0.01'); ?>">
                                    <div class="form-text">قيمة النقطة الواحدة بالريال عند الاستبدال</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الحد الأدنى للاستبدال</label>
                                    <input type="number" class="form-control" name="settings[loyalty_min_redeem]" 
                                           min="0"
                                           value="<?php echo getSetting('loyalty_min_redeem', '100'); ?>">
                                    <div class="form-text">أقل عدد نقاط يمكن استبدالها</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الطباعة -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-print me-2"></i>
                                    إعدادات الطباعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[auto_print_receipt]" 
                                           value="1" <?php echo getSetting('auto_print_receipt', '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        طباعة الفاتورة تلقائياً
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[auto_print_kitchen]" 
                                           value="1" <?php echo getSetting('auto_print_kitchen', '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        طباعة تذكرة المطبخ تلقائياً
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">عرض الفاتورة (مم)</label>
                                    <select class="form-select" name="settings[receipt_width]">
                                        <option value="58" <?php echo getSetting('receipt_width', '80') == '58' ? 'selected' : ''; ?>>58 مم</option>
                                        <option value="80" <?php echo getSetting('receipt_width', '80') == '80' ? 'selected' : ''; ?>>80 مم</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">رسالة في أسفل الفاتورة</label>
                                    <textarea class="form-control" name="settings[receipt_footer]" rows="3"><?php echo htmlspecialchars(getSetting('receipt_footer', 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى')); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- إعدادات النظام -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>
                                    إعدادات النظام
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">المنطقة الزمنية</label>
                                    <select class="form-select" name="settings[timezone]">
                                        <option value="Asia/Riyadh" <?php echo getSetting('timezone', 'Asia/Riyadh') == 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض</option>
                                        <option value="Asia/Dubai" <?php echo getSetting('timezone') == 'Asia/Dubai' ? 'selected' : ''; ?>>دبي</option>
                                        <option value="Asia/Kuwait" <?php echo getSetting('timezone') == 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت</option>
                                        <option value="Asia/Qatar" <?php echo getSetting('timezone') == 'Asia/Qatar' ? 'selected' : ''; ?>>قطر</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">تنسيق التاريخ</label>
                                    <select class="form-select" name="settings[date_format]">
                                        <option value="d/m/Y" <?php echo getSetting('date_format', 'd/m/Y') == 'd/m/Y' ? 'selected' : ''; ?>>يوم/شهر/سنة</option>
                                        <option value="Y-m-d" <?php echo getSetting('date_format') == 'Y-m-d' ? 'selected' : ''; ?>>سنة-شهر-يوم</option>
                                        <option value="m/d/Y" <?php echo getSetting('date_format') == 'm/d/Y' ? 'selected' : ''; ?>>شهر/يوم/سنة</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">تنسيق الوقت</label>
                                    <select class="form-select" name="settings[time_format]">
                                        <option value="H:i" <?php echo getSetting('time_format', 'H:i') == 'H:i' ? 'selected' : ''; ?>>24 ساعة</option>
                                        <option value="h:i A" <?php echo getSetting('time_format') == 'h:i A' ? 'selected' : ''; ?>>12 ساعة</option>
                                    </select>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[maintenance_mode]" 
                                           value="1" <?php echo getSetting('maintenance_mode') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        وضع الصيانة
                                    </label>
                                    <div class="form-text">سيمنع الوصول للنظام عدا المديرين</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الإشعارات -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-bell me-2"></i>
                                    إعدادات الإشعارات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[notifications_enabled]" 
                                           value="1" <?php echo getSetting('notifications_enabled', '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        تفعيل الإشعارات
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[email_notifications]" 
                                           value="1" <?php echo getSetting('email_notifications') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        إشعارات البريد الإلكتروني
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="settings[sms_notifications]" 
                                           value="1" <?php echo getSetting('sms_notifications') ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        إشعارات الرسائل النصية
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">تنبيه انخفاض المخزون</label>
                                    <input type="number" class="form-control" name="settings[low_stock_threshold]" 
                                           min="0"
                                           value="<?php echo getSetting('low_stock_threshold', '10'); ?>">
                                    <div class="form-text">إرسال تنبيه عند انخفاض المخزون عن هذا الحد</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">تنبيه انتهاء الصلاحية (أيام)</label>
                                    <input type="number" class="form-control" name="settings[expiry_alert_days]" 
                                           min="0"
                                           value="<?php echo getSetting('expiry_alert_days', '7'); ?>">
                                    <div class="form-text">إرسال تنبيه قبل انتهاء الصلاحية بهذا العدد من الأيام</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (hasPermission('settings.edit')): ?>
                <div class="text-center mb-4">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>
                        حفظ الإعدادات
                    </button>
                </div>
                <?php endif; ?>
            </form>
        </main>
    </div>
</div>

<?php
$additional_js = '
<script>
$(document).ready(function() {
    // تأكيد حفظ الإعدادات
    $("form").on("submit", function(e) {
        e.preventDefault();
        
        Swal.fire({
            title: "حفظ الإعدادات",
            text: "هل أنت متأكد من حفظ هذه الإعدادات؟",
            icon: "question",
            showCancelButton: true,
            confirmButtonText: "نعم، احفظ",
            cancelButtonText: "إلغاء"
        }).then((result) => {
            if (result.isConfirmed) {
                this.submit();
            }
        });
    });
    
    // معاينة تنسيق التاريخ والوقت
    function updateDateTimePreview() {
        const dateFormat = $("select[name=\"settings[date_format]\"]").val();
        const timeFormat = $("select[name=\"settings[time_format]\"]").val();
        
        // يمكن إضافة معاينة هنا
    }
    
    $("select[name=\"settings[date_format]\"], select[name=\"settings[time_format]\"]").on("change", updateDateTimePreview);
    updateDateTimePreview();
});
</script>
';

include '../layouts/footer.php';
?>
