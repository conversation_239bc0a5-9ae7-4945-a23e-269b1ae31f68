<?php
/**
 * نموذج العملاء
 * Customer Model
 * Restaurant Management System
 */

class Customer {
    private $table = 'customers';
    
    // الحصول على جميع العملاء
    public function getAll($limit = null) {
        $limit_clause = $limit ? "LIMIT ?" : "";
        $params = $limit ? array($limit) : array();
        
        $sql = "SELECT * FROM {$this->table} 
                ORDER BY total_spent DESC, created_at DESC 
                {$limit_clause}";
        
        return DB::select($sql, $params);
    }
    
    // الحصول على عميل بالمعرف
    public function getById($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        return DB::selectOne($sql, array($id));
    }
    
    // الحصول على عميل برقم الهاتف
    public function getByPhone($phone) {
        $sql = "SELECT * FROM {$this->table} WHERE phone = ?";
        return DB::selectOne($sql, array($phone));
    }
    
    // إنشاء عميل جديد
    public function create($data) {
        // التحقق من عدم تكرار رقم الهاتف
        if ($this->phoneExists($data['phone'])) {
            return array('success' => false, 'message' => 'رقم الهاتف موجود مسبقاً');
        }
        
        // تنسيق رقم الهاتف
        $data['phone'] = formatPhone($data['phone']);
        
        $sql = "INSERT INTO {$this->table} (name, phone, email, address, date_of_birth, notes) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $params = array(
            $data['name'],
            $data['phone'],
            $data['email'] ?? null,
            $data['address'] ?? null,
            $data['date_of_birth'] ?? null,
            $data['notes'] ?? null
        );
        
        $customer_id = DB::insert($sql, $params);
        
        if ($customer_id) {
            logActivity('create_customer', 'customers', $customer_id, null, $data);
            return array('success' => true, 'id' => $customer_id, 'message' => 'تم إنشاء العميل بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في إنشاء العميل');
    }
    
    // تحديث عميل
    public function update($id, $data) {
        $old_data = $this->getById($id);
        if (!$old_data) {
            return array('success' => false, 'message' => 'العميل غير موجود');
        }
        
        // التحقق من عدم تكرار رقم الهاتف
        if (isset($data['phone']) && $data['phone'] != $old_data['phone']) {
            if ($this->phoneExists($data['phone'])) {
                return array('success' => false, 'message' => 'رقم الهاتف موجود مسبقاً');
            }
            $data['phone'] = formatPhone($data['phone']);
        }
        
        $fields = array();
        $params = array();
        
        foreach ($data as $key => $value) {
            $fields[] = "{$key} = ?";
            $params[] = $value;
        }
        
        $params[] = $id;
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";
        
        if (DB::execute($sql, $params)) {
            logActivity('update_customer', 'customers', $id, $old_data, $data);
            return array('success' => true, 'message' => 'تم تحديث العميل بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في تحديث العميل');
    }
    
    // حذف عميل
    public function delete($id) {
        $customer = $this->getById($id);
        if (!$customer) {
            return array('success' => false, 'message' => 'العميل غير موجود');
        }
        
        // التحقق من وجود طلبات للعميل
        $orders_count = DB::selectOne("SELECT COUNT(*) as count FROM orders WHERE customer_id = ?", array($id));
        if ($orders_count['count'] > 0) {
            return array('success' => false, 'message' => 'لا يمكن حذف العميل لوجود طلبات مرتبطة به');
        }
        
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        
        if (DB::execute($sql, array($id))) {
            logActivity('delete_customer', 'customers', $id, $customer);
            return array('success' => true, 'message' => 'تم حذف العميل بنجاح');
        }
        
        return array('success' => false, 'message' => 'فشل في حذف العميل');
    }
    
    // التحقق من وجود رقم الهاتف
    public function phoneExists($phone, $exclude_id = null) {
        $phone = formatPhone($phone);
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE phone = ?";
        $params = array($phone);
        
        if ($exclude_id) {
            $sql .= " AND id != ?";
            $params[] = $exclude_id;
        }
        
        $result = DB::selectOne($sql, $params);
        return $result['count'] > 0;
    }
    
    // إضافة نقاط ولاء
    public function addLoyaltyPoints($customer_id, $points, $description, $reference_type = 'order', $reference_id = null) {
        DB::beginTransaction();
        
        try {
            // تحديث نقاط العميل
            $sql = "UPDATE {$this->table} SET loyalty_points = loyalty_points + ? WHERE id = ?";
            DB::execute($sql, array($points, $customer_id));
            
            // الحصول على الرصيد الجديد
            $customer = $this->getById($customer_id);
            $new_balance = $customer['loyalty_points'];
            
            // تسجيل المعاملة
            $sql = "INSERT INTO loyalty_points 
                    (customer_id, transaction_type, points, description, reference_type, reference_id, balance_after, created_by) 
                    VALUES (?, 'earned', ?, ?, ?, ?, ?, ?)";
            
            DB::insert($sql, array(
                $customer_id, $points, $description, $reference_type, $reference_id, 
                $new_balance, $_SESSION['user_id']
            ));
            
            DB::commit();
            return array('success' => true, 'new_balance' => $new_balance);
            
        } catch (Exception $e) {
            DB::rollback();
            return array('success' => false, 'message' => 'فشل في إضافة نقاط الولاء');
        }
    }
    
    // استخدام نقاط الولاء
    public function redeemLoyaltyPoints($customer_id, $points, $description, $reference_type = 'redemption', $reference_id = null) {
        $customer = $this->getById($customer_id);
        if (!$customer) {
            return array('success' => false, 'message' => 'العميل غير موجود');
        }
        
        if ($customer['loyalty_points'] < $points) {
            return array('success' => false, 'message' => 'نقاط الولاء غير كافية');
        }
        
        DB::beginTransaction();
        
        try {
            // تحديث نقاط العميل
            $sql = "UPDATE {$this->table} SET loyalty_points = loyalty_points - ? WHERE id = ?";
            DB::execute($sql, array($points, $customer_id));
            
            $new_balance = $customer['loyalty_points'] - $points;
            
            // تسجيل المعاملة
            $sql = "INSERT INTO loyalty_points 
                    (customer_id, transaction_type, points, description, reference_type, reference_id, balance_after, created_by) 
                    VALUES (?, 'redeemed', ?, ?, ?, ?, ?, ?)";
            
            DB::insert($sql, array(
                $customer_id, $points, $description, $reference_type, $reference_id, 
                $new_balance, $_SESSION['user_id']
            ));
            
            DB::commit();
            return array('success' => true, 'new_balance' => $new_balance);
            
        } catch (Exception $e) {
            DB::rollback();
            return array('success' => false, 'message' => 'فشل في استخدام نقاط الولاء');
        }
    }
    
    // إضافة معاملة مالية (دين أو دفع)
    public function addTransaction($customer_id, $type, $amount, $description, $reference_type = 'adjustment', $reference_id = null) {
        DB::beginTransaction();
        
        try {
            // حساب الرصيد الجديد
            $customer = $this->getById($customer_id);
            $current_balance = $this->getBalance($customer_id);
            
            if ($type == 'debit') {
                $new_balance = $current_balance + $amount;
            } else {
                $new_balance = $current_balance - $amount;
            }
            
            // تسجيل المعاملة
            $sql = "INSERT INTO customer_transactions 
                    (customer_id, transaction_type, amount, description, reference_type, reference_id, balance_after, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $transaction_id = DB::insert($sql, array(
                $customer_id, $type, $amount, $description, $reference_type, $reference_id, 
                $new_balance, $_SESSION['user_id']
            ));
            
            DB::commit();
            return array('success' => true, 'id' => $transaction_id, 'new_balance' => $new_balance);
            
        } catch (Exception $e) {
            DB::rollback();
            return array('success' => false, 'message' => 'فشل في إضافة المعاملة');
        }
    }
    
    // الحصول على رصيد العميل
    public function getBalance($customer_id) {
        $sql = "SELECT COALESCE(SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE -amount END), 0) as balance 
                FROM customer_transactions 
                WHERE customer_id = ?";
        
        $result = DB::selectOne($sql, array($customer_id));
        return $result['balance'];
    }
    
    // الحصول على معاملات العميل
    public function getTransactions($customer_id, $limit = 50) {
        $sql = "SELECT ct.*, u.full_name as created_by_name
                FROM customer_transactions ct 
                LEFT JOIN users u ON ct.created_by = u.id 
                WHERE ct.customer_id = ? 
                ORDER BY ct.created_at DESC 
                LIMIT ?";
        
        return DB::select($sql, array($customer_id, $limit));
    }
    
    // الحصول على معاملات نقاط الولاء
    public function getLoyaltyTransactions($customer_id, $limit = 50) {
        $sql = "SELECT lp.*, u.full_name as created_by_name
                FROM loyalty_points lp 
                LEFT JOIN users u ON lp.created_by = u.id 
                WHERE lp.customer_id = ? 
                ORDER BY lp.created_at DESC 
                LIMIT ?";
        
        return DB::select($sql, array($customer_id, $limit));
    }
    
    // الحصول على طلبات العميل
    public function getOrders($customer_id, $limit = 20) {
        $sql = "SELECT * FROM orders 
                WHERE customer_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        return DB::select($sql, array($customer_id, $limit));
    }
    
    // تحديث إحصائيات العميل
    public function updateStats($customer_id) {
        $sql = "UPDATE {$this->table} SET 
                    total_spent = (
                        SELECT COALESCE(SUM(total_amount), 0) 
                        FROM orders 
                        WHERE customer_id = ? AND status = 'completed'
                    ),
                    total_visits = (
                        SELECT COUNT(*) 
                        FROM orders 
                        WHERE customer_id = ? AND status = 'completed'
                    ),
                    last_visit = (
                        SELECT MAX(created_at) 
                        FROM orders 
                        WHERE customer_id = ? AND status = 'completed'
                    )
                WHERE id = ?";
        
        return DB::execute($sql, array($customer_id, $customer_id, $customer_id, $customer_id));
    }
    
    // البحث في العملاء
    public function search($term) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE name LIKE ? OR phone LIKE ? OR email LIKE ? 
                ORDER BY total_spent DESC";
        
        $search_term = "%{$term}%";
        return DB::select($sql, array($search_term, $search_term, $search_term));
    }
    
    // الحصول على أفضل العملاء
    public function getTopCustomers($limit = 10) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE total_spent > 0 
                ORDER BY total_spent DESC 
                LIMIT ?";
        
        return DB::select($sql, array($limit));
    }
    
    // إحصائيات العملاء
    public function getStats() {
        $sql = "SELECT 
                    COUNT(*) as total_customers,
                    SUM(CASE WHEN total_visits > 0 THEN 1 ELSE 0 END) as active_customers,
                    SUM(CASE WHEN is_vip = 1 THEN 1 ELSE 0 END) as vip_customers,
                    AVG(total_spent) as average_spent,
                    SUM(loyalty_points) as total_loyalty_points
                FROM {$this->table}";
        
        return DB::selectOne($sql);
    }
}
?>
