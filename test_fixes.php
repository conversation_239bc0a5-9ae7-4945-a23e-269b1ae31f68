<?php
/**
 * اختبار الإصلاحات الأخيرة
 * Test Recent Fixes
 * Restaurant Management System
 */

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "<title>اختبار الإصلاحات</title>\n";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n";
echo ".test-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n";
echo ".test-item { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }\n";
echo ".success { border-color: #28a745; background: #d4edda; color: #155724; }\n";
echo ".error { border-color: #dc3545; background: #f8d7da; color: #721c24; }\n";
echo ".warning { border-color: #ffc107; background: #fff3cd; color: #856404; }\n";
echo ".info { border-color: #17a2b8; background: #d1ecf1; color: #0c5460; }\n";
echo "h1, h2 { color: #333; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<div class='test-container'>\n";
echo "<h1>🔧 اختبار الإصلاحات الأخيرة</h1>\n";

$tests_passed = 0;
$tests_failed = 0;

function test_result($test_name, $result, $message = '') {
    global $tests_passed, $tests_failed;
    
    if ($result) {
        $tests_passed++;
        echo "<div class='test-item success'>✅ $test_name: نجح</div>\n";
    } else {
        $tests_failed++;
        echo "<div class='test-item error'>❌ $test_name: فشل" . ($message ? " - $message" : "") . "</div>\n";
    }
}

function test_info($test_name, $message) {
    echo "<div class='test-item info'>ℹ️ $test_name: $message</div>\n";
}

// اختبار تحميل config/database.php
echo "<h2>🗄️ اختبار config/database.php</h2>\n";

try {
    ob_start();
    include_once 'config/database.php';
    ob_end_clean();
    test_result("تحميل config/database.php", true);
} catch (Exception $e) {
    test_result("تحميل config/database.php", false, $e->getMessage());
}

// اختبار تحميل config/init.php
echo "<h2>⚙️ اختبار config/init.php</h2>\n";

try {
    ob_start();
    include_once 'config/init.php';
    ob_end_clean();
    test_result("تحميل config/init.php", true);
    
    // اختبار فئة DB
    test_result("فئة DB متوفرة", class_exists('DB'));
    
    // اختبار دوال DB
    $db_methods = array('select', 'selectOne', 'execute', 'insert');
    foreach ($db_methods as $method) {
        test_result("دالة DB::$method", method_exists('DB', $method));
    }
    
} catch (Exception $e) {
    test_result("تحميل config/init.php", false, $e->getMessage());
}

// اختبار تحميل config/constants.php
echo "<h2>📋 اختبار config/constants.php</h2>\n";

try {
    ob_start();
    include_once 'config/constants.php';
    ob_end_clean();
    test_result("تحميل config/constants.php", true);
    
    // اختبار الثوابت
    test_result("ALLOWED_IMAGE_TYPES", defined('ALLOWED_IMAGE_TYPES'));
    test_result("ALLOWED_DOCUMENT_TYPES", defined('ALLOWED_DOCUMENT_TYPES'));
    test_result("ALLOWED_EXTENSIONS في GLOBALS", isset($GLOBALS['ALLOWED_EXTENSIONS']));
    
} catch (Exception $e) {
    test_result("تحميل config/constants.php", false, $e->getMessage());
}

// اختبار قاعدة البيانات
echo "<h2>🗄️ اختبار قاعدة البيانات</h2>\n";

try {
    if (class_exists('DB')) {
        // اختبار الاتصال
        $result = DB::selectOne("SELECT 1 as test");
        test_result("اختبار الاتصال بقاعدة البيانات", $result !== false);
        
        // اختبار جدول المستخدمين
        $users_count = DB::selectOne("SELECT COUNT(*) as count FROM users");
        test_result("جدول المستخدمين", $users_count !== false, "عدد المستخدمين: " . ($users_count ? $users_count['count'] : '0'));
        
        // اختبار جدول الإعدادات
        $settings_count = DB::selectOne("SELECT COUNT(*) as count FROM settings");
        test_result("جدول الإعدادات", $settings_count !== false, "عدد الإعدادات: " . ($settings_count ? $settings_count['count'] : '0'));
        
    } else {
        test_result("فئة DB غير متوفرة", false);
    }
} catch (Exception $e) {
    test_result("اختبار قاعدة البيانات", false, $e->getMessage());
}

// اختبار الدوال المساعدة
echo "<h2>🛠️ اختبار الدوال المساعدة</h2>\n";

$functions_to_test = array('getSetting', 'setSetting', 'sanitize', 'formatCurrency', 'logError');
foreach ($functions_to_test as $func) {
    test_result("دالة $func", function_exists($func));
}

// اختبار الملفات الأساسية
echo "<h2>📁 اختبار الملفات الأساسية</h2>\n";

$files_to_test = array(
    'index.php',
    'login.php',
    'views/dashboard/index.php',
    'views/users/list.php',
    'views/tables/index.php'
);

foreach ($files_to_test as $file) {
    test_result("ملف $file", file_exists($file));
}

// اختبار التوافق مع PHP 5.6
echo "<h2>🔄 اختبار التوافق مع PHP 5.6</h2>\n";

$files_to_check = array(
    'config/database.php',
    'config/init.php',
    'config/constants.php'
);

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // فحص Null Coalescing Operator
        $has_null_coalescing = preg_match('/\?\?/', $content);
        test_result("$file - خالي من ??", !$has_null_coalescing);
        
        // فحص Array Syntax الجديد في define
        $has_array_in_define = preg_match('/define\s*\([^,]+,\s*array\s*\(/', $content);
        test_result("$file - خالي من array() في define", !$has_array_in_define);
    }
}

// إحصائيات النتائج
echo "<h2>📊 ملخص النتائج</h2>\n";
$total_tests = $tests_passed + $tests_failed;
$success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;

echo "<div class='test-item info'>\n";
echo "إجمالي الاختبارات: $total_tests<br>\n";
echo "نجح: $tests_passed<br>\n";
echo "فشل: $tests_failed<br>\n";
echo "معدل النجاح: $success_rate%\n";
echo "</div>\n";

if ($tests_failed === 0) {
    echo "<div class='test-item success'>\n";
    echo "<h3>🎉 ممتاز! جميع الإصلاحات نجحت</h3>\n";
    echo "النظام الآن جاهز للاستخدام بدون أخطاء.<br>\n";
    echo "<div class='mt-3'>\n";
    echo "<a href='start_system.php' class='btn btn-success me-2'>تشغيل النظام</a>\n";
    echo "<a href='login.php' class='btn btn-primary'>تسجيل الدخول</a>\n";
    echo "</div>\n";
    echo "</div>\n";
} else {
    echo "<div class='test-item error'>\n";
    echo "<h3>⚠️ يوجد مشاكل تحتاج إصلاح</h3>\n";
    echo "يرجى مراجعة الأخطاء أعلاه.<br>\n";
    echo "<div class='mt-3'>\n";
    echo "<a href='fix_all_issues.php' class='btn btn-warning me-2'>إصلاح شامل</a>\n";
    echo "<a href='test_web.php' class='btn btn-info'>اختبار مفصل</a>\n";
    echo "</div>\n";
    echo "</div>\n";
}

// معلومات إضافية
echo "<h2>💡 معلومات إضافية</h2>\n";
echo "<div class='test-item info'>\n";
echo "<strong>إصدار PHP:</strong> " . PHP_VERSION . "<br>\n";
echo "<strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "<br>\n";
echo "<strong>المنطقة الزمنية:</strong> " . date_default_timezone_get() . "<br>\n";
echo "<strong>الذاكرة المستخدمة:</strong> " . round(memory_get_usage() / 1024 / 1024, 2) . " MB<br>\n";
echo "</div>\n";

// روابط مفيدة
echo "<h2>🔗 روابط مفيدة</h2>\n";
echo "<div class='test-item info'>\n";
echo "<div class='row'>\n";
echo "<div class='col-md-6'>\n";
echo "<h5>اختبارات:</h5>\n";
echo "<ul>\n";
echo "<li><a href='test_fixes.php'>اختبار الإصلاحات</a></li>\n";
echo "<li><a href='test_web.php'>اختبار شامل</a></li>\n";
echo "<li><a href='start_system.php'>تشغيل النظام</a></li>\n";
echo "</ul>\n";
echo "</div>\n";
echo "<div class='col-md-6'>\n";
echo "<h5>النظام:</h5>\n";
echo "<ul>\n";
echo "<li><a href='login.php'>تسجيل الدخول</a></li>\n";
echo "<li><a href='views/dashboard/index.php'>لوحة التحكم</a></li>\n";
echo "<li><a href='views/tables/index.php'>خريطة الطاولات</a></li>\n";
echo "</ul>\n";
echo "</div>\n";
echo "</div>\n";
echo "</div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
