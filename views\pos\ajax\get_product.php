<?php
/**
 * الحصول على معلومات المنتج
 * Get Product Information
 * Restaurant Management System
 */

require_once '../../../config/database.php';
require_once '../../../includes/functions.php';
require_once '../../../models/Product.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('orders.create');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

$product_id = $_POST['product_id'] ?? null;

if (!$product_id) {
    echo json_encode(['success' => false, 'message' => 'معرف المنتج مطلوب']);
    exit;
}

try {
    $product_model = new Product();
    $product = $product_model->getById($product_id);
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'المنتج غير موجود']);
        exit;
    }
    
    // الحصول على الأحجام والإضافات
    $sizes = $product_model->getSizes($product_id);
    $addons = $product_model->getAddons($product_id);
    
    echo json_encode([
        'success' => true,
        'product' => $product,
        'sizes' => $sizes,
        'addons' => $addons
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
