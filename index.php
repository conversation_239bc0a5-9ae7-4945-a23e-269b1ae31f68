<?php
/**
 * الصفحة الرئيسية - لوحة التحكم
 * Main Dashboard Page
 * Restaurant Management System
 */

// تهيئة النظام
require_once 'config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// إعادة توجيه إلى لوحة التحكم
header('Location: views/dashboard/index.php');
exit;

$page_title = 'الصفحة الرئيسية';
include 'views/layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <?php include 'views/layouts/sidebar.php'; ?>
        
        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">لوحة التحكم</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-calendar-alt"></i>
                            <?php echo formatDate(date('Y-m-d')); ?>
                        </button>
                    </div>
                </div>
            </div>

            <?php showSessionMessages(); ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <?php if (hasPermission('orders.view')): ?>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        طلبات اليوم
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = CURDATE()";
                                        $result = DB::selectOne($sql);
                                        echo $result['count'];
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (hasPermission('reports.sales')): ?>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        مبيعات اليوم
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php
                                        $sql = "SELECT COALESCE(SUM(total_amount), 0) as total FROM orders 
                                               WHERE DATE(created_at) = CURDATE() AND status = 'completed'";
                                        $result = DB::selectOne($sql);
                                        echo formatCurrency($result['total']);
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        الطاولات المشغولة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM tables WHERE status = 'occupied'";
                                        $result = DB::selectOne($sql);
                                        echo $result['count'];
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chair fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (hasPermission('kitchen.view')): ?>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col me-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        طلبات المطبخ
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM kitchen_orders 
                                               WHERE status IN ('pending', 'preparing')";
                                        $result = DB::selectOne($sql);
                                        echo $result['count'];
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-utensils fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <div class="row">
                <!-- الطلبات الحديثة -->
                <?php if (hasPermission('orders.view')): ?>
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">الطلبات الحديثة</h6>
                            <a href="views/orders/list.php" class="btn btn-sm btn-primary">عرض الكل</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                            <th>الوقت</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql = "SELECT order_number, order_type, total_amount, status, created_at 
                                               FROM orders 
                                               ORDER BY created_at DESC 
                                               LIMIT 5";
                                        $orders = DB::select($sql);
                                        
                                        foreach ($orders as $order):
                                            $status_class = '';
                                            $status_text = '';
                                            switch ($order['status']) {
                                                case 'pending':
                                                    $status_class = 'warning';
                                                    $status_text = 'قيد الانتظار';
                                                    break;
                                                case 'preparing':
                                                    $status_class = 'info';
                                                    $status_text = 'قيد التحضير';
                                                    break;
                                                case 'ready':
                                                    $status_class = 'success';
                                                    $status_text = 'جاهز';
                                                    break;
                                                case 'completed':
                                                    $status_class = 'primary';
                                                    $status_text = 'مكتمل';
                                                    break;
                                                case 'cancelled':
                                                    $status_class = 'danger';
                                                    $status_text = 'ملغي';
                                                    break;
                                            }
                                            
                                            $type_text = '';
                                            switch ($order['order_type']) {
                                                case 'dine_in':
                                                    $type_text = 'طاولة';
                                                    break;
                                                case 'takeaway':
                                                    $type_text = 'سفري';
                                                    break;
                                                case 'delivery':
                                                    $type_text = 'توصيل';
                                                    break;
                                            }
                                        ?>
                                        <tr>
                                            <td><?php echo $order['order_number']; ?></td>
                                            <td><?php echo $type_text; ?></td>
                                            <td><?php echo formatCurrency($order['total_amount']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $status_class; ?>">
                                                    <?php echo $status_text; ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($order['created_at'], 'H:i'); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- حالة الطاولات -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">حالة الطاولات</h6>
                            <a href="views/tables/list.php" class="btn btn-sm btn-primary">عرض الكل</a>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php
                                $sql = "SELECT table_number, capacity, status FROM tables ORDER BY table_number";
                                $tables = DB::select($sql);
                                
                                foreach ($tables as $table):
                                    $status_class = '';
                                    $status_text = '';
                                    switch ($table['status']) {
                                        case 'available':
                                            $status_class = 'success';
                                            $status_text = 'متاحة';
                                            break;
                                        case 'occupied':
                                            $status_class = 'danger';
                                            $status_text = 'مشغولة';
                                            break;
                                        case 'reserved':
                                            $status_class = 'warning';
                                            $status_text = 'محجوزة';
                                            break;
                                        case 'maintenance':
                                            $status_class = 'secondary';
                                            $status_text = 'صيانة';
                                            break;
                                    }
                                ?>
                                <div class="col-md-4 col-sm-6 mb-2">
                                    <div class="card border-<?php echo $status_class; ?> text-center">
                                        <div class="card-body p-2">
                                            <h6 class="card-title mb-1"><?php echo $table['table_number']; ?></h6>
                                            <small class="text-muted"><?php echo $table['capacity']; ?> أشخاص</small>
                                            <br>
                                            <span class="badge bg-<?php echo $status_class; ?> mt-1">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">الإجراءات السريعة</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <?php if (hasPermission('orders.create')): ?>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <a href="views/pos/index.php" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-cash-register fa-2x mb-2"></i>
                                        <br>نقاط البيع
                                    </a>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (hasPermission('kitchen.view')): ?>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <a href="views/kitchen/display.php" class="btn btn-warning btn-lg w-100">
                                        <i class="fas fa-utensils fa-2x mb-2"></i>
                                        <br>شاشة المطبخ
                                    </a>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (hasPermission('customers.manage')): ?>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <a href="views/customers/list.php" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-users fa-2x mb-2"></i>
                                        <br>إدارة العملاء
                                    </a>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (hasPermission('reports.sales')): ?>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <a href="views/reports/index.php" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                        <br>التقارير
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'views/layouts/footer.php'; ?>
