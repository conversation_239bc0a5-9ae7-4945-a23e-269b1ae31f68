<?php
/**
 * اختبار بسيط للنظام
 * Simple System Test
 */

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "<title>اختبار بسيط</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n";
echo ".container { background: white; padding: 20px; border-radius: 10px; max-width: 800px; margin: 0 auto; }\n";
echo ".success { color: #28a745; }\n";
echo ".error { color: #dc3545; }\n";
echo ".warning { color: #ffc107; }\n";
echo ".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<div class='container'>\n";
echo "<h1>🧪 اختبار بسيط للنظام</h1>\n";

$all_good = true;

// اختبار إصدار PHP
echo "<h2>1. اختبار إصدار PHP</h2>\n";
$php_version = PHP_VERSION;
$php_ok = version_compare($php_version, '5.6.0', '>=');
echo "<p class='" . ($php_ok ? 'success' : 'error') . "'>إصدار PHP: $php_version " . ($php_ok ? '✓' : '✗') . "</p>\n";
if (!$php_ok) $all_good = false;

// اختبار تحميل config/constants.php
echo "<h2>2. اختبار config/constants.php</h2>\n";
try {
    ob_start();
    include_once 'config/constants.php';
    ob_end_clean();
    echo "<p class='success'>✓ تم تحميل config/constants.php بنجاح</p>\n";
    
    // اختبار المتغيرات العامة
    if (isset($GLOBALS['ALLOWED_IMAGE_TYPES'])) {
        echo "<p class='success'>✓ ALLOWED_IMAGE_TYPES متوفر</p>\n";
    } else {
        echo "<p class='error'>✗ ALLOWED_IMAGE_TYPES غير متوفر</p>\n";
        $all_good = false;
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ خطأ في تحميل config/constants.php: " . $e->getMessage() . "</p>\n";
    $all_good = false;
}

// اختبار تحميل config/init.php
echo "<h2>3. اختبار config/init.php</h2>\n";
try {
    ob_start();
    include_once 'config/init.php';
    ob_end_clean();
    echo "<p class='success'>✓ تم تحميل config/init.php بنجاح</p>\n";
    
    // اختبار فئة DB
    if (class_exists('DB')) {
        echo "<p class='success'>✓ فئة DB متوفرة</p>\n";
    } else {
        echo "<p class='error'>✗ فئة DB غير متوفرة</p>\n";
        $all_good = false;
    }
    
    // اختبار الدوال
    $functions = array('getSetting', 'setSetting', 'logActivity', 'sanitize', 'formatCurrency');
    foreach ($functions as $func) {
        if (function_exists($func)) {
            echo "<p class='success'>✓ دالة $func متوفرة</p>\n";
        } else {
            echo "<p class='error'>✗ دالة $func غير متوفرة</p>\n";
            $all_good = false;
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ خطأ في تحميل config/init.php: " . $e->getMessage() . "</p>\n";
    $all_good = false;
}

// اختبار قاعدة البيانات
echo "<h2>4. اختبار قاعدة البيانات</h2>\n";
try {
    if (class_exists('DB')) {
        $test = DB::selectOne("SELECT 1 as test");
        if ($test && $test['test'] == 1) {
            echo "<p class='success'>✓ قاعدة البيانات تعمل بشكل صحيح</p>\n";
            
            // اختبار جدول المستخدمين
            $users = DB::selectOne("SELECT COUNT(*) as count FROM users");
            if ($users) {
                echo "<p class='success'>✓ جدول المستخدمين: " . $users['count'] . " مستخدم</p>\n";
            }
            
        } else {
            echo "<p class='error'>✗ مشكلة في قاعدة البيانات</p>\n";
            $all_good = false;
        }
    } else {
        echo "<p class='error'>✗ فئة DB غير متوفرة</p>\n";
        $all_good = false;
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>\n";
    $all_good = false;
}

// اختبار الملفات الأساسية
echo "<h2>5. اختبار الملفات الأساسية</h2>\n";
$files = array(
    'index.php' => 'الصفحة الرئيسية',
    'login.php' => 'صفحة تسجيل الدخول',
    'views/dashboard/index.php' => 'لوحة التحكم',
    'views/users/list.php' => 'قائمة المستخدمين',
    'views/tables/index.php' => 'خريطة الطاولات'
);

foreach ($files as $file => $name) {
    if (file_exists($file)) {
        echo "<p class='success'>✓ $name موجود</p>\n";
    } else {
        echo "<p class='error'>✗ $name غير موجود</p>\n";
        $all_good = false;
    }
}

// النتيجة النهائية
echo "<h2>📊 النتيجة النهائية</h2>\n";

if ($all_good) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border: 1px solid #c3e6cb;'>\n";
    echo "<h3 style='color: #155724;'>🎉 ممتاز! النظام جاهز للاستخدام</h3>\n";
    echo "<p>جميع الاختبارات نجحت والنظام يعمل بشكل صحيح.</p>\n";
    echo "<div>\n";
    echo "<a href='login.php' class='btn'>تسجيل الدخول</a>\n";
    echo "<a href='views/dashboard/index.php' class='btn'>لوحة التحكم</a>\n";
    echo "<a href='views/tables/index.php' class='btn'>خريطة الطاولات</a>\n";
    echo "</div>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; border: 1px solid #f5c6cb;'>\n";
    echo "<h3 style='color: #721c24;'>⚠️ يوجد مشاكل تحتاج إصلاح</h3>\n";
    echo "<p>يرجى مراجعة الأخطاء أعلاه وتشغيل الإصلاح الشامل.</p>\n";
    echo "<div>\n";
    echo "<a href='final_fix.php' class='btn' style='background: #dc3545;'>إصلاح شامل</a>\n";
    echo "<a href='test_simple.php' class='btn' style='background: #6c757d;'>إعادة الاختبار</a>\n";
    echo "</div>\n";
    echo "</div>\n";
}

// معلومات إضافية
echo "<h2>💡 معلومات إضافية</h2>\n";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 10px;'>\n";
echo "<p><strong>إصدار PHP:</strong> " . PHP_VERSION . "</p>\n";
echo "<p><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
echo "<p><strong>بيانات تسجيل الدخول:</strong></p>\n";
echo "<ul>\n";
echo "<li>اسم المستخدم: <strong>admin</strong></li>\n";
echo "<li>كلمة المرور: <strong>admin123</strong></li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
